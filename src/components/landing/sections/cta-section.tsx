"use client";

import React, { useState } from "react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Mail, CheckCircle, Sparkles } from "lucide-react";

export const CTASection: React.FC = () => {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Here you would typically send the email to your backend
    console.log("Email submitted:", email);
    setIsSubmitted(true);

    // Reset after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setEmail("");
    }, 3000);
  };

  const benefits = [
    "Setup dalam 5 menit",
    "Gratis 30 hari tanpa kartu kredit",
    "Dukungan 24/7 dari tim ahli",
    "Import data keuangan gratis",
  ];

  return (
    <section
      id="contact"
      className="py-20 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10" />

      {/* Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/50 via-transparent to-purple-600/50" />

      <div className="relative container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-white/10 border border-white/20 text-white text-sm font-medium mb-6">
            <Sparkles className="w-4 h-4 mr-2" />
            Bergabung dengan 1,000+ Bisnis Sukses
          </div>

          {/* Main Headline */}
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            Siap Kelola Keuangan Lebih Baik?
          </h2>

          <p className="text-xl md:text-2xl text-blue-100 mb-8 max-w-3xl mx-auto leading-relaxed">
            Bergabunglah dengan ribuan bisnis yang telah mengelola keuangan
            lebih baik dengan KivaPOS
          </p>

          {/* Benefits Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="flex items-center justify-center text-white/90 text-sm md:text-base"
              >
                <CheckCircle className="w-5 h-5 text-green-400 mr-2 flex-shrink-0" />
                <span>{benefit}</span>
              </div>
            ))}
          </div>

          {/* CTA Options */}
          <div className="max-w-4xl mx-auto">
            {/* Primary CTA */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <Button
                asChild
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-4 text-lg font-semibold shadow-lg"
              >
                <Link href="/register" className="flex items-center">
                  Mulai Gratis Sekarang
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>

              <Button
                asChild
                variant="outline"
                size="lg"
                className="bg-white/10 backdrop-blur-md border-white/20 text-white hover:text-white hover:bg-white/20 px-8 py-4 text-lg transition-all duration-200"
              >
                <Link href="/contact">Jadwalkan Demo</Link>
              </Button>
            </div>

            {/* Email Signup Alternative */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">
                Atau dapatkan demo personal gratis
              </h3>
              <p className="text-blue-100 mb-6">
                Masukkan email Anda dan tim kami akan menghubungi untuk demo
                personal
              </p>

              {!isSubmitted ? (
                <form onSubmit={handleEmailSubmit} className="max-w-md mx-auto">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="flex-1">
                      <label htmlFor="cta-email" className="sr-only">
                        Email Address
                      </label>
                      <input
                        id="cta-email"
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        placeholder="Masukkan email Anda"
                        required
                        className="w-full px-4 py-3 rounded-lg border border-white/20 bg-white/10 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                      />
                    </div>
                    <Button
                      type="submit"
                      className="bg-white text-blue-600 hover:bg-gray-100 px-6 py-3 font-semibold whitespace-nowrap"
                    >
                      <Mail className="w-4 h-4 mr-2" />
                      Kirim
                    </Button>
                  </div>
                </form>
              ) : (
                <div className="text-center">
                  <div className="inline-flex items-center px-6 py-3 rounded-lg bg-green-500/20 border border-green-400/30 text-green-100">
                    <CheckCircle className="w-5 h-5 mr-2" />
                    Terima kasih! Tim kami akan segera menghubungi Anda.
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 pt-8 border-t border-white/20">
            <p className="text-blue-100 text-sm mb-4">
              Dipercaya oleh bisnis dari berbagai industri
            </p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-70">
              <div className="text-white font-semibold text-sm">Retail</div>
              <div className="text-white font-semibold text-sm">F&B</div>
              <div className="text-white font-semibold text-sm">Fashion</div>
              <div className="text-white font-semibold text-sm">
                Electronics
              </div>
              <div className="text-white font-semibold text-sm">Services</div>
            </div>
          </div>
        </div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-white/5 rounded-full blur-xl" />
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-purple-500/20 rounded-full blur-2xl" />
      <div className="absolute top-1/2 left-0 w-40 h-40 bg-blue-400/10 rounded-full blur-3xl" />
    </section>
  );
};
