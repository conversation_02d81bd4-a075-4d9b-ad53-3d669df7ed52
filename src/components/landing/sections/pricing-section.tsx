"use client";

import React, { useState } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Check, X, Star, ArrowRight, HelpCircle } from "lucide-react";

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  limitations: string[];
  popular?: boolean;
  buttonText: string;
  buttonVariant: "default" | "outline";
}

interface FAQ {
  question: string;
  answer: string;
}

export const PricingSection: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const plans: PricingPlan[] = [
    {
      id: "BASIC",
      name: "Paket Dasar",
      price: 99000,
      period: "per bulan",
      description:
        "Untuk bisnis kecil yang membutuhkan manajemen keuangan dasar",
      features: [
        "Maksimal 200 Produk",
        "Maksimal 1.000 Transaksi/bulan",
        "Maksimal 100 Kontak Supplier & Pelanggan",
        "Manajemen Pembelian & Penjualan",
        "Laporan <PERSON>uang<PERSON>",
        "Backup Manual Bulanan",
        "Export Laporan Excel",
      ],
      limitations: ["Tidak ada notifikasi email", "Tidak ada backup otomatis"],
      buttonText: "Mulai Gratis 30 Hari",
      buttonVariant: "outline",
    },
    {
      id: "PRO",
      name: "Paket Pro",
      price: 199000,
      period: "per bulan",
      description:
        "Untuk bisnis berkembang dengan kebutuhan keuangan lebih kompleks",
      features: [
        "Maksimal 1.000 Produk",
        "Maksimal 5.000 Transaksi/bulan",
        "Maksimal 500 Kontak Supplier & Pelanggan",
        "Manajemen Pembelian & Penjualan",
        "Notifikasi Website & Email",
        "Backup Otomatis Mingguan",
        "Analisis Keuangan Lanjutan",
        "Laporan Keuangan Komprehensif",
        "Priority Support",
        "Semua Fitur Dasar",
      ],
      limitations: ["Tidak ada dukungan developer"],
      popular: true,
      buttonText: "Pilih Pro",
      buttonVariant: "default",
    },
    {
      id: "ENTERPRISE",
      name: "Paket Enterprise",
      price: 399000,
      period: "per bulan",
      description:
        "Untuk bisnis besar dengan kebutuhan manajemen keuangan enterprise",
      features: [
        "Unlimited Produk",
        "Unlimited Transaksi",
        "Unlimited Pengguna",
        "Unlimited Kontak",
        "Manajemen Pembelian & Penjualan",
        "Notifikasi Website & Email",
        "Backup Otomatis Harian",
        "Analisis Keuangan Lanjutan",
        "Laporan Keuangan Komprehensif",
        "Priority Support",
        "Developer Support",
        "Semua Fitur Pro",
      ],
      limitations: [],
      buttonText: "Hubungi Sales",
      buttonVariant: "outline",
    },
  ];

  const faqs: FAQ[] = [
    {
      question: "Apakah ada biaya setup atau biaya tersembunyi?",
      answer:
        "Tidak ada biaya setup atau biaya tersembunyi. Anda hanya membayar biaya langganan bulanan sesuai paket yang dipilih.",
    },
    {
      question: "Bisakah saya upgrade atau downgrade paket kapan saja?",
      answer:
        "Ya, Anda dapat mengubah paket kapan saja. Perubahan akan berlaku pada periode billing berikutnya.",
    },
    {
      question: "Bagaimana cara pembayaran dan apakah aman?",
      answer:
        "Pembayaran dilakukan melalui gateway yang aman dengan berbagai metode pembayaran. Semua transaksi dienkripsi dan aman.",
    },
    {
      question: "Apakah data saya aman dan ter-backup?",
      answer:
        "Ya, semua data Anda dienkripsi dan di-backup secara otomatis. Kami menggunakan standar keamanan internasional untuk melindungi data Anda.",
    },
    {
      question: "Bisakah saya cancel langganan kapan saja?",
      answer:
        "Ya, Anda dapat membatalkan langganan kapan saja. Akses akan tetap aktif hingga akhir periode yang sudah dibayar.",
    },
  ];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("id-ID", {
      style: "currency",
      currency: "IDR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-green-100 text-green-800 text-sm font-medium mb-6">
            Harga Transparan
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Pilih Paket yang Sesuai untuk Bisnis Anda
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Mulai gratis 30 hari, tidak perlu kartu kredit. Upgrade kapan saja
            sesuai kebutuhan bisnis Anda.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan) => (
            <div
              key={plan.id}
              className={`relative rounded-2xl border-2 p-8 ${
                plan.popular
                  ? "border-blue-500 shadow-xl scale-105"
                  : "border-gray-200 shadow-lg"
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                    <Star className="w-4 h-4 mr-1" />
                    Paling Populer
                  </div>
                </div>
              )}

              {/* Plan Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  {plan.name}
                </h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>
                <div className="mb-4">
                  <span className="text-4xl font-bold text-gray-900">
                    {formatPrice(plan.price)}
                  </span>
                  <span className="text-gray-600 ml-2">{plan.period}</span>
                </div>
              </div>

              {/* Features */}
              <div className="mb-8">
                <h4 className="font-semibold text-gray-900 mb-4">
                  Yang Anda Dapatkan:
                </h4>
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                {plan.limitations.length > 0 && (
                  <div className="mt-6">
                    <h4 className="font-semibold text-gray-900 mb-4">
                      Keterbatasan:
                    </h4>
                    <ul className="space-y-3">
                      {plan.limitations.map((limitation, index) => (
                        <li key={index} className="flex items-start">
                          <X className="w-5 h-5 text-red-500 mr-3 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-700">{limitation}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>

              {/* CTA Button */}
              <Button
                asChild
                variant={plan.buttonVariant}
                size="lg"
                className={`w-full ${
                  plan.popular ? "bg-blue-600 hover:bg-blue-700 text-white" : ""
                }`}
              >
                <Link
                  href="/register"
                  className="flex items-center justify-center"
                >
                  {plan.buttonText}
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Pertanyaan yang Sering Diajukan
            </h3>
            <p className="text-lg text-gray-600">
              Temukan jawaban untuk pertanyaan umum tentang pricing dan layanan
              kami
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={index}
                className="border border-gray-200 rounded-lg overflow-hidden"
              >
                <button
                  className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors cursor-pointer"
                  onClick={() => setOpenFAQ(openFAQ === index ? null : index)}
                >
                  <span className="font-semibold text-gray-900">
                    {faq.question}
                  </span>
                  <HelpCircle
                    className={`w-5 h-5 text-gray-500 transform transition-transform ${
                      openFAQ === index ? "rotate-180" : ""
                    }`}
                  />
                </button>
                {openFAQ === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-700 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
