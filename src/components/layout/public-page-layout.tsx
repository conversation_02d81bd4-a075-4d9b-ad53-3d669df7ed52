"use client";

import React from "react";
import { LandingNavbar } from "@/components/landing/components/landing-navbar";
import { FooterSection } from "@/components/landing/sections/footer-section";

interface PublicPageLayoutProps {
  children: React.ReactNode;
  title?: string;
  description?: string;
}

export const PublicPageLayout: React.FC<PublicPageLayoutProps> = ({
  children,
  title,
  description,
}) => {
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      <LandingNavbar />

      {/* Page Header */}
      {(title || description) && (
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 pt-32">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto text-center">
              {title && (
                <h1 className="text-4xl md:text-5xl font-bold mb-4">{title}</h1>
              )}
              {description && (
                <p className="text-xl text-blue-100 max-w-2xl mx-auto">
                  {description}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="flex-1">{children}</main>

      <FooterSection />
    </div>
  );
};
