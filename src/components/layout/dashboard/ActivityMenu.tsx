"use client";

import React, { useState, useEffect } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { Clock, ShoppingCart, Package, User, TrendingUp } from "lucide-react";
import { getRecentActivities } from "@/actions/activity/activity";
import { ActivityItem } from "@/actions/activity/activity";

const ActivityMenu = () => {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchActivities = async () => {
      setLoading(true);
      try {
        const result = await getRecentActivities(5);
        if (result.success && result.data) {
          setActivities(result.data);
          setError(null);
        } else {
          setError(result.error || "Gagal mengambil aktivitas");
        }
      } catch (err) {
        setError("Terjadi kesalahan saat mengambil aktivitas");
        console.error(err);
      } finally {
        setLoading(false);
      }
    };

    fetchActivities();
  }, []);

  // Function to get icon and styling based on activity type
  const getActivityStyle = (type: string) => {
    switch (type) {
      case "sale":
        return {
          icon: TrendingUp,
          badgeClass:
            "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white",
          bgClass:
            "bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800/50",
          textColor: "text-emerald-700 dark:text-emerald-300",
        };
      case "purchase":
        return {
          icon: ShoppingCart,
          badgeClass: "bg-gradient-to-r from-blue-500 to-blue-600 text-white",
          bgClass:
            "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800/50",
          textColor: "text-blue-700 dark:text-blue-300",
        };
      case "product":
        return {
          icon: Package,
          badgeClass:
            "bg-gradient-to-r from-purple-500 to-purple-600 text-white",
          bgClass:
            "bg-purple-50 dark:bg-purple-950/30 border-purple-200 dark:border-purple-800/50",
          textColor: "text-purple-700 dark:text-purple-300",
        };
      case "login":
        return {
          icon: User,
          badgeClass: "bg-gradient-to-r from-amber-500 to-amber-600 text-white",
          bgClass:
            "bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50",
          textColor: "text-amber-700 dark:text-amber-300",
        };
      default:
        return {
          icon: Clock,
          badgeClass: "bg-gradient-to-r from-gray-500 to-gray-600 text-white",
          bgClass:
            "bg-gray-50 dark:bg-gray-950/30 border-gray-200 dark:border-gray-800/50",
          textColor: "text-gray-700 dark:text-gray-300",
        };
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8 md:h-11 md:w-11 rounded-lg cursor-pointer hover:bg-accent/80 transition-all duration-200 hover:scale-105"
          aria-label="Aktivitas Terbaru"
        >
          <Clock className="!h-4 !w-4 md:!h-6 md:!w-6 transition-transform duration-200" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-96 max-w-[calc(100vw-2rem)] shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm"
      >
        <div className="px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
          <DropdownMenuLabel className="text-base font-semibold text-gray-900 dark:text-gray-100">
            Aktivitas Terbaru
          </DropdownMenuLabel>
        </div>
        <DropdownMenuSeparator />

        {loading ? (
          <div className="p-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center py-8 px-4">
            <Clock className="h-12 w-12 text-red-300 dark:text-red-600 mb-3" />
            <p className="text-sm text-red-500 dark:text-red-400 text-center font-medium">
              {error}
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 text-center mt-1">
              Coba muat ulang halaman
            </p>
          </div>
        ) : activities.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 px-4">
            <Clock className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
              Tidak ada aktivitas terbaru
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 text-center mt-1">
              Aktivitas akan muncul di sini
            </p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto">
            {activities.map((activity) => {
              const style = getActivityStyle(activity.type);
              const IconComponent = style.icon;

              return (
                <DropdownMenuItem
                  key={activity.id}
                  className="flex flex-col items-start p-4 cursor-default transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50"
                >
                  <div className="flex items-start w-full space-x-3">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-lg ${style.badgeClass} shadow-sm`}
                    >
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate">
                        {activity.description}
                      </p>
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mt-2 space-y-1 sm:space-y-0">
                        <p className="text-xs text-gray-600 dark:text-gray-300">
                          oleh{" "}
                          <span className="font-semibold text-gray-800 dark:text-gray-200">
                            {activity.performedBy}
                          </span>
                          {activity.isEmployee && (
                            <span className="ml-1 px-1.5 py-0.5 text-[10px] font-medium bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 rounded-full">
                              Karyawan
                            </span>
                          )}
                        </p>
                        <p className="text-xs text-gray-400 dark:text-gray-500 font-medium">
                          {activity.timestamp}
                        </p>
                      </div>
                    </div>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
        )}

        <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
        <div className="p-2">
          <Link href="/dashboard/activity" passHref>
            <DropdownMenuItem className="cursor-pointer justify-center py-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950 dark:hover:to-indigo-950 transition-all duration-200 rounded-lg">
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                Lihat Semua Aktivitas
              </span>
            </DropdownMenuItem>
          </Link>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ActivityMenu;
