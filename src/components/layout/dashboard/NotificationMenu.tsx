"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import Link from "next/link";
import { toast } from "sonner";
import { Bell, Info, AlertTriangle, CheckCircle, XCircle } from "lucide-react";
import {
  getNotifications,
  getUnreadNotificationCount,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  NotificationItem,
} from "@/actions/notifications/notifications";

const NotificationMenu = () => {
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [unreadCount, setUnreadCount] = useState(0);
  // DISABLED: Polling state removed to prevent navigation interference
  // const [isPolling, setIsPolling] = useState(false);

  const fetchNotifications = useCallback(
    async (showLoading: boolean = true) => {
      // Check if there are any ongoing delete operations by looking for loading states in the DOM
      const hasActiveDeleteOperations =
        document.querySelector('[data-deleting="true"]') !== null;

      // Also check for any open alert dialogs (confirmation dialogs)
      const hasOpenDialogs =
        document.querySelector('[role="alertdialog"]') !== null;

      if ((hasActiveDeleteOperations || hasOpenDialogs) && !showLoading) {
        console.log(
          "[NotificationMenu] Skipping polling due to active operations or open dialogs"
        );
        return;
      }

      if (showLoading) {
        setLoading(true);
      }
      // DISABLED: Polling functionality removed to prevent navigation interference
      // else {
      //   setIsPolling(true);
      //   console.log("[NotificationMenu] Polling for new notifications...");
      // }
      try {
        // Fetch both notifications and total unread count
        const [notificationsResult, unreadCountResult] = await Promise.all([
          getNotifications(5),
          getUnreadNotificationCount(),
        ]);

        if (notificationsResult.success && notificationsResult.data) {
          const newNotifications = notificationsResult.data;

          // Use the actual total unread count from the database
          const newUnreadCount =
            unreadCountResult.success && unreadCountResult.count !== undefined
              ? unreadCountResult.count
              : newNotifications.filter((n: NotificationItem) => !n.isRead)
                  .length;

          // Check if there are new notifications since last fetch
          const hasNewNotifications = newUnreadCount > unreadCount;

          setNotifications(newNotifications);
          setUnreadCount(newUnreadCount);

          // Show toast for new notifications (only if not initial load)
          if (hasNewNotifications && !showLoading && notifications.length > 0) {
            const newNotificationCount = newUnreadCount - unreadCount;
            if (newNotificationCount > 0) {
              console.log(
                `[NotificationMenu] ${newNotificationCount} new notifications detected`
              );
              toast.info(`${newNotificationCount} notifikasi baru diterima`, {
                duration: 3000,
              });
            }
          }
        } else {
          console.error(
            "Failed to fetch notifications:",
            notificationsResult.error
          );
        }
      } catch (error) {
        console.error("Error fetching notifications:", error);
      } finally {
        if (showLoading) {
          setLoading(false);
        }
        // DISABLED: Polling state management removed
        // else {
        //   setIsPolling(false);
        // }
      }
    },
    [] // FIXED: Removed dependencies that were causing infinite loop
  );

  // Initial fetch - FIXED: Removed dependency to prevent infinite loop
  useEffect(() => {
    fetchNotifications(true);
  }, []); // Empty dependency array - only run once on mount

  // DISABLED: Set up periodic polling for real-time updates
  // This was causing POST requests during navigation - temporarily disabled
  // useEffect(() => {
  //   const pollInterval = setInterval(() => {
  //     // Only poll if user is not actively interacting with the page and not navigating
  //     if (
  //       document.visibilityState === "visible" &&
  //       !document.hidden &&
  //       !document.querySelector('[data-loading="true"]') // Don't poll during navigation
  //     ) {
  //       fetchNotifications(false); // Don't show loading spinner for background updates
  //     }
  //   }, 120000); // Poll every 2 minutes (further reduced to minimize interference)

  //   return () => clearInterval(pollInterval);
  // }, [fetchNotifications]); // Include fetchNotifications in dependency array

  // DISABLED: Also poll when window gains focus (user returns to tab) - with debouncing
  // This was causing POST requests during navigation - temporarily disabled
  // useEffect(() => {
  //   let focusTimeout: NodeJS.Timeout;

  //   const handleFocus = () => {
  //     // Debounce focus events to avoid excessive polling
  //     clearTimeout(focusTimeout);
  //     focusTimeout = setTimeout(() => {
  //       fetchNotifications(false);
  //     }, 1000); // Wait 1 second after focus before polling
  //   };

  //   window.addEventListener("focus", handleFocus);
  //   return () => {
  //     window.removeEventListener("focus", handleFocus);
  //     clearTimeout(focusTimeout);
  //   };
  // }, [fetchNotifications]);

  const handleMarkAsRead = async (id: string) => {
    try {
      // Call the server action to mark the notification as read
      const result = await markNotificationAsRead(id);
      if (result.success) {
        // Update local state to mark notification as read
        setNotifications((prev) =>
          prev.map((notification) =>
            notification.id === id
              ? { ...notification, isRead: true }
              : notification
          )
        );
        setUnreadCount((prev) => Math.max(0, prev - 1));
      } else {
        toast.error(
          result.error || "Gagal menandai notifikasi sebagai telah dibaca"
        );
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat menandai notifikasi");
      console.error(err);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      // Call the server action to mark all notifications as read
      const result = await markAllNotificationsAsRead();
      if (result.success) {
        // Update local state to mark all notifications as read
        setNotifications((prev) =>
          prev.map((notification) => ({ ...notification, isRead: true }))
        );
        setUnreadCount(0);
        toast.success("Semua notifikasi telah ditandai sebagai dibaca");
        // Don't immediately refresh to avoid interfering with other operations
        // The next polling cycle will pick up any changes
      } else {
        toast.error(
          result.error || "Gagal menandai semua notifikasi sebagai telah dibaca"
        );
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat menandai semua notifikasi");
      console.error(err);
    }
  };

  // Function to get icon and styling based on notification type
  const getNotificationStyle = (type: string) => {
    switch (type) {
      case "info":
        return {
          icon: Info,
          badgeClass: "bg-gradient-to-r from-blue-500 to-blue-600 text-white",
          bgClass:
            "bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-800/50",
          dotClass: "bg-blue-500",
        };
      case "warning":
        return {
          icon: AlertTriangle,
          badgeClass: "bg-gradient-to-r from-amber-500 to-amber-600 text-white",
          bgClass:
            "bg-amber-50 dark:bg-amber-950/30 border-amber-200 dark:border-amber-800/50",
          dotClass: "bg-amber-500",
        };
      case "success":
        return {
          icon: CheckCircle,
          badgeClass:
            "bg-gradient-to-r from-emerald-500 to-emerald-600 text-white",
          bgClass:
            "bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800/50",
          dotClass: "bg-emerald-500",
        };
      case "error":
        return {
          icon: XCircle,
          badgeClass: "bg-gradient-to-r from-red-500 to-red-600 text-white",
          bgClass:
            "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800/50",
          dotClass: "bg-red-500",
        };
      default:
        return {
          icon: Info,
          badgeClass: "bg-gradient-to-r from-gray-500 to-gray-600 text-white",
          bgClass:
            "bg-gray-50 dark:bg-gray-950/30 border-gray-200 dark:border-gray-800/50",
          dotClass: "bg-gray-500",
        };
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-8 w-8 md:h-11 md:w-11 rounded-lg cursor-pointer hover:bg-accent/80 transition-all duration-200 hover:scale-105"
          aria-label="Notifikasi"
        >
          <Bell className="!h-4 !w-4 md:!h-6 md:!w-6 transition-transform duration-200" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-5 w-5 rounded-full bg-gradient-to-r from-red-500 to-red-600 text-[10px] font-semibold text-white flex items-center justify-center shadow-lg">
              {unreadCount > 99 ? "99+" : unreadCount}
            </span>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-96 max-w-[calc(100vw-2rem)] shadow-xl border-0 bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm"
      >
        <div className="flex justify-between items-center px-4 py-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700 border-b border-gray-200 dark:border-gray-600">
          <DropdownMenuLabel className="text-base font-semibold text-gray-900 dark:text-gray-100">
            Notifikasi
            {unreadCount > 0 && (
              <span className="ml-2 px-2 py-1 text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full">
                {unreadCount} baru
              </span>
            )}
          </DropdownMenuLabel>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="text-xs h-8 px-3 hover:bg-white/80 dark:hover:bg-gray-800/80 transition-colors duration-200"
              onClick={handleMarkAllAsRead}
            >
              Tandai Semua Dibaca
            </Button>
          )}
        </div>
        <DropdownMenuSeparator />

        {loading ? (
          <div className="p-4 space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-8 w-8 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
              </div>
            ))}
          </div>
        ) : notifications.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 px-4">
            <Bell className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-3" />
            <p className="text-sm text-gray-500 dark:text-gray-400 text-center">
              Tidak ada notifikasi
            </p>
            <p className="text-xs text-gray-400 dark:text-gray-500 text-center mt-1">
              Notifikasi baru akan muncul di sini
            </p>
          </div>
        ) : (
          <div className="max-h-96 overflow-y-auto p-2 space-y-2">
            {notifications.map((notification) => {
              const style = getNotificationStyle(notification.type);
              const IconComponent = style.icon;

              return (
                <DropdownMenuItem
                  key={notification.id}
                  className={`flex flex-col items-start p-4 cursor-pointer transition-all duration-200 hover:bg-gray-50 dark:hover:bg-gray-800/50 border-l-4 rounded-lg ${
                    !notification.isRead
                      ? `${style.bgClass} border-l-blue-500`
                      : "border-l-transparent hover:border-l-gray-300 dark:hover:border-l-gray-600"
                  }`}
                  onClick={() =>
                    !notification.isRead && handleMarkAsRead(notification.id)
                  }
                >
                  <div className="flex items-start w-full space-x-3">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-lg ${style.badgeClass} shadow-sm`}
                    >
                      <IconComponent className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <p className="text-sm font-semibold text-gray-900 dark:text-gray-100 truncate pr-2">
                          {notification.title}
                        </p>
                        {!notification.isRead && (
                          <div
                            className={`h-2 w-2 rounded-full ${style.dotClass} flex-shrink-0 mt-1`}
                          ></div>
                        )}
                      </div>
                      <p className="text-xs text-gray-600 dark:text-gray-300 mt-1 line-clamp-2 leading-relaxed">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 dark:text-gray-500 mt-2 font-medium">
                        {notification.timestamp}
                      </p>
                    </div>
                  </div>
                </DropdownMenuItem>
              );
            })}
          </div>
        )}

        <DropdownMenuSeparator className="bg-gray-200 dark:bg-gray-700" />
        <div className="p-2">
          <Link href="/dashboard/notifications" passHref>
            <DropdownMenuItem className="cursor-pointer justify-center py-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-blue-950 dark:hover:to-indigo-950 transition-all duration-200 rounded-lg">
              <span className="text-sm font-semibold text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">
                Lihat Semua Notifikasi
              </span>
            </DropdownMenuItem>
          </Link>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationMenu;
