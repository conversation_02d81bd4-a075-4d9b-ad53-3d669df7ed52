"use client";

import React, { useEffect, useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";

interface SidebarLinkWrapperProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
}

/**
 * A wrapper component for sidebar links that checks for unsaved changes
 * before navigating away from the current page.
 */
const SidebarLinkWrapper: React.FC<SidebarLinkWrapperProps> = ({
  href,
  children,
  className,
  onClick,
}) => {
  // We need to detect if we're on a page that might have unsaved changes
  const pathname = usePathname();
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  // Check if we're on a page that might have unsaved changes
  useEffect(() => {
    // These are pages that might have forms with unsaved changes
    const pagesWithForms = [
      "/dashboard/products/new",
      "/dashboard/products/edit",
      "/dashboard/suppliers/new",
      "/dashboard/suppliers/edit",
      "/dashboard/customers/new",
      "/dashboard/customers/edit",
      "/dashboard/sales/new",
      "/dashboard/sales/edit",
      "/dashboard/purchases/new",
      "/dashboard/purchases/edit",
      "/dashboard/services/new",
      "/dashboard/services/edit",
    ];
    
    // Check if current pathname includes any of these paths
    const mightHaveUnsavedChanges = pagesWithForms.some(page => 
      pathname.includes(page) || 
      (page.includes('/edit') && pathname.match(/\/edit\/[^\/]+$/))
    );
    
    setHasUnsavedChanges(mightHaveUnsavedChanges);
  }, [pathname]);
  
  // Use our unsaved changes warning hook
  const { handleNavigation } = useUnsavedChangesWarning(hasUnsavedChanges);
  
  // Handle click with our custom navigation handler
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) onClick();
    
    if (hasUnsavedChanges) {
      e.preventDefault();
      handleNavigation(href);
    }
    // If no unsaved changes, let the link work normally
  };
  
  return (
    <Link href={href} className={className} onClick={handleClick}>
      {children}
    </Link>
  );
};

export default SidebarLinkWrapper;
