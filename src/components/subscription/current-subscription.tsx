"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, CreditCard, Clock } from "lucide-react";
import { SubscriptionPlan } from "@prisma/client";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";

interface CurrentSubscriptionProps {
  initialData?: {
    plan: SubscriptionPlan;
    expiryDate: string | null;
    isActive: boolean;
  };
}

export default function CurrentSubscription({
  initialData,
}: CurrentSubscriptionProps) {
  const router = useRouter();
  const [subscription, setSubscription] = useState(initialData);
  const [isLoading, setIsLoading] = useState(!initialData);

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Tidak ada";
    return format(new Date(dateString), "dd MMMM yyyy", { locale: id });
  };

  // Fetch subscription data if not provided
  const fetchSubscription = async () => {
    if (initialData) return;

    try {
      setIsLoading(true);
      const response = await fetch("/api/subscriptions");

      if (!response.ok) {
        throw new Error("Failed to fetch subscription");
      }

      const data = await response.json();
      setSubscription(data.subscription);
    } catch (error) {
      console.error("Error fetching subscription:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Load subscription on component mount if not provided
  useEffect(() => {
    fetchSubscription();
  }, [initialData]);

  // If loading or no subscription data
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Langganan Saat Ini</CardTitle>
          <CardDescription>
            Informasi langganan dan status pembayaran Anda
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center items-center py-8">
            <svg
              className="animate-spin h-8 w-8 text-primary"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
              ></circle>
              <path
                className="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Langganan Saat Ini</CardTitle>
          <CardDescription>
            Informasi langganan dan status pembayaran Anda
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-6 gap-2 text-muted-foreground">
            <AlertCircle className="h-5 w-5" />
            <span>Tidak dapat memuat informasi langganan</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const planDetails = SUBSCRIPTION_PLANS[subscription.plan];

  return (
    <Card className="overflow-hidden border shadow-sm hover:shadow-md transition-shadow">
      <div className="flex flex-col md:flex-row">
        {/* Left side - Plan info */}
        <div className="p-6 md:w-2/3">
          <div className="flex items-center gap-3 mb-4">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h3 className="font-medium text-lg">Paket {planDetails.name}</h3>
              <p className="text-sm text-muted-foreground">
                {planDetails.description}
              </p>
            </div>
          </div>

          <div className="space-y-3 mt-6">
            {planDetails.features.slice(0, 4).map((feature, index) => (
              <div key={index} className="flex items-start gap-2">
                <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                <span className="text-sm">{feature}</span>
              </div>
            ))}
          </div>

          {subscription.expiryDate && (
            <p className="text-sm text-muted-foreground mt-4 flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              Aktif hingga: {formatDate(subscription.expiryDate)}
            </p>
          )}
        </div>

        {/* Right side - Price and status */}
        <div className="bg-muted/30 p-6 md:w-1/3 flex flex-col justify-between border-t md:border-t-0 md:border-l">
          <div>
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium">Status</span>
              {subscription.isActive ? (
                <Badge
                  variant="outline"
                  className="bg-green-50 text-green-700 border-green-200"
                >
                  Aktif
                </Badge>
              ) : (
                <Badge
                  variant="outline"
                  className="bg-red-50 text-red-700 border-red-200"
                >
                  Tidak Aktif
                </Badge>
              )}
            </div>

            <div className="mb-6">
              <span className="text-sm text-muted-foreground">Harga</span>
              <div className="mt-1">
                {planDetails.price > 0 ? (
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold">
                      {new Intl.NumberFormat("id-ID", {
                        style: "currency",
                        currency: "IDR",
                        minimumFractionDigits: 0,
                        maximumFractionDigits: 0,
                      }).format(planDetails.price)}
                    </span>
                    <span className="text-sm text-muted-foreground ml-1">
                      / {planDetails.period}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-baseline">
                    <span className="text-2xl font-bold">Gratis</span>
                    <span className="text-sm text-muted-foreground ml-1">
                      {planDetails.period}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Button
            variant="default"
            size="sm"
            className="w-full"
            onClick={() => router.push("/dashboard/settings/plans")}
          >
            Ubah Paket
          </Button>
        </div>
      </div>
    </Card>
  );
}
