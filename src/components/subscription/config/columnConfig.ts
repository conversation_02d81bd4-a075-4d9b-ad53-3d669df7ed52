import { PaymentStatus } from "@prisma/client";

export interface PaymentColumnVisibility {
  date: boolean;
  orderId: boolean;
  package: boolean;
  amount: boolean;
  status: boolean;
  paymentDate: boolean;
  actions: boolean;
}

export interface PaymentColumnConfig {
  key: keyof PaymentColumnVisibility;
  label: string;
  sortKey: string;
  width?: string;
  align?: "left" | "center" | "right";
}

// Payment history column configuration with order (matching the actual table structure)
export const paymentHistoryColumnConfig: PaymentColumnConfig[] = [
  {
    key: "date",
    label: "Tanggal",
    sortKey: "createdAt",
    width: "150px",
    align: "left",
  },
  {
    key: "orderId",
    label: "Order ID",
    sortKey: "invoiceId",
    width: "200px",
    align: "left",
  },
  {
    key: "package",
    label: "Paket",
    sortKey: "subscription.plan",
    width: "120px",
    align: "left",
  },
  {
    key: "amount",
    label: "Jumlah",
    sortKey: "amount",
    width: "130px",
    align: "left",
  },
  {
    key: "status",
    label: "Status",
    sortKey: "status",
    width: "120px",
    align: "left",
  },
  {
    key: "paymentDate",
    label: "Tanggal Bayar",
    sortKey: "paymentDate",
    width: "150px",
    align: "left",
  },
  {
    key: "actions",
    label: "Aksi",
    sortKey: "",
    width: "200px",
    align: "right",
  },
];

// Default column visibility for payment history
export const defaultPaymentColumnVisibility: PaymentColumnVisibility = {
  date: true,
  orderId: true,
  package: true,
  amount: true,
  status: true,
  paymentDate: true,
  actions: true,
};

// Payment status configuration for badges and filtering
export const paymentStatusConfig = {
  PENDING: {
    label: "Menunggu",
    variant: "secondary" as const,
    color: "#6b7280",
    bgColor: "#f3f4f6",
  },
  COMPLETED: {
    label: "Berhasil",
    variant: "default" as const,
    color: "#059669",
    bgColor: "#d1fae5",
  },
  FAILED: {
    label: "Gagal",
    variant: "destructive" as const,
    color: "#dc2626",
    bgColor: "#fee2e2",
  },
  EXPIRED: {
    label: "Kedaluwarsa",
    variant: "outline" as const,
    color: "#d97706",
    bgColor: "#fef3c7",
  },
  REFUNDED: {
    label: "Dikembalikan",
    variant: "outline" as const,
    color: "#7c3aed",
    bgColor: "#ede9fe",
  },
};

// Helper function to get status configuration
export const getPaymentStatusConfig = (status: PaymentStatus) => {
  return paymentStatusConfig[status] || paymentStatusConfig.PENDING;
};

// Sort options for payment history
export const paymentSortOptions = [
  { value: "createdAt-desc", label: "Terbaru" },
  { value: "createdAt-asc", label: "Terlama" },
  { value: "amount-desc", label: "Jumlah Tertinggi" },
  { value: "amount-asc", label: "Jumlah Terendah" },
  { value: "status-asc", label: "Status A-Z" },
  { value: "status-desc", label: "Status Z-A" },
  { value: "paymentDate-desc", label: "Tanggal Bayar Terbaru" },
  { value: "paymentDate-asc", label: "Tanggal Bayar Terlama" },
];

// Filter options for payment status
export const paymentStatusFilterOptions = [
  { value: "all", label: "Semua Status" },
  { value: "PENDING", label: "Menunggu" },
  { value: "COMPLETED", label: "Berhasil" },
  { value: "FAILED", label: "Gagal" },
  { value: "EXPIRED", label: "Kedaluwarsa" },
  { value: "REFUNDED", label: "Dikembalikan" },
];

// Helper function to format currency for display
export const formatPaymentCurrency = (amount: number, currency = "IDR") => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Helper function to format date for display
export const formatPaymentDate = (date: Date | string | null) => {
  if (!date) return "-";
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return new Intl.DateTimeFormat("id-ID", {
    day: "2-digit",
    month: "short",
    year: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  }).format(dateObj);
};

// Helper function to get plan display name
export const getPaymentPlanName = (planKey: string) => {
  const planNames: { [key: string]: string } = {
    BASIC: "Basic",
    PRO: "Pro",
    ENTERPRISE: "Enterprise",
    basic: "Basic",
    pro: "Pro",
    enterprise: "Enterprise",
  };
  return planNames[planKey] || planKey;
};

// Types are already exported above, no need to re-export
