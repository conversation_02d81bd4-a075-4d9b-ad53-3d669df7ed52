import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Payment, PaymentColumnVisibility } from "../types";
import { 
  RefreshCwIcon, 
  ExternalLinkIcon, 
  Trash2Icon,
  ChevronUpIcon,
  ChevronDownIcon
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { paymentHistoryColumnConfig } from "../config/columnConfig";
import { 
  getPaymentStatusConfig,
  formatPaymentCurrency,
  formatPaymentDate,
  getPaymentPlanName
} from "../config/columnConfig";

interface PaymentHistoryTableDesktopProps {
  payments: Payment[];
  columnVisibility: PaymentColumnVisibility;
  handleSort: (field: string) => void;
  getSortIcon: (field: string) => React.ReactNode;
  searchTerm: string;
  refreshingId: string | null;
  deletingId: string | null;
  deleteConfirmId: string | null;
  onRefresh: (paymentId: string) => void;
  onDelete: (paymentId: string) => void;
  onDeleteConfirm: (paymentId: string) => void;
  onDeleteCancel: () => void;
  onPaymentClick: (payment: Payment) => void;
}

export const PaymentHistoryTableDesktop: React.FC<PaymentHistoryTableDesktopProps> = ({
  payments,
  columnVisibility,
  handleSort,
  getSortIcon,
  searchTerm,
  refreshingId,
  deletingId,
  deleteConfirmId,
  onRefresh,
  onDelete,
  onDeleteConfirm,
  onDeleteCancel,
  onPaymentClick,
}) => {
  const router = useRouter();

  // Helper function to render cell content based on column key
  const renderCellContent = (
    payment: Payment,
    columnKey: keyof PaymentColumnVisibility
  ) => {
    switch (columnKey) {
      case "date":
        return (
          <div className="text-sm font-medium">
            {formatPaymentDate(payment.createdAt)}
          </div>
        );
      case "orderId":
        return payment.invoiceId ? (
          <button
            className="text-blue-600 hover:text-blue-800 hover:underline font-mono text-xs transition-colors"
            onClick={() => onPaymentClick(payment)}
            title="Klik untuk melihat detail pembayaran"
          >
            {payment.invoiceId}
          </button>
        ) : (
          <span className="text-muted-foreground">-</span>
        );
      case "package":
        const planName = payment.subscription
          ? getPaymentPlanName(payment.subscription.plan)
          : "Unknown";
        return <div className="font-medium text-sm">{planName}</div>;
      case "amount":
        return (
          <div className="font-semibold text-sm">
            {formatPaymentCurrency(Number(payment.amount), payment.currency)}
          </div>
        );
      case "status":
        const config = getPaymentStatusConfig(payment.status);
        return (
          <span 
            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium`}
            style={{ 
              backgroundColor: config.bgColor, 
              color: config.color 
            }}
          >
            {config.label}
          </span>
        );
      case "paymentDate":
        return (
          <div className="text-sm text-muted-foreground">
            {formatPaymentDate(payment.paymentDate)}
          </div>
        );
      default:
        return "-";
    }
  };

  // Helper function to highlight search terms
  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm) return text;
    
    const regex = new RegExp(`(${searchTerm})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            {paymentHistoryColumnConfig.map(
              (column) =>
                columnVisibility[column.key] && (
                  <th
                    key={column.key}
                    scope="col"
                    className={`px-4 py-3 border-r border-gray-200 dark:border-gray-700 ${
                      column.sortKey ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-600' : ''
                    } ${column.align === 'right' ? 'text-right' : column.align === 'center' ? 'text-center' : 'text-left'}`}
                    style={{ width: column.width }}
                    onClick={() => column.sortKey ? handleSort(column.sortKey) : undefined}
                  >
                    <div className="flex items-center gap-1">
                      {column.label}
                      {column.sortKey && getSortIcon(column.sortKey)}
                    </div>
                  </th>
                )
            )}
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {payments.length > 0 ? (
            payments.map((payment) => (
              <tr
                key={payment.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                {paymentHistoryColumnConfig.map(
                  (column) =>
                    columnVisibility[column.key] && (
                      <td
                        key={column.key}
                        className={`px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 ${
                          column.key === "orderId" || column.key === "package"
                            ? "font-medium text-gray-900 dark:text-gray-100"
                            : "text-gray-500 dark:text-gray-400"
                        } ${column.align === 'right' ? 'text-right' : column.align === 'center' ? 'text-center' : 'text-left'}`}
                      >
                        {renderCellContent(payment, column.key)}
                      </td>
                    )
                )}
                
                {/* Actions Column */}
                <td className="px-6 py-4 text-right">
                  <div className="flex justify-end gap-2 items-center">
                    {payment.status === "PENDING" && (
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8 rounded-full"
                            onClick={() => onRefresh(payment.id)}
                            disabled={refreshingId === payment.id}
                          >
                            {refreshingId === payment.id ? (
                              <RefreshCwIcon className="h-4 w-4 animate-spin" />
                            ) : (
                              <RefreshCwIcon className="h-4 w-4" />
                            )}
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Periksa Status Pembayaran</p>
                        </TooltipContent>
                      </Tooltip>
                    )}

                    {payment.externalUrl && payment.status === "PENDING" && (
                      <Button
                        variant="default"
                        size="sm"
                        className="rounded-full"
                        onClick={() => window.open(payment.externalUrl!, "_blank")}
                      >
                        <ExternalLinkIcon className="h-4 w-4 mr-1.5" />
                        Bayar
                      </Button>
                    )}

                    {/* Delete button - only show for non-completed payments */}
                    {payment.status !== "COMPLETED" && (
                      <>
                        {deleteConfirmId === payment.id ? (
                          <div className="flex gap-1">
                            <Button
                              variant="destructive"
                              size="sm"
                              className="rounded-full text-xs px-2"
                              onClick={() => onDelete(payment.id)}
                              disabled={deletingId === payment.id}
                            >
                              {deletingId === payment.id ? (
                                <RefreshCwIcon className="h-3 w-3 animate-spin mr-1" />
                              ) : null}
                              Ya
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              className="rounded-full text-xs px-2"
                              onClick={onDeleteCancel}
                              disabled={deletingId === payment.id}
                            >
                              Batal
                            </Button>
                          </div>
                        ) : (
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="outline"
                                size="icon"
                                className="h-8 w-8 rounded-full text-red-600 hover:text-red-700 hover:bg-red-50"
                                onClick={() => onDeleteConfirm(payment.id)}
                                disabled={deletingId === payment.id}
                              >
                                <Trash2Icon className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Hapus Pembayaran</p>
                            </TooltipContent>
                          </Tooltip>
                        )}
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td 
                colSpan={paymentHistoryColumnConfig.filter(col => columnVisibility[col.key]).length + 1}
                className="px-6 py-12 text-center"
              >
                <div className="flex flex-col items-center gap-3">
                  <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center">
                    <RefreshCwIcon className="h-6 w-6 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium">Belum Ada Pembayaran</h3>
                  <p className="text-sm text-muted-foreground max-w-md">
                    Riwayat pembayaran Anda akan muncul di sini setelah Anda
                    melakukan pembayaran untuk langganan.
                  </p>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};
