"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Package,
  Receipt,
  Users,
  Contact,
  AlertTriangle,
  Infinity,
  ChevronsUp,
} from "lucide-react";
import Link from "next/link";

interface UsageSummary {
  plan: string;
  products: {
    current: number;
    limit: number | null;
    percentage: number;
  };
  transactions: {
    current: number;
    limit: number | null;
    percentage: number;
  };
  users: {
    current: number;
    limit: number | null;
    percentage: number;
  };
  contacts: {
    current: number;
    limit: number | null;
    percentage: number;
  };
  features: {
    notifications: {
      website: boolean;
      email: boolean;
    };
    backup: {
      manual: boolean;
      automatic: boolean;
      frequencies: string[];
    };
    support: {
      priority: boolean;
      developer: boolean;
    };
  };
}

interface UsageSummaryProps {
  className?: string;
  showUpgradeButton?: boolean;
}

export default function UsageSummary({
  className,
  showUpgradeButton = true,
}: UsageSummaryProps) {
  const [usage, setUsage] = useState<UsageSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchUsage();
  }, []);

  const fetchUsage = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/subscription/usage");
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch usage");
      }

      setUsage(data.usage);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch usage");
    } finally {
      setLoading(false);
    }
  };

  const formatLimit = (limit: number | null) => {
    return limit === null ? "Unlimited" : limit.toLocaleString();
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return "bg-red-500";
    if (percentage >= 75) return "bg-yellow-500";
    return "bg-green-500";
  };

  const isNearLimit = (percentage: number) => percentage >= 80;

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Penggunaan Langganan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-2 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Penggunaan Langganan</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-500 text-sm">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchUsage}
            className="mt-2"
          >
            Coba Lagi
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!usage) return null;

  const usageItems = [
    {
      icon: Package,
      label: "Produk",
      current: usage.products.current,
      limit: usage.products.limit,
      percentage: usage.products.percentage,
    },
    {
      icon: Receipt,
      label: "Transaksi (Bulan Ini)",
      current: usage.transactions.current,
      limit: usage.transactions.limit,
      percentage: usage.transactions.percentage,
    },
    {
      icon: Users,
      label: "Pengguna",
      current: usage.users.current,
      limit: usage.users.limit,
      percentage: usage.users.percentage,
    },
    {
      icon: Contact,
      label: "Kontak",
      current: usage.contacts.current,
      limit: usage.contacts.limit,
      percentage: usage.contacts.percentage,
    },
  ];

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg">Penggunaan Langganan</CardTitle>
        <Badge variant="outline">{usage.plan}</Badge>
      </CardHeader>
      <CardContent className="space-y-4">
        {usageItems.map((item) => {
          const Icon = item.icon;
          const isUnlimited = item.limit === null;
          const nearLimit = !isUnlimited && isNearLimit(item.percentage);

          return (
            <div key={item.label} className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Icon className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm font-medium">{item.label}</span>
                  {nearLimit && (
                    <AlertTriangle className="h-4 w-4 text-yellow-500" />
                  )}
                </div>
                <div className="flex items-center gap-1 text-sm">
                  <span>{item.current.toLocaleString()}</span>
                  <span className="text-muted-foreground">/</span>
                  {isUnlimited ? (
                    <div className="flex items-center gap-1">
                      <Infinity className="h-3 w-3" />
                      <span>Unlimited</span>
                    </div>
                  ) : (
                    <span>{formatLimit(item.limit)}</span>
                  )}
                </div>
              </div>
              {!isUnlimited && (
                <Progress
                  value={item.percentage}
                  className="h-2"
                  // Custom color based on usage
                />
              )}
            </div>
          );
        })}

        {showUpgradeButton && usage.plan === "Paket Gratis" && (
          <div className="pt-4 border-t">
            <Link href="/dashboard/settings/plans">
              <Button className="w-full" size="sm">
                <ChevronsUp className="h-4 w-4 mr-2" />
                Upgrade Paket
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
