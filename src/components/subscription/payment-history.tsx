"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { ChevronUpIcon, ChevronDownIcon } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Pagination } from "@/components/ui/pagination";
import PaymentDetailModal from "./payment-detail-modal";
import { Payment, PaymentColumnVisibility } from "./types";
import { PaymentActions } from "./components/PaymentActions";
import { PaymentHistoryTableDesktop } from "./components/PaymentHistoryTableDesktop";
import { PaymentFilterState } from "./components/PaymentFilter";
import { defaultPaymentColumnVisibility } from "./config/columnConfig";

export default function PaymentHistory() {
  const router = useRouter();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshingId, setRefreshingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [deleteConfirmId, setDeleteConfirmId] = useState<string | null>(null);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [showPaymentDetail, setShowPaymentDetail] = useState(false);

  // Table state
  const [columnVisibility, setColumnVisibility] =
    useState<PaymentColumnVisibility>(defaultPaymentColumnVisibility);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [filters, setFilters] = useState<PaymentFilterState>({
    status: "all",
    planType: "all",
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [paginatedPayments, setPaginatedPayments] = useState<Payment[]>([]);

  // Helper function to handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  // Helper function to get sort icon
  const getSortIcon = (field: string) => {
    if (sortField !== field) return null;
    return sortDirection === "asc" ? (
      <ChevronUpIcon className="h-4 w-4" />
    ) : (
      <ChevronDownIcon className="h-4 w-4" />
    );
  };

  // Filter and search logic
  useEffect(() => {
    let filtered = [...payments];

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter((payment) => {
        const searchLower = searchTerm.toLowerCase();
        return (
          payment.invoiceId?.toLowerCase().includes(searchLower) ||
          payment.subscription?.plan.toLowerCase().includes(searchLower) ||
          payment.status.toLowerCase().includes(searchLower)
        );
      });
    }

    // Apply status filter
    if (filters.status && filters.status !== "all") {
      filtered = filtered.filter(
        (payment) => payment.status === filters.status
      );
    }

    // Apply plan type filter
    if (filters.planType && filters.planType !== "all") {
      filtered = filtered.filter(
        (payment) => payment.subscription?.plan === filters.planType
      );
    }

    // Apply date range filter
    if (filters.dateFrom) {
      filtered = filtered.filter(
        (payment) => new Date(payment.createdAt) >= filters.dateFrom!
      );
    }
    if (filters.dateTo) {
      filtered = filtered.filter(
        (payment) => new Date(payment.createdAt) <= filters.dateTo!
      );
    }

    // Apply amount range filter
    if (filters.amountRange?.min) {
      filtered = filtered.filter(
        (payment) => Number(payment.amount) >= filters.amountRange!.min!
      );
    }
    if (filters.amountRange?.max) {
      filtered = filtered.filter(
        (payment) => Number(payment.amount) <= filters.amountRange!.max!
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (sortField) {
        case "createdAt":
          aValue = new Date(a.createdAt);
          bValue = new Date(b.createdAt);
          break;
        case "amount":
          aValue = Number(a.amount);
          bValue = Number(b.amount);
          break;
        case "status":
          aValue = a.status;
          bValue = b.status;
          break;
        case "paymentDate":
          aValue = a.paymentDate ? new Date(a.paymentDate) : new Date(0);
          bValue = b.paymentDate ? new Date(b.paymentDate) : new Date(0);
          break;
        case "invoiceId":
          aValue = a.invoiceId || "";
          bValue = b.invoiceId || "";
          break;
        default:
          aValue = a.createdAt;
          bValue = b.createdAt;
      }

      if (sortDirection === "asc") {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredPayments(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  }, [payments, searchTerm, filters, sortField, sortDirection]);

  // Pagination logic
  useEffect(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    setPaginatedPayments(filteredPayments.slice(startIndex, endIndex));
  }, [filteredPayments, currentPage, itemsPerPage]);

  // Fetch payment history
  const fetchPayments = async () => {
    try {
      console.log("📋 [PAYMENT HISTORY] Fetching payment history");
      setIsLoading(true);
      const response = await fetch("/api/payments");

      if (!response.ok) {
        console.error(
          "❌ [PAYMENT HISTORY] Failed to fetch payments:",
          response.status
        );
        throw new Error("Failed to fetch payments");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment history loaded:", {
        count: data.payments?.length || 0,
      });
      setPayments(data.payments || []);
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error fetching payments:", error);
      toast.error("Gagal memuat riwayat pembayaran");
    } finally {
      setIsLoading(false);
    }
  };

  // Check payment status
  const checkPaymentStatus = async (paymentId: string) => {
    try {
      console.log("🔍 [PAYMENT HISTORY] Starting payment status check:", {
        paymentId,
      });
      setRefreshingId(paymentId);

      const response = await fetch(`/api/payments/check/${paymentId}`, {
        method: "POST",
      });

      console.log("📊 [PAYMENT HISTORY] Payment status check response:", {
        paymentId,
        status: response.status,
        ok: response.ok,
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ [PAYMENT HISTORY] Payment status check failed:", {
          paymentId,
          status: response.status,
          error: errorData.error,
        });
        throw new Error(errorData.error || "Failed to check payment status");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment status check successful:", {
        paymentId,
        newStatus: data.status,
        hasStatusChange: !!data.status,
      });

      // If status changed, refresh the list
      if (data.status) {
        console.log(
          "🔄 [PAYMENT HISTORY] Status changed, refreshing payment list:",
          {
            paymentId,
            newStatus: data.status,
          }
        );
        fetchPayments();

        // If payment is completed, refresh the page to update subscription status
        if (data.status === "COMPLETED") {
          console.log(
            "💰 [PAYMENT HISTORY] Payment completed, refreshing page:",
            { paymentId }
          );
          toast.success("Pembayaran berhasil dikonfirmasi!");
          router.refresh();
        } else {
          toast.success(`Status pembayaran diperbarui: ${data.status}`);
        }
      } else {
        console.log("ℹ️ [PAYMENT HISTORY] No status change detected:", {
          paymentId,
        });
        toast.info("Status pembayaran tidak berubah");
      }
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error checking payment status:", {
        paymentId,
        error: error instanceof Error ? error.message : "Unknown error",
        stack: error instanceof Error ? error.stack : undefined,
      });
      toast.error(
        error instanceof Error
          ? error.message
          : "Gagal memeriksa status pembayaran"
      );
    } finally {
      console.log("🏁 [PAYMENT HISTORY] Payment status check completed:", {
        paymentId,
      });
      setRefreshingId(null);
    }
  };

  // Delete payment
  const deletePayment = async (paymentId: string) => {
    try {
      console.log("🗑️ [PAYMENT HISTORY] Starting payment deletion:", {
        paymentId,
      });
      setDeletingId(paymentId);

      const response = await fetch(`/api/payments?id=${paymentId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error("❌ [PAYMENT HISTORY] Failed to delete payment:", {
          paymentId,
          status: response.status,
          error: errorData.error,
        });
        throw new Error(errorData.error || "Failed to delete payment");
      }

      const data = await response.json();
      console.log("✅ [PAYMENT HISTORY] Payment deleted successfully:", {
        paymentId,
        message: data.message,
      });

      // Remove the payment from the local state
      setPayments((prev) => prev.filter((payment) => payment.id !== paymentId));

      // Clear confirmation state
      setDeleteConfirmId(null);

      toast.success("Pembayaran berhasil dihapus");
    } catch (error) {
      console.error("❌ [PAYMENT HISTORY] Error deleting payment:", {
        paymentId,
        error: error instanceof Error ? error.message : "Unknown error",
      });
      toast.error(
        error instanceof Error ? error.message : "Gagal menghapus pembayaran"
      );
    } finally {
      setDeletingId(null);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = (paymentId: string) => {
    console.log("🔔 [PAYMENT HISTORY] Delete confirmation requested:", {
      paymentId,
    });
    setDeleteConfirmId(paymentId);
  };

  // Handle delete cancel
  const handleDeleteCancel = () => {
    console.log("❌ [PAYMENT HISTORY] Delete confirmation cancelled");
    setDeleteConfirmId(null);
  };

  // Handle order ID click to show payment details
  const handleOrderIdClick = (payment: Payment) => {
    console.log("🔍 [PAYMENT HISTORY] Order ID clicked:", {
      paymentId: payment.id,
      orderId: payment.invoiceId,
    });
    setSelectedPayment(payment);
    setShowPaymentDetail(true);
  };

  // Handler functions for the table
  const handleRefresh = (paymentId: string) => {
    checkPaymentStatus(paymentId);
  };

  const handleDelete = (paymentId: string) => {
    deletePayment(paymentId);
  };

  // Load payments on component mount
  useEffect(() => {
    fetchPayments();
  }, []);

  return (
    <div className="">
      <div className="space-y-6">
        {/* Payment Actions */}
        <PaymentActions
          columnVisibility={columnVisibility}
          setColumnVisibility={setColumnVisibility}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          filters={filters}
          onFilterChange={setFilters}
          onRefresh={fetchPayments}
          totalPayments={filteredPayments.length}
          isLoading={isLoading}
        />
        {/* Payment History Table */}
        <Card className="border-none py-0">
          <CardContent className="p-0">
            {isLoading ? (
              <div className="flex justify-center items-center py-8">
                <div className="flex flex-col items-center gap-2">
                  <svg
                    className="animate-spin h-8 w-8 text-primary"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  <p className="text-sm text-muted-foreground">
                    Memuat riwayat pembayaran...
                  </p>
                </div>
              </div>
            ) : (
              <PaymentHistoryTableDesktop
                payments={paginatedPayments}
                columnVisibility={columnVisibility}
                handleSort={handleSort}
                getSortIcon={getSortIcon}
                searchTerm={searchTerm}
                refreshingId={refreshingId}
                deletingId={deletingId}
                deleteConfirmId={deleteConfirmId}
                onRefresh={handleRefresh}
                onDelete={handleDelete}
                onDeleteConfirm={handleDeleteConfirm}
                onDeleteCancel={handleDeleteCancel}
                onPaymentClick={handleOrderIdClick}
              />
            )}
          </CardContent>
        </Card>
      </div>

      {/* Pagination */}
      {filteredPayments.length > itemsPerPage && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Menampilkan {(currentPage - 1) * itemsPerPage + 1} -{" "}
            {Math.min(currentPage * itemsPerPage, filteredPayments.length)} dari{" "}
            {filteredPayments.length} transaksi
          </div>
          <Pagination
            currentPage={currentPage}
            totalPages={Math.ceil(filteredPayments.length / itemsPerPage)}
            totalItems={filteredPayments.length}
            onPageChange={setCurrentPage}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={setItemsPerPage}
          />
        </div>
      )}

      {/* Payment Detail Modal */}
      <PaymentDetailModal
        payment={selectedPayment}
        isOpen={showPaymentDetail}
        onClose={() => {
          setShowPaymentDetail(false);
          setSelectedPayment(null);
        }}
      />
    </div>
  );
}
