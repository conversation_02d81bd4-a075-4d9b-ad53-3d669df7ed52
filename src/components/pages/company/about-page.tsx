"use client";

import React from "react";
import { PublicPageLayout } from "@/components/layout/public-page-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Target,
  Eye,
  Heart,
  Users,
  Award,
  TrendingUp,
  Shield,
  Zap,
  Globe,
  Star,
} from "lucide-react";

interface TeamMember {
  name: string;
  role: string;
  image: string;
  description: string;
}

interface Statistic {
  label: string;
  value: string;
  icon: React.ElementType;
}

export const AboutPage: React.FC = () => {
  const statistics: Statistic[] = [
    { label: "Bisnis Terdaftar", value: "10,000+", icon: Users },
    { label: "Transaksi Diproses", value: "1M+", icon: TrendingUp },
    { label: "Kota Terjangkau", value: "100+", icon: Globe },
    { label: "Rating Kepuasan", value: "4.9/5", icon: Star },
  ];

  const values = [
    {
      icon: Shield,
      title: "Keamanan Terp<PERSON>",
      description: "Data bisnis Anda dilindungi dengan standar keamanan tingkat bank dan enkripsi end-to-end.",
    },
    {
      icon: Zap,
      title: "Inovasi Berkelanjutan",
      description: "Kami terus mengembangkan fitur-fitur baru untuk memenuhi kebutuhan bisnis modern.",
    },
    {
      icon: Heart,
      title: "Fokus pada Pelanggan",
      description: "Kepuasan dan kesuksesan bisnis Anda adalah prioritas utama dalam setiap keputusan kami.",
    },
    {
      icon: Award,
      title: "Kualitas Terbaik",
      description: "Komitmen kami adalah memberikan solusi POS dengan kualitas dan performa terbaik.",
    },
  ];

  const teamMembers: TeamMember[] = [
    {
      name: "Ahmad Rizki",
      role: "CEO & Founder",
      image: "/api/placeholder/150/150",
      description: "Visioner di balik KivaPOS dengan pengalaman 10+ tahun di industri teknologi retail.",
    },
    {
      name: "Sari Dewi",
      role: "CTO",
      image: "/api/placeholder/150/150",
      description: "Ahli teknologi yang memimpin pengembangan platform dengan arsitektur yang scalable.",
    },
    {
      name: "Budi Santoso",
      role: "Head of Product",
      image: "/api/placeholder/150/150",
      description: "Bertanggung jawab merancang pengalaman pengguna yang intuitif dan powerful.",
    },
    {
      name: "Maya Putri",
      role: "Head of Customer Success",
      image: "/api/placeholder/150/150",
      description: "Memastikan setiap pelanggan mendapatkan nilai maksimal dari platform KivaPOS.",
    },
  ];

  return (
    <PublicPageLayout
      title="Tentang Kami"
      description="Mengenal lebih dekat KivaPOS dan tim di balik platform POS terdepan di Indonesia"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Company Story */}
        <section className="mb-16">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Cerita Kami</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
              KivaPOS lahir dari pengalaman langsung mengelola bisnis retail dan memahami 
              tantangan yang dihadapi pemilik usaha kecil hingga menengah di Indonesia. 
              Kami percaya bahwa setiap bisnis, tidak peduli ukurannya, berhak mendapatkan 
              akses ke teknologi POS yang powerful, mudah digunakan, dan terjangkau.
            </p>
            <p className="text-lg text-gray-600 dark:text-gray-300">
              Sejak diluncurkan pada tahun 2020, KivaPOS telah membantu ribuan bisnis 
              mengoptimalkan operasional mereka, meningkatkan efisiensi, dan mengembangkan 
              usaha dengan lebih baik melalui data dan insights yang actionable.
            </p>
          </div>
        </section>

        {/* Statistics */}
        <section className="mb-16">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {statistics.map((stat, index) => (
              <Card key={index} className="text-center">
                <CardContent className="pt-6">
                  <div className="mx-auto mb-4 p-3 bg-blue-100 dark:bg-blue-900/20 rounded-full w-fit">
                    <stat.icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                    {stat.value}
                  </div>
                  <p className="text-gray-600 dark:text-gray-300">{stat.label}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="mb-16">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <Target className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  Misi Kami
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Memberdayakan setiap bisnis di Indonesia dengan teknologi POS yang 
                  inovatif, mudah digunakan, dan terjangkau. Kami berkomitmen untuk 
                  menjadi partner terpercaya dalam transformasi digital bisnis retail 
                  dan F&B di seluruh nusantara.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                    <Eye className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  Visi Kami
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  Menjadi platform POS nomor satu di Indonesia yang mengubah cara 
                  bisnis beroperasi melalui teknologi cerdas, data-driven insights, 
                  dan ekosistem yang terintegrasi untuk mendorong pertumbuhan ekonomi 
                  digital Indonesia.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Values */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Nilai-Nilai Kami</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Nilai-nilai yang menjadi fondasi dalam setiap keputusan dan inovasi yang kami lakukan
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                      <value.icon className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                    </div>
                    {value.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 dark:text-gray-300">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* Team */}
        <section className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Tim Kami</h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              Bertemu dengan orang-orang hebat di balik kesuksesan KivaPOS
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <CardContent className="pt-6">
                  <div className="w-24 h-24 mx-auto mb-4 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center">
                    <Users className="h-12 w-12 text-gray-400" />
                  </div>
                  <h3 className="font-semibold text-lg mb-1">{member.name}</h3>
                  <Badge variant="secondary" className="mb-3">
                    {member.role}
                  </Badge>
                  <p className="text-sm text-gray-600 dark:text-gray-300">
                    {member.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* CTA */}
        <section className="text-center">
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
            <CardContent className="py-12">
              <h2 className="text-3xl font-bold mb-4">Siap Bergabung dengan KivaPOS?</h2>
              <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
                Bergabunglah dengan ribuan bisnis yang telah mempercayai KivaPOS 
                untuk mengoptimalkan operasional dan meningkatkan penjualan mereka.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button size="lg" asChild>
                  <a href="/register">
                    Mulai Gratis Sekarang
                  </a>
                </Button>
                <Button size="lg" variant="outline" asChild>
                  <a href="/contact">
                    Hubungi Tim Sales
                  </a>
                </Button>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </PublicPageLayout>
  );
};
