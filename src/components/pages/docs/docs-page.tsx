"use client";

import React, { useState } from "react";
import { PublicPageLayout } from "@/components/layout/public-page-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import {
  BookOpen,
  Users,
  ShoppingCart,
  Package,
  BarChart3,
  Settings,
  CreditCard,
  FileText,
  Search,
  ChevronRight,
  ExternalLink,
} from "lucide-react";

interface DocSection {
  id: string;
  title: string;
  icon: React.ElementType;
  content: React.ReactNode;
}

export const DocsPage: React.FC = () => {
  const [activeSection, setActiveSection] = useState("getting-started");

  const docSections: DocSection[] = [
    {
      id: "getting-started",
      title: "Me<PERSON>lai",
      icon: BookOpen,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Selamat Datang di KivaPOS</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              KivaPOS adalah sistem Point of Sale modern yang membantu Anda mengelola bisnis dengan lebih efisien. 
              Panduan ini akan membantu Anda memahami fitur-fitur utama dan cara menggunakannya.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Langkah Pertama</h4>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Daftar akun baru atau masuk ke akun yang sudah ada</li>
              <li>Lengkapi profil bisnis Anda di menu Pengaturan</li>
              <li>Tambahkan produk pertama Anda</li>
              <li>Mulai membuat transaksi penjualan</li>
            </ol>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h4 className="text-lg font-medium mb-2 text-blue-800 dark:text-blue-200">
              💡 Tips untuk Pemula
            </h4>
            <p className="text-blue-700 dark:text-blue-300">
              Mulai dengan menambahkan beberapa produk dan coba buat transaksi percobaan untuk 
              memahami alur kerja sistem.
            </p>
          </div>
        </div>
      ),
    },
    {
      id: "products",
      title: "Manajemen Produk",
      icon: Package,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Mengelola Produk</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Pelajari cara menambah, mengedit, dan mengelola produk dalam sistem KivaPOS.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Menambah Produk Baru</h4>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Buka menu "Produk" di sidebar</li>
              <li>Klik tombol "Tambah Produk"</li>
              <li>Isi informasi produk: nama, harga, stok, kategori</li>
              <li>Upload gambar produk (opsional)</li>
              <li>Klik "Simpan" untuk menyimpan produk</li>
            </ol>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Fitur Produk</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Manajemen stok otomatis</li>
              <li>Kategori produk untuk organisasi yang lebih baik</li>
              <li>Barcode untuk scanning cepat</li>
              <li>Harga khusus dan diskon</li>
              <li>Tracking perubahan harga</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: "sales",
      title: "Transaksi Penjualan",
      icon: ShoppingCart,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Membuat Transaksi Penjualan</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Panduan lengkap untuk membuat dan mengelola transaksi penjualan.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Proses Penjualan</h4>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Buka menu "Penjualan"</li>
              <li>Klik "Transaksi Baru"</li>
              <li>Pilih atau scan produk yang dijual</li>
              <li>Atur jumlah dan harga (jika perlu)</li>
              <li>Pilih metode pembayaran</li>
              <li>Cetak atau kirim struk digital</li>
            </ol>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Metode Pembayaran</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Tunai</li>
              <li>Transfer Bank</li>
              <li>E-wallet (GoPay, OVO, DANA)</li>
              <li>Kartu Kredit/Debit</li>
              <li>Kredit/Hutang</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: "customers",
      title: "Manajemen Pelanggan",
      icon: Users,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Mengelola Data Pelanggan</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Simpan dan kelola informasi pelanggan untuk layanan yang lebih personal.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Menambah Pelanggan</h4>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Buka menu "Pelanggan"</li>
              <li>Klik "Tambah Pelanggan"</li>
              <li>Isi data: nama, nomor telepon, alamat, email</li>
              <li>Simpan data pelanggan</li>
            </ol>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Fitur Pelanggan</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Riwayat pembelian pelanggan</li>
              <li>Program loyalitas dan poin</li>
              <li>Reminder follow-up</li>
              <li>Segmentasi pelanggan</li>
            </ul>
          </div>
        </div>
      ),
    },
    {
      id: "reports",
      title: "Laporan & Analitik",
      icon: BarChart3,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Memahami Laporan Bisnis</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Gunakan laporan dan analitik untuk memahami performa bisnis Anda.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Jenis Laporan</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Laporan Penjualan Harian/Bulanan</li>
              <li>Laporan Produk Terlaris</li>
              <li>Laporan Stok dan Inventory</li>
              <li>Laporan Keuangan</li>
              <li>Laporan Pelanggan</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Cara Mengakses Laporan</h4>
            <ol className="list-decimal list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Buka menu "Laporan" di sidebar</li>
              <li>Pilih jenis laporan yang diinginkan</li>
              <li>Atur periode waktu</li>
              <li>Klik "Generate Laporan"</li>
              <li>Export ke PDF atau Excel jika diperlukan</li>
            </ol>
          </div>
        </div>
      ),
    },
    {
      id: "settings",
      title: "Pengaturan Sistem",
      icon: Settings,
      content: (
        <div className="space-y-6">
          <div>
            <h3 className="text-xl font-semibold mb-3">Konfigurasi Sistem</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Sesuaikan pengaturan sistem agar sesuai dengan kebutuhan bisnis Anda.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Pengaturan Umum</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Informasi bisnis (nama, alamat, kontak)</li>
              <li>Logo dan branding</li>
              <li>Mata uang dan format angka</li>
              <li>Zona waktu</li>
              <li>Bahasa sistem</li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-medium mb-3">Pengaturan Transaksi</h4>
            <ul className="list-disc list-inside space-y-2 text-gray-600 dark:text-gray-300">
              <li>Format nomor transaksi</li>
              <li>Template struk</li>
              <li>Metode pembayaran yang tersedia</li>
              <li>Pajak dan diskon default</li>
            </ul>
          </div>
        </div>
      ),
    },
  ];

  const quickLinks = [
    { title: "Panduan Video", href: "#", icon: ExternalLink },
    { title: "FAQ", href: "#", icon: ExternalLink },
    { title: "Kontak Support", href: "/contact", icon: ExternalLink },
    { title: "Changelog", href: "#", icon: ExternalLink },
  ];

  return (
    <PublicPageLayout
      title="Dokumentasi"
      description="Panduan lengkap penggunaan KivaPOS untuk mengoptimalkan bisnis Anda"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:w-1/4">
            <div className="sticky top-8">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    Navigasi Dokumentasi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {docSections.map((section) => (
                    <Button
                      key={section.id}
                      variant={activeSection === section.id ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => setActiveSection(section.id)}
                    >
                      <section.icon className="h-4 w-4 mr-2" />
                      {section.title}
                    </Button>
                  ))}
                </CardContent>
              </Card>

              {/* Quick Links */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Link Berguna</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {quickLinks.map((link, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      className="w-full justify-between"
                      asChild
                    >
                      <a href={link.href}>
                        {link.title}
                        <link.icon className="h-4 w-4" />
                      </a>
                    </Button>
                  ))}
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:w-3/4">
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  {docSections.find(s => s.id === activeSection)?.icon && (
                    <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                      {React.createElement(
                        docSections.find(s => s.id === activeSection)!.icon,
                        { className: "h-6 w-6 text-blue-600 dark:text-blue-400" }
                      )}
                    </div>
                  )}
                  <CardTitle className="text-2xl">
                    {docSections.find(s => s.id === activeSection)?.title}
                  </CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                {docSections.find(s => s.id === activeSection)?.content}
              </CardContent>
            </Card>

            {/* Navigation Footer */}
            <div className="flex justify-between items-center mt-8">
              <div>
                {docSections.findIndex(s => s.id === activeSection) > 0 && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      const currentIndex = docSections.findIndex(s => s.id === activeSection);
                      setActiveSection(docSections[currentIndex - 1].id);
                    }}
                  >
                    ← Sebelumnya
                  </Button>
                )}
              </div>
              <div>
                {docSections.findIndex(s => s.id === activeSection) < docSections.length - 1 && (
                  <Button
                    onClick={() => {
                      const currentIndex = docSections.findIndex(s => s.id === activeSection);
                      setActiveSection(docSections[currentIndex + 1].id);
                    }}
                  >
                    Selanjutnya →
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </PublicPageLayout>
  );
};
