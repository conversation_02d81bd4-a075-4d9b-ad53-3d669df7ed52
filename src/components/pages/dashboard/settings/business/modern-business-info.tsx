"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import {
  Building2,
  Building,
  Globe,
  Phone,
  Mail,
  MapPin,
  Clock,
  Bell,
  Save,
  RefreshCw,
  Briefcase,
  Users,
  Factory,
  CreditCard,
  Gift,
  Image as ImageIcon,
  Edit3,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import {
  getBusinessInfo,
  updateBusinessInfo,
  type BusinessInfoData,
} from "@/actions/users/additional-info";
import { User } from "../profile/types";

interface ModernBusinessInfoProps {
  user: User;
}

export default function ModernBusinessInfo({ user }: ModernBusinessInfoProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [businessInfo, setBusinessInfo] = useState<BusinessInfoData>({});

  // Load business info on component mount
  useEffect(() => {
    const loadBusinessInfo = async () => {
      setIsLoading(true);
      try {
        const result = await getBusinessInfo();
        if (result.success && result.data) {
          console.log("Result data:", result.data); // Add this line for debugging
          setBusinessInfo(result.data as BusinessInfoData);
        }
      } catch (error) {
        console.error("Error loading business info:", error);
        toast.error("Gagal memuat informasi bisnis");
      } finally {
        setIsLoading(false);
      }
    };

    loadBusinessInfo();
  }, []);

  // Handle form submission
  const handleSave = async () => {
    setIsSaving(true);
    try {
      const result = await updateBusinessInfo(businessInfo);
      if (result.success) {
        toast.success("Informasi bisnis berhasil disimpan!");
      } else {
        toast.error(result.error || "Gagal menyimpan informasi bisnis");
      }
    } catch (error) {
      console.error("Error saving business info:", error);
      toast.error("Terjadi kesalahan saat menyimpan");
    } finally {
      setIsSaving(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof BusinessInfoData, value: string) => {
    setBusinessInfo((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Calculate completion percentage
  const calculateCompletion = () => {
    const fields = [
      businessInfo.companyName,
      businessInfo.companyUsername,
      businessInfo.companyPhone,
      businessInfo.companyEmail,
      businessInfo.companyAddress,
      businessInfo.industry,
      businessInfo.position,
      businessInfo.employeeCount,
    ];
    const completedFields = fields.filter(
      (field) => field && field.trim() !== ""
    ).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const completionPercentage = calculateCompletion();

  if (isLoading) {
    return (
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardContent className="flex items-center justify-center py-12">
          <RefreshCw className="h-8 w-8 animate-spin text-indigo-600" />
          <span className="ml-3 text-lg">Memuat informasi bisnis...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold">
                Informasi Bisnis
              </CardTitle>
              <p className="text-gray-600 dark:text-gray-400">
                Kelola detail perusahaan dan informasi bisnis Anda
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Badge
              className={`px-3 py-1 ${
                completionPercentage >= 80
                  ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                  : completionPercentage >= 50
                    ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"
                    : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
              }`}
            >
              {completionPercentage >= 80 ? (
                <CheckCircle className="h-3 w-3 mr-1" />
              ) : (
                <AlertCircle className="h-3 w-3 mr-1" />
              )}
              {completionPercentage}% Lengkap
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-8">
        {/* Company Basic Information */}
        <div className="space-y-6">
          <div className="flex items-center gap-3 mb-4">
            <Building className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Informasi Perusahaan
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="companyName" className="text-sm font-medium">
                Nama Perusahaan
              </Label>
              <Input
                id="companyName"
                value={businessInfo.companyName || ""}
                onChange={(e) =>
                  handleInputChange("companyName", e.target.value)
                }
                placeholder="Masukkan nama perusahaan"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyUsername" className="text-sm font-medium">
                Username Perusahaan
              </Label>
              <Input
                id="companyUsername"
                value={businessInfo.companyUsername || ""}
                onChange={(e) =>
                  handleInputChange("companyUsername", e.target.value)
                }
                placeholder="username-perusahaan"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyPhone" className="text-sm font-medium">
                Telepon Perusahaan
              </Label>
              <Input
                id="companyPhone"
                value={businessInfo.companyPhone || ""}
                onChange={(e) =>
                  handleInputChange("companyPhone", e.target.value)
                }
                placeholder="+62 xxx-xxxx-xxxx"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="companyEmail" className="text-sm font-medium">
                Email Perusahaan
              </Label>
              <Input
                id="companyEmail"
                type="email"
                value={businessInfo.companyEmail || ""}
                onChange={(e) =>
                  handleInputChange("companyEmail", e.target.value)
                }
                placeholder="<EMAIL>"
                className="bg-white dark:bg-gray-800"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="companyAddress" className="text-sm font-medium">
              Alamat Perusahaan
            </Label>
            <Textarea
              id="companyAddress"
              value={businessInfo.companyAddress || ""}
              onChange={(e) =>
                handleInputChange("companyAddress", e.target.value)
              }
              placeholder="Masukkan alamat lengkap perusahaan"
              className="bg-white dark:bg-gray-800 min-h-[80px]"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="city" className="text-sm font-medium">
                Kota
              </Label>
              <Input
                id="city"
                value={businessInfo.city || ""}
                onChange={(e) => handleInputChange("city", e.target.value)}
                placeholder="Jakarta"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="postalCode" className="text-sm font-medium">
                Kode Pos
              </Label>
              <Input
                id="postalCode"
                value={businessInfo.postalCode || ""}
                onChange={(e) =>
                  handleInputChange("postalCode", e.target.value)
                }
                placeholder="12345"
                className="bg-white dark:bg-gray-800"
              />
            </div>
          </div>
        </div>

        <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

        {/* Business Details */}
        <div className="space-y-6">
          <div className="flex items-center gap-3 mb-4">
            <Briefcase className="h-5 w-5 text-indigo-600" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Detail Bisnis
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="industry" className="text-sm font-medium">
                Industri
              </Label>
              <Input
                id="industry"
                value={businessInfo.industry || ""}
                onChange={(e) => handleInputChange("industry", e.target.value)}
                placeholder="Teknologi, Retail, F&B, dll"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="position" className="text-sm font-medium">
                Posisi Anda
              </Label>
              <Input
                id="position"
                value={businessInfo.position || ""}
                onChange={(e) => handleInputChange("position", e.target.value)}
                placeholder="CEO, Manager, Owner, dll"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="employeeCount" className="text-sm font-medium">
                Jumlah Karyawan
              </Label>
              <Input
                id="employeeCount"
                value={businessInfo.employeeCount || ""}
                onChange={(e) =>
                  handleInputChange("employeeCount", e.target.value)
                }
                placeholder="1-10, 11-50, 51-200, dll"
                className="bg-white dark:bg-gray-800"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="occupation" className="text-sm font-medium">
                Bidang Usaha
              </Label>
              <Input
                id="occupation"
                value={businessInfo.occupation || ""}
                onChange={(e) =>
                  handleInputChange("occupation", e.target.value)
                }
                placeholder="Perdagangan, Jasa, Manufaktur, dll"
                className="bg-white dark:bg-gray-800"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={() => window.location.reload()}
            disabled={isSaving}
            className="px-6"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button
            onClick={handleSave}
            disabled={isSaving}
            className="px-6 bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700"
          >
            {isSaving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            {isSaving ? "Menyimpan..." : "Simpan Perubahan"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
