"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import {
  Building2,
  Save,
  CheckCircle,
  AlertCircle,
  MapPin,
  Phone,
  Mail,
  Globe,
  Receipt,
  Calculator,
  Image,
  Store,
  Users,
  Clock,
  Shield,
} from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import ModernBusinessInfo from "../profile/modern-business-info";

export default function BusinessSettings() {
  // Business Information
  const [businessName, setBusinessName] = useState("Toko Saya");
  const [businessType, setBusinessType] = useState("retail");
  const [businessAddress, setBusinessAddress] = useState("");
  const [businessPhone, setBusinessPhone] = useState("");
  const [businessEmail, setBusinessEmail] = useState("");
  const [businessWebsite, setBusinessWebsite] = useState("");
  const [businessDescription, setBusinessDescription] = useState("");

  // Tax Settings
  const [enableTax, setEnableTax] = useState(false);
  const [taxRate, setTaxRate] = useState("10");
  const [taxNumber, setTaxNumber] = useState("");

  // Receipt Settings
  const [receiptHeader, setReceiptHeader] = useState("");
  const [receiptFooter, setReceiptFooter] = useState(
    "Terima kasih telah berbelanja!"
  );
  const [showLogo, setShowLogo] = useState(true);
  const [receiptTemplate, setReceiptTemplate] = useState("modern");

  // Operating Hours
  const [operatingHours, setOperatingHours] = useState({
    monday: { open: "08:00", close: "17:00", closed: false },
    tuesday: { open: "08:00", close: "17:00", closed: false },
    wednesday: { open: "08:00", close: "17:00", closed: false },
    thursday: { open: "08:00", close: "17:00", closed: false },
    friday: { open: "08:00", close: "17:00", closed: false },
    saturday: { open: "08:00", close: "17:00", closed: false },
    sunday: { open: "08:00", close: "17:00", closed: true },
  });

  // Status
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const businessTypes = [
    { value: "retail", label: "Retail", icon: Store },
    { value: "food", label: "Makanan & Minuman", icon: Receipt },
    { value: "service", label: "Jasa", icon: Users },
    { value: "wholesale", label: "Grosir", icon: Building2 },
    { value: "other", label: "Lainnya", icon: Globe },
  ];

  const handleSaveSettings = () => {
    setIsLoading(true);
    setTimeout(() => {
      setShowSuccess(true);
      setIsLoading(false);
      setTimeout(() => setShowSuccess(false), 3000);
    }, 1000);
  };

  const days = [
    { key: "monday", label: "Senin" },
    { key: "tuesday", label: "Selasa" },
    { key: "wednesday", label: "Rabu" },
    { key: "thursday", label: "Kamis" },
    { key: "friday", label: "Jumat" },
    { key: "saturday", label: "Sabtu" },
    { key: "sunday", label: "Minggu" },
  ];

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Hero Header */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 p-8 text-white">
        <div className="absolute inset-0">
          <img
            src="https://images.unsplash.com/photo-1528800557835-47d25f56a5a5?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwyfHxvZmZpY2UlMjBidWlsZGluZyUyMGJ1c2luZXNzfGVufDB8Mnx8Z3JlZW58MTc1NDQ1NDk3OXww&ixlib=rb-4.1.0&q=85"
            alt="Business and office illustration by Simone Hutsch on Unsplash"
            className="w-full h-full object-cover opacity-20"
            style={{ width: "100%", height: "100%" }}
          />
        </div>
        <div className="relative z-10">
          <div className="flex items-center gap-4 mb-4">
            <div className="h-12 w-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
              <Building2 className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-2xl font-bold">Pengaturan Bisnis</h1>
              <p className="text-white/80">
                Kelola informasi dan konfigurasi bisnis Anda
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            <span className="text-sm">
              Data bisnis Anda aman dan terenkripsi
            </span>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div></div>
    </div>
  );
}
