"use client";

import { useState } from "react";
import { <PERSON><PERSON>, Check, ExternalLink, Settings, AlertCircle } from "lucide-react";
import { toast } from "sonner";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function RedirectionSettings() {
  const [copiedUrl, setCopiedUrl] = useState<string | null>(null);

  // Get base URL from environment or construct it
  const baseUrl =
    process.env.NEXT_PUBLIC_APP_URL ||
    (typeof window !== "undefined"
      ? window.location.origin
      : "https://yourdomain.com");

  // Define redirect URLs for Midtrans configuration
  const redirectUrls = {
    finish: `${baseUrl}/dashboard/settings/billing/success`,
    error: `${baseUrl}/dashboard/settings/billing/failed`,
    pending: `${baseUrl}/dashboard/settings/billing/pending`,
    webhook: `${baseUrl}/api/webhooks/midtrans`,
  };

  const copyToClipboard = async (url: string, type: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setCopiedUrl(type);
      toast.success(`${type} URL berhasil disalin!`);

      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedUrl(null), 2000);
    } catch (error) {
      toast.error("Gagal menyalin URL");
    }
  };

  const openMidtransDashboard = () => {
    const dashboardUrl =
      process.env.MIDTRANS_IS_PRODUCTION === "true"
        ? "https://dashboard.midtrans.com"
        : "https://dashboard.sandbox.midtrans.com";

    window.open(dashboardUrl, "_blank");
  };

  return (
    <div className="space-y-6 min-h-[100vh]">
      {/* Header */}
      <Card className="border-0 shadow-sm bg-gradient-to-r from-primary/5 to-primary/10">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
              <Settings className="h-5 w-5 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold">
                Pengaturan Redirect Midtrans
              </h2>
              <p className="text-sm text-muted-foreground">
                URL yang diperlukan untuk konfigurasi Midtrans Dashboard
              </p>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Instructions */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Salin URL di bawah ini dan konfigurasikan di Midtrans Dashboard Anda
          pada bagian <strong>Settings → Configuration</strong>
        </AlertDescription>
      </Alert>

      {/* Redirect URLs Configuration */}
      <div className="grid gap-6">
        {/* Finish URL */}
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Finish Redirect URL</CardTitle>
                <CardDescription>
                  URL yang akan dituju ketika pembayaran berhasil diselesaikan
                </CardDescription>
              </div>
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200"
              >
                Success
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="finish-url">URL Finish</Label>
              <div className="flex gap-2">
                <Input
                  id="finish-url"
                  value={redirectUrls.finish}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(redirectUrls.finish, "Finish")}
                >
                  {copiedUrl === "Finish" ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Error URL */}
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Error Redirect URL</CardTitle>
                <CardDescription>
                  URL yang akan dituju ketika pembayaran gagal atau dibatalkan
                </CardDescription>
              </div>
              <Badge
                variant="outline"
                className="bg-red-50 text-red-700 border-red-200"
              >
                Error
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="error-url">URL Error</Label>
              <div className="flex gap-2">
                <Input
                  id="error-url"
                  value={redirectUrls.error}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => copyToClipboard(redirectUrls.error, "Error")}
                >
                  {copiedUrl === "Error" ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending URL */}
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">Unfinish Redirect URL</CardTitle>
                <CardDescription>
                  URL yang akan dituju ketika pembayaran masih dalam proses
                </CardDescription>
              </div>
              <Badge
                variant="outline"
                className="bg-yellow-50 text-yellow-700 border-yellow-200"
              >
                Pending
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pending-url">URL Unfinish</Label>
              <div className="flex gap-2">
                <Input
                  id="pending-url"
                  value={redirectUrls.pending}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() =>
                    copyToClipboard(redirectUrls.pending, "Pending")
                  }
                >
                  {copiedUrl === "Pending" ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Webhook URL */}
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-lg">
                  Payment Notification URL
                </CardTitle>
                <CardDescription>
                  URL webhook untuk menerima notifikasi status pembayaran
                </CardDescription>
              </div>
              <Badge
                variant="outline"
                className="bg-blue-50 text-blue-700 border-blue-200"
              >
                Webhook
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="webhook-url">URL Webhook</Label>
              <div className="flex gap-2">
                <Input
                  id="webhook-url"
                  value={redirectUrls.webhook}
                  readOnly
                  className="font-mono text-sm"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() =>
                    copyToClipboard(redirectUrls.webhook, "Webhook")
                  }
                >
                  {copiedUrl === "Webhook" ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <Card className="border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg">Langkah Selanjutnya</CardTitle>
          <CardDescription>
            Buka Midtrans Dashboard untuk mengkonfigurasi URL di atas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={openMidtransDashboard}
              className="flex items-center gap-2"
            >
              <ExternalLink className="h-4 w-4" />
              Buka Midtrans Dashboard
            </Button>
            <Button
              variant="outline"
              onClick={() => {
                const allUrls = Object.entries(redirectUrls)
                  .map(([key, url]) => `${key.toUpperCase()}: ${url}`)
                  .join("\n");

                navigator.clipboard.writeText(allUrls);
                toast.success("Semua URL berhasil disalin!");
              }}
            >
              Salin Semua URL
            </Button>
          </div>

          <div className="text-sm text-muted-foreground space-y-2">
            <p>
              <strong>Cara konfigurasi di Midtrans Dashboard:</strong>
            </p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Login ke Midtrans Dashboard</li>
              <li>
                Pilih menu <strong>Settings</strong> →{" "}
                <strong>Configuration</strong>
              </li>
              <li>Masukkan URL di atas pada field yang sesuai</li>
              <li>
                Klik <strong>Save</strong> untuk menyimpan konfigurasi
              </li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
