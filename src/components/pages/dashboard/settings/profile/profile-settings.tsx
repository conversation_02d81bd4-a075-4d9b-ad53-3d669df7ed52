"use client";

import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Tabs,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from "@/components/ui/tabs-v2";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { ProfileSettingsProps, getRoleBadgeColor } from "./types";
import ModernProfileImage from "./modern-profile-image";
import ModernAccountInfo from "./modern-account-info";
import ModernProfileForm from "./modern-profile-form";
import ModernProfileStats from "./modern-profile-stats";
import ModernSecuritySettings from "./modern-security-settings";
import ModernNotificationSettings from "./modern-notification-settings";
import ModernBusinessInfo from "../business/modern-business-info";
import UserActivityTab from "./user-activity-tab";
import {
  User,
  UserCog,
  Shield,
  Bell,
  Activity,
  Settings,
  Star,
  TrendingUp,
  Zap,
  Crown,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";

export default function ProfileSettings({ user }: ProfileSettingsProps) {
  // Form values
  const [name, setName] = useState(user.name || "");
  const [username, setUsername] = useState(user.username || "");
  const [imageUrl, setImageUrl] = useState(user.image || "");
  const [phone, setPhone] = useState(user.phone || "");
  const [bio, setBio] = useState(user.bio || "");
  const [birthday, setBirthday] = useState<Date | undefined>(
    user.birthday ? new Date(user.birthday) : undefined
  );

  // Profile completion calculation
  const calculateProfileCompletion = () => {
    const fields = [name, username, imageUrl, phone, bio, birthday];
    const completedFields = fields.filter(
      (field) => field && field !== ""
    ).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const profileCompletion = calculateProfileCompletion();

  const handleReset = () => {
    setName(user.name || "");
    setUsername(user.username || "");
    setImageUrl(user.image || "");
    setPhone(user.phone || "");
    setBio(user.bio || "");
    setBirthday(user.birthday ? new Date(user.birthday) : undefined);
  };

  return (
    <div className="min-h-screen rounded-xl bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 p-6">
      {/* Modern Hero Section with Enhanced Responsiveness */}
      <div className="relative overflow-hidden rounded-3xl mb-8 group">
        {/* Background with Mesh Gradient and Glass Effect */}
        <div className="absolute inset-0">
          <div
            className="absolute inset-0 bg-gradient-to-br from-violet-600/90 via-fuchsia-600/90 to-cyan-500/90 opacity-95 transition-opacity duration-700 group-hover:opacity-100"
            style={{
              backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(236, 72, 153, 0.3) 0%, transparent 50%),
          url('https://images.unsplash.com/photo-1656231775842-74b961b70b16?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHwzfHxncmFkaWVudCUyMGFic3RyYWN0JTIwbW9kZXJuJTIwYmFja2dyb3VuZHxlbnwwfDB8fGJsdWV8MTc1NDQ0ODYzMXww&ixlib=rb-4.1.0&q=85')
        `,
              backgroundSize: "cover",
              backgroundPosition: "center",
            }}
          />
          {/* Animated Overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -translate-x-full animate-pulse group-hover:translate-x-full transition-transform duration-3000 ease-in-out" />
        </div>

        <div className="relative z-10 px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between gap-6 lg:gap-8">
            {/* Profile and Info Section */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 sm:gap-6 flex-1">
              {/* Profile Image with Enhanced Animation */}
              <div className="relative group/avatar">
                <div className="absolute -inset-1 bg-gradient-to-r from-violet-400 via-fuchsia-400 to-cyan-400 rounded-full opacity-0 group-hover/avatar:opacity-100 blur-sm transition-all duration-500" />
                <ModernProfileImage
                  imageUrl={imageUrl}
                  setImageUrl={setImageUrl}
                  name={name}
                  username={username}
                />
                <div className="absolute -bottom-2 -right-2 bg-white dark:bg-gray-900 rounded-full p-2 shadow-xl border-2 border-white/20 backdrop-blur-sm transform transition-transform duration-300 hover:scale-110">
                  <Crown className="h-4 w-4 text-amber-500 drop-shadow-lg" />
                </div>
              </div>

              {/* Text Content */}
              <div className="text-white space-y-3 sm:space-y-4">
                <div className="space-y-2">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent leading-tight">
                    Pengaturan Profil
                  </h1>
                  <p className="text-base sm:text-lg lg:text-xl text-white/90 font-medium max-w-md">
                    Kelola informasi pribadi dan preferensi akun Anda
                  </p>
                </div>

                {/* Badges Row */}
                <div className="flex flex-col xs:flex-row items-start xs:items-center gap-3 xs:gap-4">
                  <Badge
                    variant="secondary"
                    className={`${getRoleBadgeColor(user.role)} px-3 py-2 text-sm font-semibold shadow-xl border border-white/20 backdrop-blur-sm hover:scale-105 transition-all duration-300`}
                  >
                    <Crown className="h-4 w-4 mr-2" />
                    {user.role}
                  </Badge>

                  <div className="flex items-center gap-2 bg-white/15 backdrop-blur-md rounded-full px-4 py-2 border border-white/20 shadow-lg hover:bg-white/20 transition-all duration-300">
                    <TrendingUp className="h-4 w-4 text-emerald-300" />
                    <span className="text-sm font-medium">
                      Profil {profileCompletion}% Lengkap
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Progress Card - Now Responsive */}
            <div className="w-full sm:w-auto lg:w-auto">
              <div className="bg-white/10 backdrop-blur-xl rounded-2xl p-4 sm:p-6 border border-white/20 shadow-2xl hover:bg-white/15 transition-all duration-500 hover:scale-105">
                <div className="text-center space-y-3">
                  <div className="relative">
                    <div className="text-2xl sm:text-3xl font-bold text-white drop-shadow-lg">
                      {profileCompletion}%
                    </div>
                    <div className="absolute -inset-2 bg-gradient-to-r from-violet-400/20 via-fuchsia-400/20 to-cyan-400/20 rounded-lg blur-xl opacity-0 animate-pulse" />
                  </div>

                  <div className="text-xs sm:text-sm text-white/80 font-medium">
                    Kelengkapan Profil
                  </div>

                  <div className="space-y-2">
                    <Progress
                      value={profileCompletion}
                      className="h-2 sm:h-3 bg-white/20 rounded-full overflow-hidden backdrop-blur-sm"
                    />
                    <div className="flex justify-between text-xs text-white/60">
                      <span>0%</span>
                      <span>100%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Mobile-First Action Buttons (Optional Enhancement) */}
          <div className="flex flex-col sm:flex-row gap-3 mt-6 lg:hidden">
            <button className="flex-1 bg-white/20 hover:bg-white/30 backdrop-blur-md text-white font-medium py-3 px-4 rounded-xl border border-white/30 transition-all duration-300 hover:scale-105">
              Edit Profil
            </button>
            <button className="flex-1 bg-gradient-to-r from-violet-500 to-fuchsia-500 hover:from-violet-600 hover:to-fuchsia-600 text-white font-medium py-3 px-4 rounded-xl shadow-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl">
              Lengkapi Profil
            </button>
          </div>
        </div>

        {/* Floating Particles Effect (Optional) */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-10 left-10 w-2 h-2 bg-white/30 rounded-full animate-ping" />
          <div className="absolute top-20 right-16 w-1 h-1 bg-cyan-300/40 rounded-full animate-pulse delay-1000" />
          <div className="absolute bottom-16 left-20 w-1.5 h-1.5 bg-fuchsia-300/30 rounded-full animate-bounce delay-2000" />
        </div>
      </div>

      {/* Profile Completion Alert */}
      {profileCompletion < 80 && (
        <Alert className="mb-6 border-l-4 border-l-amber-500 bg-amber-50 dark:bg-amber-900/20">
          <AlertTriangle className="h-4 w-4 text-amber-600" />
          <AlertTitle className="text-amber-800 dark:text-amber-200">
            Lengkapi Profil Anda
          </AlertTitle>
          <AlertDescription className="text-amber-700 dark:text-amber-300">
            Profil yang lengkap membantu meningkatkan keamanan dan pengalaman
            penggunaan aplikasi.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3 bg-white/70 dark:bg-gray-800/70 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20">
          <TabsTrigger
            value="profile"
            className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-indigo-600 transition-all duration-200"
          >
            <User className="h-4 w-4 mr-2" />
            Profil
          </TabsTrigger>
          <TabsTrigger
            value="account"
            className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-indigo-600 transition-all duration-200"
          >
            <UserCog className="h-4 w-4 mr-2" />
            Akun
          </TabsTrigger>
          <TabsTrigger
            value="activity"
            className="rounded-xl data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-indigo-600 transition-all duration-200"
          >
            <Activity className="h-4 w-4 mr-2" />
            Aktivitas
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Profile Form - Takes 2 columns */}
            <div className="lg:col-span-2">
              <ModernProfileForm
                user={user}
                name={name}
                setName={setName}
                username={username}
                setUsername={setUsername}
                imageUrl={imageUrl}
                phone={phone}
                setPhone={setPhone}
                bio={bio}
                setBio={setBio}
                birthday={birthday}
                setBirthday={setBirthday}
                handleReset={handleReset}
              />
            </div>

            {/* Profile Stats - Takes 1 column */}
            <div className="lg:col-span-1">
              <ModernProfileStats
                user={user}
                profileCompletion={profileCompletion}
              />
            </div>
          </div>
        </TabsContent>

        {/* Account Tab */}
        <TabsContent value="account" className="space-y-6">
          <ModernAccountInfo user={user} />
          <ModernBusinessInfo user={user} />
        </TabsContent>

        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <ModernSecuritySettings user={user} />
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value="notifications" className="space-y-6">
          <ModernNotificationSettings user={user} />
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <UserActivityTab user={user} />
        </TabsContent>
      </Tabs>

      {/* Success Completion Alert */}
      {profileCompletion >= 80 && (
        <Alert className="mt-6 border-l-4 border-l-green-500 bg-green-50 dark:bg-green-900/20">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800 dark:text-green-200">
            Profil Lengkap!
          </AlertTitle>
          <AlertDescription className="text-green-700 dark:text-green-300">
            Selamat! Profil Anda sudah lengkap dan siap digunakan untuk
            pengalaman terbaik.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
