import { Role } from "@prisma/client";

// User interface definition
export interface User {
  id: string;
  name: string | null;
  email: string | null;
  username: string | null;
  image: string | null;
  phone: string | null;
  bio: string | null;
  birthday: Date | null;
  role: Role;
  lastLogin: Date | null;
  createdAt: Date;
  currentPlan?: string;
  subscriptionExpiry?: Date | null;
  companyId: string | null; // Company identifier (from businessInfo)
  companyName: string | null; // Company name (from businessInfo)
  companyUsername: string | null; // Company username (from businessInfo)
}

export interface ProfileSettingsProps {
  user: User;
}

// Helper function to get role badge color
export const getRoleBadgeColor = (role: Role) => {
  switch (role) {
    case "OWNER":
      return "bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300";
    case "ADMIN":
      return "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300";
    case "CASHIER":
      return "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300";
    default:
      return "bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300";
  }
};
