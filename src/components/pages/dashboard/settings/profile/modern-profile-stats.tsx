"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import {
  TrendingUp,
  Star,
  Award,
  Target,
  Calendar,
  Clock,
  Activity,
  Shield,
  CheckCircle,
  AlertTriangle,
  Zap,
  Crown,
  Users,
  BarChart3,
} from "lucide-react";
import { User } from "./types";

interface ModernProfileStatsProps {
  user: User;
  profileCompletion: number;
}

export default function ModernProfileStats({
  user,
  profileCompletion,
}: ModernProfileStatsProps) {
  // Calculate account age in days
  const getAccountAge = () => {
    if (!user.createdAt) return 0;
    const now = new Date();
    const created = new Date(user.createdAt);
    const diffTime = Math.abs(now.getTime() - created.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const accountAge = getAccountAge();

  // Mock data for demonstration
  const stats = {
    totalLogins: 142,
    lastWeekActivity: 85,
    securityScore: 95,
    featuresUsed: 12,
    totalFeatures: 20,
  };

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getCompletionBadgeColor = (percentage: number) => {
    if (percentage >= 80)
      return "bg-gradient-to-r from-green-500 to-emerald-500";
    if (percentage >= 60)
      return "bg-gradient-to-r from-yellow-500 to-orange-500";
    return "bg-gradient-to-r from-red-500 to-pink-500";
  };

  return (
    <div className="space-y-6">
      {/* Profile Completion Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Target className="h-5 w-5 text-white" />
            </div>
            Kelengkapan Profil
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="relative w-32 h-32 mx-auto">
              <svg
                className="w-32 h-32 transform -rotate-90"
                viewBox="0 0 36 36"
              >
                <path
                  className="text-gray-200 dark:text-gray-700"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className={getCompletionColor(profileCompletion)}
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeDasharray={`${profileCompletion}, 100`}
                  strokeLinecap="round"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div
                    className={`text-2xl font-bold ${getCompletionColor(profileCompletion)}`}
                  >
                    {profileCompletion}%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Lengkap
                  </div>
                </div>
              </div>
            </div>

            <Badge
              className={`${getCompletionBadgeColor(profileCompletion)} text-white px-4 py-2 text-sm font-semibold`}
            >
              {profileCompletion >= 80 ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Profil Lengkap
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Perlu Dilengkapi
                </>
              )}
            </Badge>
          </div>

          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Informasi Dasar
              </span>
              <span className="font-medium">✓</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                Foto Profil
              </span>
              <span className="font-medium">{user.image ? "✓" : "○"}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Bio</span>
              <span className="font-medium">{user.bio ? "✓" : "○"}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600 dark:text-gray-400">Kontak</span>
              <span className="font-medium">{user.phone ? "✓" : "○"}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Account Stats Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
            Statistik Akun
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl">
              <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {accountAge}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Hari Bergabung
              </div>
            </div>

            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
              <Activity className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats.totalLogins}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Total Login
              </div>
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Skor Keamanan
                </span>
              </div>
              <span className="text-sm font-bold text-blue-600">
                {stats.securityScore}%
              </span>
            </div>
            <Progress value={stats.securityScore} className="h-2" />

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Penggunaan Fitur
                </span>
              </div>
              <span className="text-sm font-bold text-purple-600">
                {stats.featuresUsed}/{stats.totalFeatures}
              </span>
            </div>
            <Progress
              value={(stats.featuresUsed / stats.totalFeatures) * 100}
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
