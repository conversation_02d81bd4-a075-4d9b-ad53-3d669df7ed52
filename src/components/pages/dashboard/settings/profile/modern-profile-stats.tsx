"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { toast } from "sonner";
import {
  TrendingUp,
  Star,
  Award,
  Target,
  Calendar,
  Clock,
  Activity,
  Shield,
  CheckCircle,
  AlertTriangle,
  Zap,
  Crown,
  Users,
  BarChart3,
} from "lucide-react";
import { User } from "./types";
import {
  getUserStats,
  getProfileCompletion,
  UserStats,
  ProfileCompletion,
} from "@/actions/users/stats";

interface ModernProfileStatsProps {
  user: User;
  profileCompletion: number;
}

export default function ModernProfileStats({
  user,
  profileCompletion: initialProfileCompletion,
}: ModernProfileStatsProps) {
  const [stats, setStats] = useState<UserStats | null>(null);
  const [profileCompletion, setProfileCompletion] = useState(
    initialProfileCompletion
  );
  const [profileDetails, setProfileDetails] =
    useState<ProfileCompletion | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch real data from database
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      setError(null);

      try {
        const [statsResult, profileResult] = await Promise.all([
          getUserStats(),
          getProfileCompletion(),
        ]);

        if (statsResult.success && statsResult.data) {
          setStats(statsResult.data);
        } else {
          setError(statsResult.error || "Gagal memuat statistik");
        }

        if (profileResult.success && profileResult.data) {
          setProfileCompletion(profileResult.data.percentage);
          setProfileDetails(profileResult.data);
        }
      } catch (err) {
        console.error("Error fetching profile stats:", err);
        setError("Terjadi kesalahan saat memuat data");
        toast.error("Gagal memuat statistik profil");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user.id]);

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return "text-green-600";
    if (percentage >= 60) return "text-yellow-600";
    return "text-red-600";
  };

  const getCompletionBadgeColor = (percentage: number) => {
    if (percentage >= 80)
      return "bg-gradient-to-r from-green-500 to-emerald-500";
    if (percentage >= 60)
      return "bg-gradient-to-r from-yellow-500 to-orange-500";
    return "bg-gradient-to-r from-red-500 to-pink-500";
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {/* Loading Profile Completion Card */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
                <Target className="h-5 w-5 text-white" />
              </div>
              Kelengkapan Profil
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center space-y-4">
              <Skeleton className="w-32 h-32 rounded-full mx-auto" />
              <Skeleton className="w-24 h-8 mx-auto" />
            </div>
            <div className="space-y-3">
              {Array.from({ length: 4 }).map((_, i) => (
                <div key={i} className="flex justify-between">
                  <Skeleton className="w-24 h-4" />
                  <Skeleton className="w-4 h-4" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Loading Account Stats Card */}
        <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg font-bold flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
                <BarChart3 className="h-5 w-5 text-white" />
              </div>
              Statistik Akun
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-24 rounded-xl" />
              <Skeleton className="h-24 rounded-xl" />
            </div>
            <Separator />
            <div className="space-y-4">
              <Skeleton className="w-full h-6" />
              <Skeleton className="w-full h-2" />
              <Skeleton className="w-full h-6" />
              <Skeleton className="w-full h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Gagal Memuat Statistik
            </h3>
            <p className="text-gray-500 dark:text-gray-400">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Profile Completion Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Target className="h-5 w-5 text-white" />
            </div>
            Kelengkapan Profil
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <div className="relative w-32 h-32 mx-auto">
              <svg
                className="w-32 h-32 transform -rotate-90"
                viewBox="0 0 36 36"
              >
                <path
                  className="text-gray-200 dark:text-gray-700"
                  stroke="currentColor"
                  strokeWidth="3"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
                <path
                  className={getCompletionColor(profileCompletion)}
                  stroke="currentColor"
                  strokeWidth="3"
                  strokeDasharray={`${profileCompletion}, 100`}
                  strokeLinecap="round"
                  fill="none"
                  d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div
                    className={`text-2xl font-bold ${getCompletionColor(profileCompletion)}`}
                  >
                    {profileCompletion}%
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    Lengkap
                  </div>
                </div>
              </div>
            </div>

            <Badge
              className={`${getCompletionBadgeColor(profileCompletion)} text-white px-4 py-2 text-sm font-semibold`}
            >
              {profileCompletion >= 80 ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Profil Lengkap
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Perlu Dilengkapi
                </>
              )}
            </Badge>
          </div>

          <div className="space-y-3">
            {profileDetails ? (
              <>
                {/* Show completed fields */}
                {profileDetails.completedFields.slice(0, 4).map((field) => (
                  <div key={field} className="flex justify-between text-sm">
                    <span className="text-gray-600 dark:text-gray-400">
                      {field}
                    </span>
                    <span className="font-medium text-green-600">✓</span>
                  </div>
                ))}
                {/* Show missing fields */}
                {profileDetails.missingFields
                  .slice(
                    0,
                    4 - profileDetails.completedFields.slice(0, 4).length
                  )
                  .map((field) => (
                    <div key={field} className="flex justify-between text-sm">
                      <span className="text-gray-600 dark:text-gray-400">
                        {field}
                      </span>
                      <span className="font-medium text-gray-400">○</span>
                    </div>
                  ))}
              </>
            ) : (
              // Fallback to basic checks
              <>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Informasi Dasar
                  </span>
                  <span className="font-medium text-green-600">✓</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Foto Profil
                  </span>
                  <span
                    className={`font-medium ${user.image ? "text-green-600" : "text-gray-400"}`}
                  >
                    {user.image ? "✓" : "○"}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Bio</span>
                  <span
                    className={`font-medium ${user.bio ? "text-green-600" : "text-gray-400"}`}
                  >
                    {user.bio ? "✓" : "○"}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    Kontak
                  </span>
                  <span
                    className={`font-medium ${user.phone ? "text-green-600" : "text-gray-400"}`}
                  >
                    {user.phone ? "✓" : "○"}
                  </span>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Account Stats Card */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-lg font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
              <BarChart3 className="h-5 w-5 text-white" />
            </div>
            Statistik Akun
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl">
              <Calendar className="h-6 w-6 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {accountAge}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Hari Bergabung
              </div>
            </div>

            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
              <Activity className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {stats.totalLogins}
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400">
                Total Login
              </div>
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Skor Keamanan
                </span>
              </div>
              <span className="text-sm font-bold text-blue-600">
                {stats.securityScore}%
              </span>
            </div>
            <Progress value={stats.securityScore} className="h-2" />

            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-600" />
                <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  Penggunaan Fitur
                </span>
              </div>
              <span className="text-sm font-bold text-purple-600">
                {stats.featuresUsed}/{stats.totalFeatures}
              </span>
            </div>
            <Progress
              value={(stats.featuresUsed / stats.totalFeatures) * 100}
              className="h-2"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
