"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Activity,
  RefreshCw,
  Calendar,
  Clock,
  Monitor,
  User,
  ShoppingCart,
  Package,
  LogIn,
  Edit3,
  Filter,
  TrendingUp,
  Eye,
} from "lucide-react";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { toast } from "sonner";
import { getUserActivities, UserActivity } from "@/actions/users/activity";
import { getActivities, ActivityItem } from "@/actions/activity/activity";
import { User as UserType } from "./types";

interface UserActivityTabProps {
  user: UserType;
}

type CombinedActivity = (UserActivity | ActivityItem) & {
  category: "user" | "business";
};

export default function UserActivityTab({ user }: UserActivityTabProps) {
  const [activities, setActivities] = useState<CombinedActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"all" | "user" | "business">("all");
  const [refreshing, setRefreshing] = useState(false);

  const fetchUserActivities = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Fetch user-specific activities (login, profile updates, etc.)
      const userActivitiesResult = await getUserActivities(10);
      
      // Fetch business activities (sales, purchases, products) for current user only
      const businessActivitiesResult = await getActivities({
        limit: 10,
        filters: {
          // Only get activities for the current user
        }
      });

      const combinedActivities: CombinedActivity[] = [];

      // Add user activities
      if (userActivitiesResult.success && userActivitiesResult.data) {
        userActivitiesResult.data.forEach(activity => {
          combinedActivities.push({
            ...activity,
            category: "user"
          });
        });
      }

      // Add business activities (filtered for current user only)
      if (businessActivitiesResult.success && businessActivitiesResult.data) {
        businessActivitiesResult.data.forEach(activity => {
          // Only include activities performed by the current user or their role
          if (user.role === "OWNER" || !activity.isEmployee) {
            combinedActivities.push({
              ...activity,
              category: "business",
              // Add missing properties for UserActivity compatibility
              device: "Web Browser",
              date: new Date()
            } as CombinedActivity);
          }
        });
      }

      // Sort by date (newest first)
      combinedActivities.sort((a, b) => {
        const dateA = 'date' in a ? a.date : new Date();
        const dateB = 'date' in b ? b.date : new Date();
        return dateB.getTime() - dateA.getTime();
      });

      setActivities(combinedActivities);
    } catch (err) {
      console.error("Error fetching user activities:", err);
      setError("Gagal memuat aktivitas pengguna");
      toast.error("Gagal memuat aktivitas pengguna");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserActivities();
  }, [user.id]);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUserActivities();
    setRefreshing(false);
    toast.success("Aktivitas berhasil diperbarui");
  };

  const filteredActivities = activities.filter(activity => {
    if (filter === "all") return true;
    return activity.category === filter;
  });

  const getActivityIcon = (activity: CombinedActivity) => {
    if (activity.category === "user") {
      const userActivity = activity as UserActivity;
      switch (userActivity.type) {
        case "login":
          return <LogIn className="h-4 w-4 text-green-600" />;
        case "profile_update":
          return <Edit3 className="h-4 w-4 text-blue-600" />;
        case "password_change":
          return <User className="h-4 w-4 text-orange-600" />;
        default:
          return <Activity className="h-4 w-4 text-gray-600" />;
      }
    } else {
      const businessActivity = activity as ActivityItem;
      switch (businessActivity.type) {
        case "sale":
          return <ShoppingCart className="h-4 w-4 text-green-600" />;
        case "purchase":
          return <Package className="h-4 w-4 text-blue-600" />;
        case "product":
          return <Package className="h-4 w-4 text-purple-600" />;
        default:
          return <Activity className="h-4 w-4 text-gray-600" />;
      }
    }
  };

  const getActivityBadge = (activity: CombinedActivity) => {
    if (activity.category === "user") {
      return (
        <Badge variant="outline" className="text-xs">
          <User className="h-3 w-3 mr-1" />
          Akun
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="text-xs">
          <TrendingUp className="h-3 w-3 mr-1" />
          Bisnis
        </Badge>
      );
    }
  };

  if (error) {
    return (
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <Activity className="h-16 w-16 text-red-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Gagal Memuat Aktivitas
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
            <Button onClick={handleRefresh} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Coba Lagi
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl">
              <Activity className="h-6 w-6 text-white" />
            </div>
            <div>
              <CardTitle className="text-2xl font-bold">
                Aktivitas Saya
              </CardTitle>
              <p className="text-gray-600 dark:text-gray-400">
                Riwayat aktivitas akun dan bisnis Anda
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
              <SelectTrigger className="w-40">
                <Filter className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua</SelectItem>
                <SelectItem value="user">Akun</SelectItem>
                <SelectItem value="business">Bisnis</SelectItem>
              </SelectContent>
            </Select>
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 5 }).map((_, index) => (
            <div
              key={index}
              className="flex items-start gap-4 p-4 border border-gray-100 dark:border-gray-700 rounded-lg"
            >
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            </div>
          ))
        ) : filteredActivities.length === 0 ? (
          <div className="text-center py-12">
            <Eye className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Belum Ada Aktivitas
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {filter === "all" 
                ? "Aktivitas Anda akan muncul di sini"
                : `Belum ada aktivitas ${filter === "user" ? "akun" : "bisnis"}`
              }
            </p>
          </div>
        ) : (
          filteredActivities.map((activity, index) => (
            <div
              key={`${activity.category}-${activity.id}-${index}`}
              className="flex items-start gap-4 p-4 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <div className="flex-shrink-0 p-2 bg-gray-100 dark:bg-gray-800 rounded-full">
                {getActivityIcon(activity)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {activity.description}
                    </p>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                        <Clock className="h-3 w-3 mr-1" />
                        {activity.timestamp}
                      </div>
                      {activity.category === "user" && (activity as UserActivity).device && (
                        <div className="flex items-center text-xs text-gray-500 dark:text-gray-400">
                          <Monitor className="h-3 w-3 mr-1" />
                          {(activity as UserActivity).device}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    {getActivityBadge(activity)}
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
