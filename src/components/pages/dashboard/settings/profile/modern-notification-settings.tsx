"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Bell,
  Mail,
  Smartphone,
  Volume2,
  VolumeX,
  Clock,
  ShoppingCart,
  Users,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Settings,
  Zap,
  Calendar,
  MessageSquare,
  CreditCard,
  Shield
} from "lucide-react";
import { User } from "./types";

interface ModernNotificationSettingsProps {
  user: User;
}

export default function ModernNotificationSettings({ user }: ModernNotificationSettingsProps) {
  // Email Notifications
  const [emailSecurity, setEmailSecurity] = useState(true);
  const [emailMarketing, setEmailMarketing] = useState(false);
  const [emailUpdates, setEmailUpdates] = useState(true);
  const [emailReports, setEmailReports] = useState(true);

  // Push Notifications
  const [pushSales, setPushSales] = useState(true);
  const [pushInventory, setPushInventory] = useState(true);
  const [pushTeam, setPushTeam] = useState(false);
  const [pushSystem, setPushSystem] = useState(true);

  // App Notifications
  const [appSounds, setAppSounds] = useState(true);
  const [appBadges, setAppBadges] = useState(true);
  const [appPopups, setAppPopups] = useState(false);

  // Timing Settings
  const [quietHours, setQuietHours] = useState(true);
  const [weekendNotif, setWeekendNotif] = useState(false);

  return (
    <div className="space-y-6">
      {/* Notification Overview */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-2xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl">
              <Bell className="h-6 w-6 text-white" />
            </div>
            Pengaturan Notifikasi
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Kelola preferensi notifikasi dan pemberitahuan aplikasi
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">12</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Notifikasi Aktif</div>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl">
              <Mail className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">8</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Email Harian</div>
            </div>
            
            <div className="text-center p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl">
              <Smartphone className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">24</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Push Mingguan</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Email Notifications */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-xl">
              <Mail className="h-5 w-5 text-white" />
            </div>
            Notifikasi Email
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Atur jenis email yang ingin Anda terima
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-500 rounded-lg">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Keamanan & Login</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Notifikasi login, perubahan password, dan aktivitas mencurigakan
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Switch
                  checked={emailSecurity}
                  onCheckedChange={setEmailSecurity}
                />
                <Badge className="bg-red-500 text-white text-xs">Penting</Badge>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500 rounded-lg">
                  <TrendingUp className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Laporan & Analitik</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Laporan penjualan harian, mingguan, dan bulanan
                  </p>
                </div>
              </div>
              <Switch
                checked={emailReports}
                onCheckedChange={setEmailReports}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-500 rounded-lg">
                  <Zap className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Update Produk</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Fitur baru, pembaruan sistem, dan pengumuman penting
                  </p>
                </div>
              </div>
              <Switch
                checked={emailUpdates}
                onCheckedChange={setEmailUpdates}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-500 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Marketing & Promosi</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tips bisnis, webinar, dan penawaran khusus
                  </p>
                </div>
              </div>
              <Switch
                checked={emailMarketing}
                onCheckedChange={setEmailMarketing}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Push Notifications */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl">
              <Smartphone className="h-5 w-5 text-white" />
            </div>
            Notifikasi Push
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Notifikasi real-time untuk aktivitas bisnis penting
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500 rounded-lg">
                  <ShoppingCart className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Transaksi Penjualan</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Notifikasi saat ada penjualan baru atau pembayaran diterima
                  </p>
                </div>
              </div>
              <Switch
                checked={pushSales}
                onCheckedChange={setPushSales}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-orange-500 rounded-lg">
                  <AlertCircle className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Stok Menipis</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Peringatan saat stok produk mencapai batas minimum
                  </p>
                </div>
              </div>
              <Switch
                checked={pushInventory}
                onCheckedChange={setPushInventory}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-500 rounded-lg">
                  <Users className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Aktivitas Tim</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Notifikasi saat anggota tim melakukan aktivitas penting
                  </p>
                </div>
              </div>
              <Switch
                checked={pushTeam}
                onCheckedChange={setPushTeam}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-500 rounded-lg">
                  <Settings className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Sistem & Maintenance</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Pembaruan sistem, maintenance, dan gangguan layanan
                  </p>
                </div>
              </div>
              <Switch
                checked={pushSystem}
                onCheckedChange={setPushSystem}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* App Preferences */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl">
              <Volume2 className="h-5 w-5 text-white" />
            </div>
            Preferensi Aplikasi
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Atur cara notifikasi ditampilkan di aplikasi
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500 rounded-lg">
                  {appSounds ? <Volume2 className="h-5 w-5 text-white" /> : <VolumeX className="h-5 w-5 text-white" />}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Suara Notifikasi</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Mainkan suara saat menerima notifikasi
                  </p>
                </div>
              </div>
              <Switch
                checked={appSounds}
                onCheckedChange={setAppSounds}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-500 rounded-lg">
                  <Bell className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Badge Notifikasi</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tampilkan angka notifikasi di ikon aplikasi
                  </p>
                </div>
              </div>
              <Switch
                checked={appBadges}
                onCheckedChange={setAppBadges}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-purple-500 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Pop-up Notifikasi</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Tampilkan pop-up untuk notifikasi penting
                  </p>
                </div>
              </div>
              <Switch
                checked={appPopups}
                onCheckedChange={setAppPopups}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Timing & Schedule */}
      <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900">
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold flex items-center gap-3">
            <div className="p-2 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl">
              <Clock className="h-5 w-5 text-white" />
            </div>
            Jadwal & Waktu
          </CardTitle>
          <p className="text-gray-600 dark:text-gray-400">
            Atur kapan Anda ingin menerima notifikasi
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-indigo-500 rounded-lg">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Jam Tenang</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Nonaktifkan notifikasi dari 22:00 - 07:00
                  </p>
                </div>
              </div>
              <Switch
                checked={quietHours}
                onCheckedChange={setQuietHours}
              />
            </div>

            <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800/50 rounded-xl">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-500 rounded-lg">
                  <Calendar className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100">Notifikasi Weekend</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Terima notifikasi di hari Sabtu dan Minggu
                  </p>
                </div>
              </div>
              <Switch
                checked={weekendNotif}
                onCheckedChange={setWeekendNotif}
              />
            </div>
          </div>

          <Separator className="bg-gradient-to-r from-transparent via-gray-200 dark:via-gray-700 to-transparent" />

          {/* Quick Actions */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Aksi Cepat</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button 
                variant="outline" 
                className="justify-start border-blue-200 text-blue-600 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20"
              >
                <CheckCircle className="h-4 w-4 mr-3" />
                Aktifkan Semua
              </Button>
              
              <Button 
                variant="outline" 
                className="justify-start border-red-200 text-red-600 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20"
              >
                <VolumeX className="h-4 w-4 mr-3" />
                Nonaktifkan Semua
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}