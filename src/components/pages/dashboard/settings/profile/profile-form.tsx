"use client";

import { useState } from "react";
import { updateProfile } from "@/actions/users/profile";
import { useSession } from "next-auth/react";
import { User } from "./types";
import {
  UserCircle,
  Mail,
  Phone,
  AtSign,
  AlertCircle,
  CheckCircle,
  Building,
  Lock,
} from "lucide-react";
import { DatePicker } from "@/components/ui/date-picker";
import { format } from "date-fns";

interface ProfileFormProps {
  user: User;
  name: string;
  setName: (name: string) => void;
  username: string;
  setUsername: (username: string) => void;
  imageUrl: string;
  phone: string;
  setPhone: (phone: string) => void;
  bio: string;
  setBio: (bio: string) => void;
  birthday: Date | undefined;
  setBirthday: (date: Date | undefined) => void;
  handleReset: () => void;
}

export default function ProfileForm({
  user,
  name,
  setName,
  username,
  setUsername,
  imageUrl,
  phone,
  setPhone,
  bio,
  setBio,
  birthday,
  setBirthday,
  handleReset,
}: ProfileFormProps) {
  const { update } = useSession();
  const [isProfileLoading, setIsProfileLoading] = useState(false);
  const [profileSuccess, setProfileSuccess] = useState<string | null>(null);
  const [profileError, setProfileError] = useState<string | null>(null);

  // Form submission
  const handleProfileSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsProfileLoading(true);
    setProfileSuccess(null);
    setProfileError(null);

    try {
      const result = await updateProfile({
        name,
        username,
        image: imageUrl,
        phone,
        bio,
        birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
      });

      if (result.error) {
        setProfileError(result.error);
      } else {
        setProfileSuccess("Profil berhasil diperbarui!");
        await update({
          user: {
            name,
            username,
            image: imageUrl,
            phone,
            bio,
            // For the session update, convert the birthday to a string
            birthday: birthday ? format(birthday, "yyyy-MM-dd") : "",
          },
        });
      }
    } catch (err) {
      setProfileError("Terjadi kesalahan saat memperbarui profil.");
      console.error(err);
    } finally {
      setIsProfileLoading(false);
    }
  };

  return (
    <form onSubmit={handleProfileSubmit} className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm">
        <div className="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-base font-medium text-gray-900 dark:text-gray-100">
            Edit Profil
          </h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Perbarui informasi profil Anda
          </p>
        </div>
        <div className="px-4 py-5 sm:p-6 space-y-6">
          {/* Notifications */}
          {profileSuccess && (
            <div className="rounded-lg bg-green-50 dark:bg-green-900/20 p-4 border border-green-100 dark:border-green-800/30 shadow-sm animate-fade-in">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-800 dark:text-green-200">
                    {profileSuccess}
                  </p>
                </div>
              </div>
            </div>
          )}

          {profileError && (
            <div className="rounded-lg bg-red-50 dark:bg-red-900/20 p-4 border border-red-100 dark:border-red-800/30 shadow-sm animate-fade-in">
              <div className="flex items-center">
                <AlertCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800 dark:text-red-200">
                    {profileError}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Form Fields */}
          <div className="grid grid-cols-1 gap-y-6 gap-x-6 sm:grid-cols-6">
            <div className="sm:col-span-3">
              <label
                htmlFor="name"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Nama Lengkap
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <UserCircle className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                </div>
                <input
                  type="text"
                  name="name"
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                  placeholder="Masukkan nama lengkap"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="username"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Username
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <AtSign className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                </div>
                <input
                  type="text"
                  name="username"
                  id="username"
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                  className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                  placeholder="username"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Email
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Mail className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <input
                  type="email"
                  name="email"
                  id="email"
                  value={user.email || ""}
                  disabled
                  className="py-3 pl-10 block w-full bg-gray-50 dark:bg-gray-700/50 text-gray-500 dark:text-gray-400 border-gray-200 dark:border-gray-700 rounded-lg cursor-not-allowed shadow-sm"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-700 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:text-gray-300">
                    Terkunci
                  </span>
                </div>
              </div>
              <p className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
                Email tidak dapat diubah
              </p>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="companyId"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                ID Perusahaan
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Building className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                </div>
                <input
                  type="text"
                  name="companyId"
                  id="companyId"
                  value={user.companyId || "Belum tersedia"}
                  disabled
                  className="py-3 pl-10 block w-full bg-gray-50 dark:bg-gray-700/50 text-gray-500 dark:text-gray-400 border-gray-200 dark:border-gray-700 rounded-lg cursor-not-allowed shadow-sm"
                />
                <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span className="inline-flex items-center rounded-full bg-gray-100 dark:bg-gray-700 px-2.5 py-0.5 text-xs font-medium text-gray-800 dark:text-gray-300">
                    Terkunci
                  </span>
                </div>
              </div>
              <p className="mt-1.5 text-xs text-gray-500 dark:text-gray-400">
                ID Perusahaan tidak dapat diubah
              </p>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="phone"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Nomor Telepon
              </label>
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Phone className="h-5 w-5 text-indigo-500 dark:text-indigo-400" />
                </div>
                <input
                  type="tel"
                  name="phone"
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="py-3 pl-10 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                  placeholder="+62 812 3456 7890"
                />
              </div>
            </div>

            <div className="sm:col-span-3">
              <label
                htmlFor="birthday"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Tanggal Lahir
              </label>
              <DatePicker
                date={birthday}
                setDate={setBirthday}
                placeholder="Pilih tanggal lahir"
                className="py-2.5 pl-3 border-gray-200 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-200 dark:hover:bg-gray-700/50 cursor-pointer shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                fromYear={1940}
                toYear={new Date().getFullYear()}
              />
            </div>

            <div className="sm:col-span-6">
              <label
                htmlFor="bio"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1.5"
              >
                Bio
              </label>
              <div className="relative rounded-md shadow-sm">
                <textarea
                  id="bio"
                  name="bio"
                  rows={4}
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  className="py-3 px-4 block w-full border-gray-200 dark:border-gray-700 rounded-lg text-sm focus:border-indigo-500 focus:ring-indigo-500 dark:bg-gray-800 dark:text-gray-200 dark:focus:ring-indigo-600 transition-colors duration-200 ease-in-out shadow-sm hover:border-gray-300 dark:hover:border-gray-600"
                  placeholder="Ceritakan sedikit tentang diri Anda..."
                />
              </div>
              <p className="mt-2 text-xs text-gray-500 dark:text-gray-400 flex items-center">
                <span className="inline-block mr-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                    />
                  </svg>
                </span>
                Deskripsi singkat tentang diri Anda. Maksimal 500 karakter.
              </p>
            </div>
          </div>
        </div>
        <div className="px-6 py-4 bg-gray-50 dark:bg-gray-800/50 flex justify-end gap-3 sm:px-6 border-t border-gray-200 dark:border-gray-700">
          <button
            type="button"
            onClick={handleReset}
            className="inline-flex items-center justify-center py-2.5 px-5 border border-gray-300 dark:border-gray-600 shadow-sm text-sm font-medium rounded-lg text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 transition-colors duration-200 cursor-pointer"
          >
            Reset
          </button>
          <button
            type="submit"
            disabled={isProfileLoading}
            className="inline-flex items-center justify-center py-2.5 px-5 border border-transparent shadow-sm text-sm font-medium rounded-lg text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 cursor-pointer"
          >
            {isProfileLoading ? (
              <>
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Menyimpan...
              </>
            ) : (
              "Simpan Perubahan"
            )}
          </button>
        </div>
      </div>
    </form>
  );
}
