"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>heck,
  AlertCircle,
  CheckCircle,
  Key,
  Clock,
  Bell,
  Laptop,
  Smartphone,
  Tablet,
  LogOut,
  Info,
  Loader2,
  Eye,
  EyeOff,
  Lock,
  Fingerprint,
  History,
  Settings,
  Zap,
  Shield,
  Activity,
  Globe,
  MapPin,
  Calendar,
  Timer,
} from "lucide-react";
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogT<PERSON>le,
  <PERSON>alogTrigger,
} from "@/components/ui/dialog";
import { toast } from "sonner";
import { signOut } from "next-auth/react";
import {
  updatePassword,
  checkUserPasswordStatus,
} from "@/actions/auth/update-password";
import {
  DeviceSession,
  getDeviceSessions,
  revokeAllSessionsIncludingCurrent,
  revokeSession,
} from "@/actions/users/sessions";
import {
  getSessionSettings,
  SessionSettings,
  updateSessionSettings,
} from "@/actions/users/session-settings";

export default function SecuritySettings() {
  // Security settings state
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [sessionTimeout, setSessionTimeout] = useState("30");
  const [loginNotifications, setLoginNotifications] = useState(true);
  const [securityAlerts, setSecurityAlerts] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [originalSettings, setOriginalSettings] = useState<SessionSettings>({
    sessionTimeout: "30",
    loginNotifications: true,
  });
  const [hasChanges, setHasChanges] = useState(false);

  // Device management state
  const [devices, setDevices] = useState<
    (DeviceSession & { isLoggingOut: boolean })[]
  >([]);
  const [isLoadingDevices, setIsLoadingDevices] = useState(true);
  const [isLoggingOutAll, setIsLoggingOutAll] = useState(false);

  // Password form state
  const [isPasswordLoading, setIsPasswordLoading] = useState(false);
  const [passwordSuccess, setPasswordSuccess] = useState<string | null>(null);
  const [passwordError, setPasswordError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);

  // Password form values
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Dialog states
  const [isPasswordDialogOpen, setIsPasswordDialogOpen] = useState(false);
  const [is2FADialogOpen, setIs2FADialogOpen] = useState(false);

  // User password status
  const [userPasswordStatus, setUserPasswordStatus] = useState<{
    hasPassword: boolean;
    isGoogleUser: boolean;
    needsCurrentPassword: boolean;
    provider: string | null;
  } | null>(null);
  const [isCheckingPasswordStatus, setIsCheckingPasswordStatus] =
    useState(true);

  // Mock security score
  const [securityScore, setSecurityScore] = useState(75);

  // Check user password status on component mount
  useEffect(() => {
    const checkPasswordStatus = async () => {
      try {
        setIsCheckingPasswordStatus(true);
        const result = await checkUserPasswordStatus();
        if (result.success) {
          setUserPasswordStatus({
            hasPassword: result.hasPassword,
            isGoogleUser: result.isGoogleUser,
            needsCurrentPassword: result.needsCurrentPassword,
            provider: result.provider,
          });
        }
      } catch (error) {
        console.error("Error checking password status:", error);
      } finally {
        setIsCheckingPasswordStatus(false);
      }
    };

    checkPasswordStatus();
  }, []);

  // Calculate password strength when newPassword changes
  useEffect(() => {
    if (!newPassword) {
      setPasswordStrength(0);
      return;
    }

    let strength = 0;
    if (newPassword.length >= 8) strength += 25; // Length
    if (/[A-Z]/.test(newPassword)) strength += 25; // Uppercase
    if (/[0-9]/.test(newPassword)) strength += 25; // Number
    if (/[^A-Za-z0-9]/.test(newPassword)) strength += 25; // Special char

    setPasswordStrength(strength);
  }, [newPassword]);

  const handlePasswordSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setIsPasswordLoading(true);
    setPasswordSuccess(null);
    setPasswordError(null);

    try {
      // For Google OAuth users, don't send current password
      const passwordData = userPasswordStatus?.needsCurrentPassword
        ? { currentPassword, newPassword, confirmPassword }
        : { currentPassword: "", newPassword, confirmPassword };

      const result = await updatePassword(passwordData);

      if (result.error) {
        setPasswordError(result.error);
      } else {
        const successMessage = userPasswordStatus?.hasPassword
          ? "Password berhasil diperbarui!"
          : "Password berhasil dibuat!";
        setPasswordSuccess(result.success || successMessage);
        setCurrentPassword("");
        setNewPassword("");
        setConfirmPassword("");
        setPasswordStrength(0);
        setTimeout(() => setIsPasswordDialogOpen(false), 2000);
      }
    } catch (err) {
      setPasswordError("Terjadi kesalahan saat memperbarui password.");
      console.error(err);
    } finally {
      setIsPasswordLoading(false);
    }
  };

  const handleResetPassword = () => {
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
    setPasswordSuccess(null);
    setPasswordError(null);
    setPasswordStrength(0);
  };

  // Fetch device sessions and session settings on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch device sessions
        setIsLoadingDevices(true);
        const sessions = await getDeviceSessions();
        setDevices(
          sessions.map((session) => ({ ...session, isLoggingOut: false }))
        );

        // Fetch session settings
        setIsLoadingSettings(true);
        const settingsResult = await getSessionSettings();
        if (settingsResult.success && settingsResult.data) {
          const { sessionTimeout: timeout, loginNotifications: notifications } =
            settingsResult.data;
          setSessionTimeout(timeout);
          setLoginNotifications(notifications);
          setOriginalSettings(settingsResult.data);
        } else if (settingsResult.message) {
          console.error(
            "Error fetching session settings:",
            settingsResult.message
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Gagal memuat data");
      } finally {
        setIsLoadingDevices(false);
        setIsLoadingSettings(false);
      }
    };

    fetchData();
  }, []);

  // Handle logout from a specific device
  const handleLogoutDevice = async (deviceId: string) => {
    // Update device state to show loading
    setDevices((prevDevices) =>
      prevDevices.map((device) =>
        device.id === deviceId ? { ...device, isLoggingOut: true } : device
      )
    );

    try {
      const result = await revokeSession(deviceId);

      if (result.success) {
        if (result.message === "current_session") {
          toast.info(
            "Anda akan logout dari perangkat ini dalam beberapa detik"
          );
          // Wait a moment before logging out to show the toast
          setTimeout(() => {
            signOut({ callbackUrl: "/login" });
          }, 2000);
        } else {
          toast.success("Berhasil logout dari perangkat");
          // Remove the device from the list
          setDevices((prevDevices) =>
            prevDevices.filter((device) => device.id !== deviceId)
          );
        }
      } else {
        toast.error(result.message);
        // Reset the loading state
        setDevices((prevDevices) =>
          prevDevices.map((device) =>
            device.id === deviceId ? { ...device, isLoggingOut: false } : device
          )
        );
      }
    } catch (error) {
      console.error("Error logging out device:", error);
      toast.error("Terjadi kesalahan saat logout dari perangkat");
      // Reset the loading state
      setDevices((prevDevices) =>
        prevDevices.map((device) =>
          device.id === deviceId ? { ...device, isLoggingOut: false } : device
        )
      );
    }
  };

  // Handle logout from all devices
  const handleLogoutAllDevices = async () => {
    setIsLoggingOutAll(true);

    try {
      const result = await revokeAllSessionsIncludingCurrent();

      if (result.success) {
        if (result.message === "all_sessions") {
          toast.info(
            "Anda akan logout dari semua perangkat dalam beberapa detik"
          );
          // Wait a moment before logging out to show the toast
          setTimeout(() => {
            signOut({ callbackUrl: "/login" });
          }, 2000);
        } else {
          toast.success("Berhasil logout dari semua perangkat lain");
          // Refresh the device list
          const sessions = await getDeviceSessions();
          setDevices(
            sessions.map((session) => ({ ...session, isLoggingOut: false }))
          );
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error logging out all devices:", error);
      toast.error("Terjadi kesalahan saat logout dari semua perangkat");
    } finally {
      setIsLoggingOutAll(false);
    }
  };

  // Check for changes in session settings
  useEffect(() => {
    const hasSessionChanges =
      sessionTimeout !== originalSettings.sessionTimeout ||
      loginNotifications !== originalSettings.loginNotifications;

    setHasChanges(hasSessionChanges);
  }, [sessionTimeout, loginNotifications, originalSettings]);

  // Handle session timeout change
  const handleSessionTimeoutChange = (
    e: React.ChangeEvent<HTMLSelectElement>
  ) => {
    setSessionTimeout(e.target.value);
  };

  // Handle login notifications toggle
  const handleLoginNotificationsChange = (checked: boolean) => {
    setLoginNotifications(checked);
  };

  // Save session settings
  const handleSaveSettings = async () => {
    if (!hasChanges) {
      toast.info("Tidak ada perubahan untuk disimpan");
      return;
    }

    setIsLoading(true);

    try {
      // Save session settings to the database
      const result = await updateSessionSettings({
        sessionTimeout,
        loginNotifications,
      });

      if (result.success) {
        // Update original settings to match current settings
        setOriginalSettings({
          sessionTimeout,
          loginNotifications,
        });

        // Show success message
        toast.success(
          result.message || "Pengaturan keamanan berhasil disimpan!"
        );
        setHasChanges(false);
      } else {
        toast.error(result.message || "Gagal menyimpan pengaturan");
      }
    } catch (error) {
      console.error("Error saving session settings:", error);
      toast.error("Terjadi kesalahan saat menyimpan pengaturan");
    } finally {
      setIsLoading(false);
    }
  };

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return "from-green-500 to-emerald-500";
    if (score >= 60) return "from-yellow-500 to-orange-500";
    return "from-red-500 to-pink-500";
  };

  const getSecurityScoreText = (score: number) => {
    if (score >= 80) return "Sangat Aman";
    if (score >= 60) return "Cukup Aman";
    return "Perlu Ditingkatkan";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-blue-900/20 dark:to-indigo-900/20 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header with Security Score */}
        <div className="relative overflow-hidden">
          <Card className="border-0 shadow-2xl bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
            <CardHeader className="pb-8 relative">
              <div className="absolute inset-0 bg-black/10 backdrop-blur-sm"></div>
              <div className="relative z-10">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-16 w-16 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                    <div>
                      <h1 className="text-3xl font-bold mb-2">Keamanan Akun</h1>
                      <p className="text-blue-100 text-lg">
                        Lindungi akun Anda dengan pengaturan keamanan terdepan
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-3 mb-2">
                      <Activity className="h-5 w-5 text-blue-200" />
                      <span className="text-blue-100">Skor Keamanan</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <div className="text-3xl font-bold">{securityScore}%</div>
                        <div className="text-sm text-blue-200">
                          {getSecurityScoreText(securityScore)}
                        </div>
                      </div>
                      <div className="w-16 h-16 relative">
                        <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                          <path
                            className="text-white/20"
                            stroke="currentColor"
                            strokeWidth="3"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          />
                          <path
                            className="text-white"
                            stroke="currentColor"
                            strokeWidth="3"
                            strokeDasharray={`${securityScore}, 100`}
                            strokeLinecap="round"
                            fill="none"
                            d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                          />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Security Settings */}
          <div className="lg:col-span-2 space-y-8">
            {/* Password Management */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                      <Key className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold">
                        {userPasswordStatus?.hasPassword
                          ? "Manajemen Password"
                          : "Buat Password"}
                      </CardTitle>
                      <CardDescription className="text-base">
                        {userPasswordStatus?.isGoogleUser &&
                        !userPasswordStatus?.hasPassword
                          ? "Buat password untuk akun Google Anda"
                          : userPasswordStatus?.hasPassword
                            ? "Perbarui dan kelola password akun Anda"
                            : "Buat password untuk akun Anda"}
                      </CardDescription>
                    </div>
                  </div>
                  <Dialog open={isPasswordDialogOpen} onOpenChange={setIsPasswordDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-6 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
                        <Settings className="h-4 w-4 mr-2" />
                        {userPasswordStatus?.hasPassword ? "Ubah Password" : "Buat Password"}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-2xl bg-white dark:bg-gray-800 rounded-2xl border-0 shadow-2xl">
                      <DialogHeader>
                        <DialogTitle className="text-2xl font-bold flex items-center gap-3">
                          <Key className="h-6 w-6 text-blue-600" />
                          {userPasswordStatus?.hasPassword ? "Ubah Password" : "Buat Password"}
                        </DialogTitle>
                        <DialogDescription className="text-base">
                          {userPasswordStatus?.hasPassword 
                            ? "Perbarui password Anda untuk menjaga keamanan akun"
                            : "Buat password yang kuat untuk melindungi akun Anda"}
                        </DialogDescription>
                      </DialogHeader>

                      {isCheckingPasswordStatus ? (
                        <div className="flex items-center justify-center py-12">
                          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                          <span className="ml-3 text-base text-muted-foreground">
                            Memeriksa status password...
                          </span>
                        </div>
                      ) : (
                        <form onSubmit={handlePasswordSubmit} className="space-y-6">
                          {/* Info message for Google OAuth users */}
                          {userPasswordStatus?.isGoogleUser &&
                            !userPasswordStatus?.hasPassword && (
                              <Alert className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                                <Info className="h-5 w-5 text-blue-600" />
                                <AlertTitle className="text-blue-800 dark:text-blue-200 font-semibold">
                                  Akun Google
                                </AlertTitle>
                                <AlertDescription className="text-blue-700 dark:text-blue-300">
                                  Anda login menggunakan Google. Buat password untuk dapat login dengan email dan password.
                                </AlertDescription>
                              </Alert>
                            )}

                          {passwordSuccess && (
                            <Alert className="border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 rounded-xl animate-in slide-in-from-top-2">
                              <CheckCircle className="h-5 w-5 text-green-600" />
                              <AlertTitle className="text-green-800 dark:text-green-200 font-semibold">
                                Berhasil!
                              </AlertTitle>
                              <AlertDescription className="text-green-700 dark:text-green-300">
                                {passwordSuccess}
                              </AlertDescription>
                            </Alert>
                          )}

                          {passwordError && (
                            <Alert className="border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20 rounded-xl animate-in slide-in-from-top-2">
                              <AlertCircle className="h-5 w-5 text-red-600" />
                              <AlertTitle className="text-red-800 dark:text-red-200 font-semibold">
                                Error
                              </AlertTitle>
                              <AlertDescription className="text-red-700 dark:text-red-300">
                                {passwordError}
                              </AlertDescription>
                            </Alert>
                          )}

                          <div className="space-y-6">
                            {/* Conditionally show current password field */}
                            {userPasswordStatus?.needsCurrentPassword && (
                              <div className="space-y-2">
                                <label htmlFor="current-password" className="block text-sm font-semibold">
                                  Password Saat Ini
                                </label>
                                <div className="relative">
                                  <input
                                    type={showCurrentPassword ? "text" : "password"}
                                    name="current-password"
                                    id="current-password"
                                    value={currentPassword}
                                    onChange={(e) => setCurrentPassword(e.target.value)}
                                    className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 pr-12"
                                    placeholder="Masukkan password saat ini"
                                    required
                                    autoComplete="current-password"
                                  />
                                  <button
                                    type="button"
                                    onClick={() => setShowCurrentPassword((prev) => !prev)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-4 focus:outline-none"
                                  >
                                    {showCurrentPassword ? (
                                      <EyeOff className="h-5 w-5 text-gray-400" />
                                    ) : (
                                      <Eye className="h-5 w-5 text-gray-400" />
                                    )}
                                  </button>
                                </div>
                              </div>
                            )}

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                              <div className="space-y-2">
                                <label htmlFor="new-password" className="block text-sm font-semibold">
                                  Password Baru
                                </label>
                                <div className="relative">
                                  <input
                                    type={showNewPassword ? "text" : "password"}
                                    name="new-password"
                                    id="new-password"
                                    value={newPassword}
                                    onChange={(e) => setNewPassword(e.target.value)}
                                    className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 pr-12"
                                    placeholder="Masukkan password baru"
                                    required
                                    autoComplete="new-password"
                                  />
                                  <button
                                    type="button"
                                    onClick={() => setShowNewPassword((prev) => !prev)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-4 focus:outline-none"
                                  >
                                    {showNewPassword ? (
                                      <EyeOff className="h-5 w-5 text-gray-400" />
                                    ) : (
                                      <Eye className="h-5 w-5 text-gray-400" />
                                    )}
                                  </button>
                                </div>
                              </div>

                              <div className="space-y-2">
                                <label htmlFor="confirm-password" className="block text-sm font-semibold">
                                  Konfirmasi Password Baru
                                </label>
                                <div className="relative">
                                  <input
                                    type={showConfirmPassword ? "text" : "password"}
                                    name="confirm-password"
                                    id="confirm-password"
                                    value={confirmPassword}
                                    onChange={(e) => setConfirmPassword(e.target.value)}
                                    className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:border-blue-500 dark:focus:border-blue-400 transition-all duration-200 pr-12"
                                    placeholder="Konfirmasi password baru"
                                    required
                                    autoComplete="new-password"
                                  />
                                  <button
                                    type="button"
                                    onClick={() => setShowConfirmPassword((prev) => !prev)}
                                    className="absolute inset-y-0 right-0 flex items-center pr-4 focus:outline-none"
                                  >
                                    {showConfirmPassword ? (
                                      <EyeOff className="h-5 w-5 text-gray-400" />
                                    ) : (
                                      <Eye className="h-5 w-5 text-gray-400" />
                                    )}
                                  </button>
                                </div>
                              </div>
                            </div>

                            {/* Password Strength Indicator */}
                            {newPassword && (
                              <div className="space-y-4 p-6 bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl border border-gray-200 dark:border-gray-700">
                                <div className="flex justify-between items-center">
                                  <label className="text-sm font-semibold">
                                    Kekuatan Password:
                                  </label>
                                  <Badge
                                    variant="secondary"
                                    className={cn(
                                      "font-medium px-3 py-1 rounded-full",
                                      passwordStrength < 50
                                        ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300"
                                        : passwordStrength < 75
                                          ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                                          : "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                                    )}
                                  >
                                    {passwordStrength < 50 && "Lemah"}
                                    {passwordStrength >= 50 && passwordStrength < 75 && "Sedang"}
                                    {passwordStrength >= 75 && "Kuat"}
                                  </Badge>
                                </div>
                                <Progress
                                  value={passwordStrength}
                                  className="h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden"
                                />

                                <div className="grid grid-cols-2 gap-4 mt-4">
                                  <div className="flex items-center gap-3">
                                    <div
                                      className={cn(
                                        "w-5 h-5 rounded-full flex items-center justify-center",
                                        newPassword.length >= 8 ? "bg-green-500" : "bg-gray-300"
                                      )}
                                    >
                                      {newPassword.length >= 8 && <CheckCircle className="h-3 w-3 text-white" />}
                                    </div>
                                    <span className="text-sm text-muted-foreground">
                                      Minimal 8 karakter
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div
                                      className={cn(
                                        "w-5 h-5 rounded-full flex items-center justify-center",
                                        /[A-Z]/.test(newPassword) ? "bg-green-500" : "bg-gray-300"
                                      )}
                                    >
                                      {/[A-Z]/.test(newPassword) && <CheckCircle className="h-3 w-3 text-white" />}
                                    </div>
                                    <span className="text-sm text-muted-foreground">
                                      Huruf besar
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div
                                      className={cn(
                                        "w-5 h-5 rounded-full flex items-center justify-center",
                                        /[0-9]/.test(newPassword) ? "bg-green-500" : "bg-gray-300"
                                      )}
                                    >
                                      {/[0-9]/.test(newPassword) && <CheckCircle className="h-3 w-3 text-white" />}
                                    </div>
                                    <span className="text-sm text-muted-foreground">
                                      Angka
                                    </span>
                                  </div>
                                  <div className="flex items-center gap-3">
                                    <div
                                      className={cn(
                                        "w-5 h-5 rounded-full flex items-center justify-center",
                                        /[^A-Za-z0-9]/.test(newPassword) ? "bg-green-500" : "bg-gray-300"
                                      )}
                                    >
                                      {/[^A-Za-z0-9]/.test(newPassword) && <CheckCircle className="h-3 w-3 text-white" />}
                                    </div>
                                    <span className="text-sm text-muted-foreground">
                                      Karakter khusus
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>

                          <DialogFooter className="gap-3">
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleResetPassword}
                              className="px-6 py-2 rounded-xl border-2"
                            >
                              Reset
                            </Button>
                            <Button
                              type="submit"
                              disabled={isPasswordLoading}
                              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                              {isPasswordLoading ? (
                                <>
                                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                  {userPasswordStatus?.hasPassword ? "Memperbarui..." : "Membuat..."}
                                </>
                              ) : (
                                <>
                                  <Key className="h-4 w-4 mr-2" />
                                  {userPasswordStatus?.hasPassword ? "Perbarui Password" : "Buat Password"}
                                </>
                              )}
                            </Button>
                          </DialogFooter>
                        </form>
                      )}
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center gap-4">
                    <Lock className="h-8 w-8 text-blue-600" />
                    <div>
                      <h4 className="font-semibold text-lg">Status Password</h4>
                      <p className="text-sm text-muted-foreground">
                        {userPasswordStatus?.hasPassword 
                          ? "Password aktif dan terlindungi" 
                          : "Belum ada password"}
                      </p>
                    </div>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      "px-4 py-2 rounded-full font-medium",
                      userPasswordStatus?.hasPassword
                        ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                        : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300"
                    )}
                  >
                    {userPasswordStatus?.hasPassword ? "Aktif" : "Belum Diatur"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Two-Factor Authentication */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-green-500 to-teal-600 flex items-center justify-center">
                      <Fingerprint className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <CardTitle className="text-xl font-bold">
                        Autentikasi Dua Faktor
                      </CardTitle>
                      <CardDescription className="text-base">
                        Tambahkan lapisan keamanan ekstra untuk akun Anda
                      </CardDescription>
                    </div>
                  </div>
                  <Dialog open={is2FADialogOpen} onOpenChange={setIs2FADialogOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        className="border-2 border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-300 dark:hover:bg-green-900/20 px-6 py-2 rounded-xl font-semibold"
                      >
                        <Settings className="h-4 w-4 mr-2" />
                        Konfigurasi
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-md bg-white dark:bg-gray-800 rounded-2xl border-0 shadow-2xl">
                      <DialogHeader>
                        <DialogTitle className="text-xl font-bold flex items-center gap-3">
                          <Fingerprint className="h-6 w-6 text-green-600" />
                          Setup 2FA
                        </DialogTitle>
                        <DialogDescription>
                          Fitur autentikasi dua faktor akan segera tersedia
                        </DialogDescription>
                      </DialogHeader>
                      <div className="py-6">
                        <Alert className="border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                          <Info className="h-5 w-5 text-blue-600" />
                          <AlertTitle className="text-blue-800 dark:text-blue-200 font-semibold">
                            Segera Hadir
                          </AlertTitle>
                          <AlertDescription className="text-blue-700 dark:text-blue-300">
                            Fitur 2FA sedang dalam pengembangan dan akan segera tersedia untuk meningkatkan keamanan akun Anda.
                          </AlertDescription>
                        </Alert>
                      </div>
                      <DialogFooter>
                        <Button onClick={() => setIs2FADialogOpen(false)} className="w-full">
                          Mengerti
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="flex items-center justify-between p-6 bg-gradient-to-r from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 rounded-xl border border-green-200 dark:border-green-800">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-3">
                      <Switch
                        checked={twoFactorEnabled}
                        onCheckedChange={setTwoFactorEnabled}
                        className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-green-500 data-[state=checked]:to-teal-500"
                      />
                      <div>
                        <h4 className="font-semibold">Aktifkan 2FA</h4>
                        <p className="text-sm text-muted-foreground">
                          Memerlukan kode verifikasi tambahan saat login
                        </p>
                      </div>
                    </div>
                  </div>
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      "px-4 py-2 rounded-full font-medium",
                      twoFactorEnabled
                        ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300"
                        : "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300"
                    )}
                  >
                    {twoFactorEnabled ? "Aktif" : "Nonaktif"}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Session Settings */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-6">
                <div className="flex items-center gap-4">
                  <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-orange-500 to-red-600 flex items-center justify-center">
                    <Clock className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-xl font-bold">Pengaturan Sesi</CardTitle>
                    <CardDescription className="text-base">
                      Kelola durasi dan perilaku sesi login Anda
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {isLoadingSettings ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Loader2 className="h-8 w-8 animate-spin text-orange-600 mb-4" />
                    <p className="text-base text-muted-foreground">
                      Memuat pengaturan...
                    </p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="space-y-3">
                      <label htmlFor="session-timeout" className="text-sm font-semibold">
                        Batas Waktu Sesi (menit)
                      </label>
                      <select
                        id="session-timeout"
                        value={sessionTimeout}
                        onChange={handleSessionTimeoutChange}
                        className="w-full px-4 py-3 rounded-xl border-2 border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 focus:border-orange-500 dark:focus:border-orange-400 transition-all duration-200"
                      >
                        <option value="15">15 menit</option>
                        <option value="30">30 menit</option>
                        <option value="60">1 jam</option>
                        <option value="120">2 jam</option>
                        <option value="240">4 jam</option>
                        <option value="480">8 jam</option>
                        <option value="1440">24 jam</option>
                      </select>
                      <p className="text-sm text-muted-foreground">
                        Sesi Anda akan berakhir secara otomatis setelah tidak aktif selama periode ini
                      </p>
                    </div>

                    <Separator className="my-6 bg-gradient-to-r from-transparent via-gray-300 to-transparent dark:via-gray-600" />

                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-xl border border-orange-200 dark:border-orange-800">
                        <div className="flex items-center gap-3">
                          <Bell className="h-5 w-5 text-orange-600" />
                          <div>
                            <h4 className="font-semibold">Notifikasi Login</h4>
                            <p className="text-sm text-muted-foreground">
                              Kirim notifikasi email saat ada login baru ke akun Anda
                            </p>
                          </div>
                        </div>
                        <Switch
                          checked={loginNotifications}
                          onCheckedChange={handleLoginNotificationsChange}
                          className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-orange-500 data-[state=checked]:to-red-500"
                        />
                      </div>

                      <div className="flex items-center justify-between p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                        <div className="flex items-center gap-3">
                          <AlertCircle className="h-5 w-5 text-purple-600" />
                          <div>
                            <h4 className="font-semibold">Peringatan Keamanan</h4>
                            <p className="text-sm text-muted-foreground">
                              Dapatkan notifikasi untuk aktivitas mencurigakan
                            </p>
                          </div>
                        </div>
                        <Switch
                          checked={securityAlerts}
                          onCheckedChange={setSecurityAlerts}
                          className="data-[state=checked]:bg-gradient-to-r data-[state=checked]:from-purple-500 data-[state=checked]:to-pink-500"
                        />
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Device Management & Quick Actions */}
          <div className="lg:col-span-1 space-y-8">
            {/* Quick Security Actions */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-bold flex items-center gap-3">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  Aksi Cepat
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-2 border-blue-200 text-blue-700 hover:bg-blue-50 dark:border-blue-800 dark:text-blue-300 dark:hover:bg-blue-900/20 rounded-xl py-3"
                >
                  <History className="h-4 w-4 mr-3" />
                  Riwayat Login
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-2 border-green-200 text-green-700 hover:bg-green-50 dark:border-green-800 dark:text-green-300 dark:hover:bg-green-900/20 rounded-xl py-3"
                >
                  <Globe className="h-4 w-4 mr-3" />
                  Lokasi Login
                </Button>
                <Button 
                  variant="outline" 
                  className="w-full justify-start border-2 border-purple-200 text-purple-700 hover:bg-purple-50 dark:border-purple-800 dark:text-purple-300 dark:hover:bg-purple-900/20 rounded-xl py-3"
                >
                  <Activity className="h-4 w-4 mr-3" />
                  Log Aktivitas
                </Button>
              </CardContent>
            </Card>

            {/* Device Management */}
            <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                    <Laptop className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-lg font-bold">Perangkat Aktif</CardTitle>
                    <CardDescription>
                      {devices.length} perangkat terhubung
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {isLoadingDevices ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <Loader2 className="h-6 w-6 animate-spin text-indigo-600 mb-3" />
                    <p className="text-sm text-muted-foreground">
                      Memuat perangkat...
                    </p>
                  </div>
                ) : devices.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="h-12 w-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
                      <Info className="h-6 w-6 text-gray-400" />
                    </div>
                    <h4 className="font-medium mb-1">Tidak ada perangkat aktif</h4>
                    <p className="text-sm text-muted-foreground">
                      Hanya perangkat ini yang aktif
                    </p>
                  </div>
                ) : (
                  <>
                    <div className="space-y-3 max-h-80 overflow-y-auto thin-scrollbar">
                      {devices.map((device) => (
                        <div
                          key={device.id}
                          className="p-4 bg-gradient-to-r from-gray-50 to-blue-50 dark:from-gray-800 dark:to-blue-900/20 rounded-xl border border-gray-200 dark:border-gray-700 hover:shadow-md transition-all duration-200"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div
                                className={cn(
                                  "h-8 w-8 rounded-lg flex items-center justify-center",
                                  device.type === "mobile" 
                                    ? "bg-purple-100 dark:bg-purple-900/20" 
                                    : device.type === "tablet" 
                                      ? "bg-amber-100 dark:bg-amber-900/20" 
                                      : "bg-blue-100 dark:bg-blue-900/20"
                                )}
                              >
                                {device.type === "mobile" ? (
                                  <Smartphone className="h-4 w-4 text-purple-600 dark:text-purple-400" />
                                ) : device.type === "tablet" ? (
                                  <Tablet className="h-4 w-4 text-amber-600 dark:text-amber-400" />
                                ) : (
                                  <Laptop className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                )}
                              </div>
                              <div className="min-w-0 flex-1">
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium text-sm truncate">
                                    {device.name}
                                  </h4>
                                  {device.isCurrent && (
                                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 text-xs px-2 py-0.5 rounded-full">
                                      Aktif
                                    </Badge>
                                  )}
                                </div>
                                <div className="flex items-center gap-2 mt-1">
                                  <MapPin className="h-3 w-3 text-gray-400" />
                                  <p className="text-xs text-muted-foreground truncate">
                                    {device.isCurrent
                                      ? `Perangkat ini • ${device.lastLogin}`
                                      : device.lastLogin}
                                  </p>
                                </div>
                              </div>
                            </div>
                            <Button
                              variant="outline"
                              size="sm"
                              disabled={device.isLoggingOut || (device.isCurrent && isLoggingOutAll)}
                              className="text-red-600 border-red-200 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20 rounded-lg"
                              onClick={() => handleLogoutDevice(device.id)}
                            >
                              {device.isLoggingOut ? (
                                <Loader2 className="h-3 w-3 animate-spin" />
                              ) : (
                                <LogOut className="h-3 w-3" />
                              )}
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    <Separator className="my-4 bg-gradient-to-r from-transparent via-gray-300 to-transparent dark:via-gray-600" />

                    <Button
                      variant="outline"
                      className="w-full border-2 border-red-200 text-red-700 hover:bg-red-50 dark:border-red-800 dark:text-red-400 dark:hover:bg-red-900/20 rounded-xl py-3 font-semibold"
                      disabled={isLoggingOutAll || devices.length === 0}
                      onClick={handleLogoutAllDevices}
                    >
                      {isLoggingOutAll ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Memproses...
                        </>
                      ) : (
                        <>
                          <LogOut className="h-4 w-4 mr-2" />
                          Logout Semua Perangkat
                        </>
                      )}
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Security Tips */}
            <Card className="border-0 shadow-xl bg-gradient-to-br from-yellow-50 to-orange-100 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg font-bold flex items-center gap-3 text-yellow-800 dark:text-yellow-200">
                  <Info className="h-5 w-5" />
                  Tips Keamanan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-start gap-3">
                  <div className="h-2 w-2 rounded-full bg-yellow-600 mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Gunakan password yang unik dan kuat untuk setiap akun
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="h-2 w-2 rounded-full bg-yellow-600 mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Aktifkan autentikasi dua faktor untuk keamanan ekstra
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="h-2 w-2 rounded-full bg-yellow-600 mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Periksa aktivitas login secara berkala
                  </p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="h-2 w-2 rounded-full bg-yellow-600 mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    Logout dari perangkat yang tidak digunakan
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Save Button */}
        <Card className="border-0 shadow-xl bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
          <CardFooter className="flex justify-end p-6">
            <Button
              type="button"
              onClick={handleSaveSettings}
              disabled={isLoading || !hasChanges}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold px-8 py-3 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Menyimpan...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Simpan Perubahan
                </>
              )}
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}