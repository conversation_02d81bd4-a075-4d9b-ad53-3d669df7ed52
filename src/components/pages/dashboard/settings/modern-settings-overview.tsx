"use client";

import Link from "next/link";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  UserCircle,
  Users,
  Palette,
  Bell,
  CreditCard,
  Receipt,
  ShieldCheck,
  HelpCircle,
  ExternalLink,
  ArrowRight,
  Sparkles,
  LayoutGrid,
} from "lucide-react";

const settingsCategories = [
  {
    title: "Akun & Profil",
    description: "Kelola informasi pribadi dan preferensi akun Anda",
    icon: UserCircle,
    color: "from-blue-500 to-cyan-500",
    items: [
      {
        title: "Profile",
        description: "Informasi pribadi, foto profil, dan kontak",
        href: "/dashboard/settings/profile",
        icon: UserCircle,
      },
      {
        title: "Bis<PERSON>",
        description: "Pengaturan informasi bisnis dan perusa<PERSON>an",
        href: "/dashboard/settings/business",
        icon: LayoutGrid,
      },
      {
        title: "<PERSON><PERSON><PERSON>",
        description: "<PERSON><PERSON><PERSON> tim dan hak akses karyawan",
        href: "/dashboard/settings/employees",
        icon: Users,
        badge: "Owner Only",
      },
    ],
  },
  {
    title: "Preferensi",
    description: "Sesuaikan pengalaman aplikasi sesuai kebutuhan Anda",
    icon: Palette,
    color: "from-purple-500 to-pink-500",
    items: [
      {
        title: "Tampilan",
        description: "Tema, warna, dan preferensi visual",
        href: "/dashboard/settings/appearance",
        icon: Palette,
      },
      {
        title: "Notifikasi",
        description: "Atur pemberitahuan dan alert",
        href: "/dashboard/settings/notifications",
        icon: Bell,
      },
      {
        title: "Redirect Settings",
        description: "Konfigurasi pengalihan URL dan domain",
        href: "/dashboard/settings/redirections",
        icon: ExternalLink,
      },
    ],
  },
  {
    title: "Billing & Langganan",
    description: "Kelola pembayaran, tagihan, dan paket langganan",
    icon: CreditCard,
    color: "from-green-500 to-emerald-500",
    items: [
      {
        title: "Billing",
        description: "Metode pembayaran dan riwayat tagihan",
        href: "/dashboard/settings/billing",
        icon: CreditCard,
      },
      {
        title: "Plan & Tagihan",
        description: "Paket langganan dan upgrade",
        href: "/dashboard/settings/plans",
        icon: Receipt,
        badge: "Popular",
      },
    ],
  },
  {
    title: "Keamanan",
    description: "Lindungi akun Anda dengan pengaturan keamanan",
    icon: ShieldCheck,
    color: "from-red-500 to-orange-500",
    items: [
      {
        title: "Keamanan",
        description: "Password, 2FA, dan sesi aktif",
        href: "/dashboard/settings/security",
        icon: ShieldCheck,
      },
    ],
  },
  {
    title: "Bantuan",
    description: "Dapatkan dukungan dan bantuan teknis",
    icon: HelpCircle,
    color: "from-orange-500 to-yellow-500",
    items: [
      {
        title: "Support",
        description: "Bantuan, dokumentasi, dan kontak",
        href: "/dashboard/settings/support",
        icon: HelpCircle,
      },
    ],
  },
];

export default function ModernSettingsOverview() {
  return (
    <div className="p-6 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex justify-center">
          <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-4 rounded-2xl">
            <Sparkles className="h-8 w-8 text-white" />
          </div>
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Selamat Datang di Pengaturan
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 mt-2">
            Kelola semua aspek akun dan aplikasi Anda dari satu tempat
          </p>
        </div>
      </div>

      <Separator className="my-8" />

      {/* Settings Categories */}
      <div className="space-y-8">
        {settingsCategories.map((category, categoryIndex) => (
          <div key={category.title} className="space-y-4">
            {/* Category Header */}
            <div className="flex items-center gap-4">
              <div className={`bg-gradient-to-r ${category.color} p-3 rounded-xl`}>
                <category.icon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                  {category.title}
                </h2>
                <p className="text-gray-600 dark:text-gray-400">
                  {category.description}
                </p>
              </div>
            </div>

            {/* Category Items */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {category.items.map((item) => (
                <Link key={item.href} href={item.href}>
                  <Card className="group hover:shadow-lg transition-all duration-300 hover:scale-[1.02] border-gray-200 dark:border-gray-700 cursor-pointer">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex items-center gap-3">
                          <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded-lg group-hover:bg-gray-200 dark:group-hover:bg-gray-700 transition-colors">
                            <item.icon className="h-5 w-5 text-gray-600 dark:text-gray-400" />
                          </div>
                          <div>
                            <CardTitle className="text-base font-semibold text-gray-900 dark:text-white">
                              {item.title}
                            </CardTitle>
                            {item.badge && (
                              <Badge 
                                variant="secondary" 
                                className="mt-1 text-xs bg-gradient-to-r from-indigo-500 to-purple-600 text-white"
                              >
                                {item.badge}
                              </Badge>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300 transition-colors" />
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <CardDescription className="text-sm text-gray-500 dark:text-gray-400">
                        {item.description}
                      </CardDescription>
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>

            {categoryIndex < settingsCategories.length - 1 && (
              <Separator className="mt-8" />
            )}
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-12">
        <Card className="border-2 border-dashed border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800/50">
          <CardContent className="p-8 text-center">
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-3 rounded-xl">
                  <HelpCircle className="h-6 w-6 text-white" />
                </div>
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Butuh Bantuan?
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Tim support kami siap membantu Anda 24/7
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button asChild variant="outline">
                  <Link href="/dashboard/settings/support">
                    Hubungi Support
                  </Link>
                </Button>
                <Button asChild>
                  <Link href="/docs">
                    Lihat Dokumentasi
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}