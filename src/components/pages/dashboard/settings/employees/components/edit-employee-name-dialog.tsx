"use client";

import { useState, useEffect } from "react";
import { updateEmployeeName } from "@/actions/entities/employee";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UpdateEmployeeNameSchema } from "@/schemas/zod";
import { toast } from "sonner";
import { Role } from "@prisma/client";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
}

interface EditEmployeeNameDialogProps {
  employee: Employee | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void; // Callback to refresh the employee list
}

export function EditEmployeeNameDialog({
  employee,
  open,
  onOpenChange,
  onSuccess,
}: EditEmployeeNameDialogProps) {
  const [editName, setEditName] = useState("");
  const [editRole, setEditRole] = useState<"ADMIN" | "CASHIER">("CASHIER");
  const [editNameFormErrors, setEditNameFormErrors] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    if (employee) {
      setEditName(employee.name);
      setEditRole(employee.role === Role.ADMIN ? "ADMIN" : "CASHIER");
      setEditNameFormErrors({}); // Clear errors when a new employee is selected
    }
  }, [employee]);

  const resetEditNameForm = () => {
    setEditName("");
    setEditRole("CASHIER");
    setEditNameFormErrors({});
  };

  const handleUpdateEmployeeName = async () => {
    if (!employee) return;

    try {
      // Validate form
      const formData = { name: editName, role: editRole };
      const validationResult = UpdateEmployeeNameSchema.safeParse(formData);

      if (!validationResult.success) {
        const errors: Record<string, string> = {};
        validationResult.error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0].toString()] = err.message;
          }
        });
        setEditNameFormErrors(errors);
        return;
      }

      // Clear previous errors
      setEditNameFormErrors({});

      // Submit form
      const result = await updateEmployeeName(employee.id, formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(result.success);
        onOpenChange(false); // Close dialog
        resetEditNameForm();
        onSuccess(); // Trigger refresh
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat memperbarui data karyawan");
      console.error(err);
    }
  };

  // Reset form when dialog closes
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetEditNameForm();
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Edit Karyawan</DialogTitle>
          <DialogDescription>Perbarui informasi karyawan</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="editName">Nama Karyawan</Label>
            <Input
              id="editName"
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              placeholder="Masukkan nama karyawan"
            />
            {editNameFormErrors.name && (
              <p className="text-sm text-red-500">{editNameFormErrors.name}</p>
            )}
          </div>
          <div className="grid gap-2">
            <Label htmlFor="editRole">Peran</Label>
            <Select
              value={editRole}
              onValueChange={(value: "ADMIN" | "CASHIER") => setEditRole(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih peran" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ADMIN">Admin</SelectItem>
                <SelectItem value="CASHIER">Kasir</SelectItem>
              </SelectContent>
            </Select>
            {editNameFormErrors.role && (
              <p className="text-sm text-red-500">{editNameFormErrors.role}</p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            className="cursor-pointer"
          >
            Batal
          </Button>
          <Button onClick={handleUpdateEmployeeName} className="cursor-pointer">
            Simpan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
