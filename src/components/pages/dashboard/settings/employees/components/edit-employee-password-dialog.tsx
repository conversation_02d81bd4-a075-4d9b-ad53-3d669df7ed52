"use client";

import { useState, useEffect } from "react";
import { updateEmployeePassword } from "@/actions/entities/employee";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  Di<PERSON>Title,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UpdateEmployeePasswordSchema } from "@/schemas/zod";
import { toast } from "sonner";
import { Role } from "@prisma/client"; // Keep Role import if Employee type uses it

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role; // Assuming Employee type is shared or defined elsewhere
}

interface EditEmployeePasswordDialogProps {
  employee: Employee | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void; // Optional success callback
}

export function EditEmployeePasswordDialog({
  employee,
  open,
  onOpenChange,
  onSuccess,
}: EditEmployeePasswordDialogProps) {
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [editPasswordFormErrors, setEditPasswordFormErrors] = useState<
    Record<string, string>
  >({});

  useEffect(() => {
    // Clear form when dialog opens or employee changes
    if (open) {
      resetEditPasswordForm();
    }
  }, [open, employee]);

  const resetEditPasswordForm = () => {
    setNewPassword("");
    setConfirmPassword("");
    setEditPasswordFormErrors({});
  };

  const handleUpdateEmployeePassword = async () => {
    if (!employee) return;

    try {
      // Validate form
      const formData = { password: newPassword, confirmPassword };
      const validationResult = UpdateEmployeePasswordSchema.safeParse(formData);

      if (!validationResult.success) {
        const errors: Record<string, string> = {};
        validationResult.error.errors.forEach((err) => {
          if (err.path[0]) {
            errors[err.path[0].toString()] = err.message;
          }
        });
        setEditPasswordFormErrors(errors);
        return;
      }

      // Clear previous errors
      setEditPasswordFormErrors({});

      // Submit form
      const result = await updateEmployeePassword(employee.id, formData);

      if (result.error) {
        toast.error(result.error);
      } else {
        toast.success(result.success);
        onOpenChange(false); // Close dialog
        resetEditPasswordForm();
        if (onSuccess) onSuccess(); // Call optional success callback
      }
    } catch (err) {
      toast.error("Terjadi kesalahan saat memperbarui password karyawan");
      console.error(err);
    }
  };

  // Reset form when dialog closes
  const handleOpenChange = (isOpen: boolean) => {
    if (!isOpen) {
      resetEditPasswordForm();
    }
    onOpenChange(isOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Ubah Password Karyawan</DialogTitle>
          <DialogDescription>Perbarui password karyawan</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="newPassword">Password Baru</Label>
            <Input
              id="newPassword"
              type="password"
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              placeholder="Masukkan password baru"
            />
            {editPasswordFormErrors.password && (
              <p className="text-sm text-red-500">
                {editPasswordFormErrors.password}
              </p>
            )}
          </div>
          <div className="grid gap-2">
            <Label htmlFor="confirmPassword">Konfirmasi Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              placeholder="Konfirmasi password baru"
            />
            {editPasswordFormErrors.confirmPassword && (
              <p className="text-sm text-red-500">
                {editPasswordFormErrors.confirmPassword}
              </p>
            )}
          </div>
        </div>
        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => handleOpenChange(false)}
            className="cursor-pointer"
          >
            Batal
          </Button>
          <Button
            onClick={handleUpdateEmployeePassword}
            className="cursor-pointer"
          >
            Simpan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
