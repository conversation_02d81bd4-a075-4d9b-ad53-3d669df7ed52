import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Role } from "@prisma/client";
import { Pencil, Trash2, Key, Calendar, UserCircle2 } from "lucide-react";
import { format } from "date-fns";

interface Employee {
  id: string;
  name: string;
  employeeId: string;
  role: Role;
  createdAt?: Date;
}

interface EmployeeTableProps {
  employees: Employee[];
  onEditName: (employee: Employee) => void;
  onEditPassword: (employee: Employee) => void;
  onDelete: (id: string) => void;
}

export function EmployeeTable({
  employees,
  onEditName,
  onEditPassword,
  onDelete,
}: EmployeeTableProps) {
  if (employees.length === 0) {
    return (
      <div className="text-center py-12 px-4">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4">
          <UserCircle2 className="h-8 w-8 text-gray-500 dark:text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-1">
          Belum ada karyawan
        </h3>
        <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
          Belum ada karyawan yang ditambahkan. Klik tombol &quot;Tambah
          Karyawan&quot; untuk menambahkan karyawan baru.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden">
      <Table>
        <TableHeader className="bg-gray-50 dark:bg-gray-800/50">
          <TableRow className="hover:bg-gray-50 dark:hover:bg-gray-800/50">
            <TableHead className="font-medium">Nama</TableHead>
            <TableHead className="font-medium">ID Karyawan</TableHead>
            <TableHead className="font-medium">Peran</TableHead>
            <TableHead className="font-medium">Tanggal Bergabung</TableHead>
            <TableHead className="text-right font-medium">Aksi</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {employees.map((employee) => (
            <TableRow
              key={employee.id}
              className="hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <TableCell className="font-medium">{employee.name}</TableCell>
              <TableCell>{employee.employeeId}</TableCell>
              <TableCell>
                {employee.role === Role.ADMIN ? (
                  <Badge
                    variant="outline"
                    className="bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-800/30"
                  >
                    Admin
                  </Badge>
                ) : (
                  <Badge
                    variant="outline"
                    className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800/30"
                  >
                    Kasir
                  </Badge>
                )}
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1 text-gray-500 dark:text-gray-400 text-sm">
                  <Calendar className="h-3.5 w-3.5" />
                  {employee.createdAt
                    ? format(new Date(employee.createdAt), "dd MMM yyyy")
                    : "-"}
                </div>
              </TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEditName(employee)}
                    title="Edit Karyawan"
                    className="h-8 w-8 text-blue-500 hover:text-blue-600 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer"
                  >
                    <Pencil className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onEditPassword(employee)}
                    title="Ubah Password"
                    className="h-8 w-8 text-amber-500 hover:text-amber-600 hover:bg-amber-50 dark:hover:bg-amber-900/20 cursor-pointer"
                  >
                    <Key className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => onDelete(employee.id)}
                    title="Hapus Karyawan"
                    className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 cursor-pointer"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
