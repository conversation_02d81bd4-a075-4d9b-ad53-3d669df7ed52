"use client";

import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Ta<PERSON>,
  Ta<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from "@/components/ui/tabs";
import { CheckCircle, X, Zap, Rocket, Crown } from "lucide-react";
import { SUBSCRIPTION_PLANS } from "@/lib/subscription";

interface PlanComparisonProps {
  isVisible: boolean;
  onClose: () => void;
}

const planIcons = {
  BASIC: Zap,
  PRO: Rocket,
  ENTERPRISE: Crown,
};

const allFeatures = [
  "Basic Dashboard",
  "User Management",
  "Data Export",
  "Email Support",
  "Advanced Analytics",
  "Priority Support",
  "Custom Integrations",
  "API Access",
  "White Label",
  "Dedicated Manager",
  "Custom Training",
  "SLA Guarantee",
];

export default function PlanComparison({ isVisible, onClose }: PlanComparisonProps) {
  const [activeTab, setActiveTab] = useState("features");

  if (!isVisible) return null;

  return (
    <Card className="w-full max-w-6xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-2xl font-bold">Compare Plans</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="limits">Limits & Usage</TabsTrigger>
          </TabsList>

          <TabsContent value="features" className="mt-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-semibold">Features</th>
                    {Object.entries(SUBSCRIPTION_PLANS).map(([planKey, plan]) => {
                      const IconComponent = planIcons[planKey as keyof typeof planIcons];
                      return (
                        <th key={planKey} className="text-center p-4 min-w-[150px]">
                          <div className="flex flex-col items-center gap-2">
                            <div className="h-8 w-8 rounded-lg bg-primary/10 flex items-center justify-center">
                              <IconComponent className="h-4 w-4 text-primary" />
                            </div>
                            <span className="font-semibold">{plan.name}</span>
                            <Badge variant="outline" className="text-xs">
                              {plan.price === 0 ? "Free" : `$${plan.price}`}
                            </Badge>
                          </div>
                        </th>
                      );
                    })}
                  </tr>
                </thead>
                <tbody>
                  {allFeatures.map((feature, index) => (
                    <tr key={index} className="border-b hover:bg-muted/50">
                      <td className="p-4 font-medium">{feature}</td>
                      {Object.entries(SUBSCRIPTION_PLANS).map(([planKey, plan]) => (
                        <td key={planKey} className="text-center p-4">
                          {plan.features.includes(feature) ? (
                            <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-muted-foreground mx-auto" />
                          )}
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </TabsContent>

          <TabsContent value="limits" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {Object.entries(SUBSCRIPTION_PLANS).map(([planKey, plan]) => {
                const IconComponent = planIcons[planKey as keyof typeof planIcons];
                return (
                  <Card key={planKey} className="border-2">
                    <CardHeader className="text-center">
                      <div className="h-12 w-12 rounded-xl bg-primary/10 flex items-center justify-center mx-auto mb-2">
                        <IconComponent className="h-6 w-6 text-primary" />
                      </div>
                      <CardTitle>{plan.name}</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm">Users</span>
                          <span className="font-semibold">
                            {planKey === "BASIC" ? "5" : planKey === "PRO" ? "25" : "Unlimited"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Storage</span>
                          <span className="font-semibold">
                            {planKey === "BASIC" ? "10 GB" : planKey === "PRO" ? "100 GB" : "1 TB"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">API Calls</span>
                          <span className="font-semibold">
                            {planKey === "BASIC" ? "1K/month" : planKey === "PRO" ? "10K/month" : "Unlimited"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Support</span>
                          <span className="font-semibold">
                            {planKey === "BASIC" ? "Email" : planKey === "PRO" ? "Priority" : "24/7 Phone"}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}