"use client";

import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Crown, Rocket, Zap } from "lucide-react";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  <PERSON><PERSON>ip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { SubscriptionPlan } from "@prisma/client";

interface PlanCardProps {
  planKey: SubscriptionPlan;
  planDetails: {
    name: string;
    price: string;
    originalPrice?: number | null;
    savings?: number | null;
    discountPercentage?: number | null;
    features: string[];
    limitations: string[];
  };
  isCurrentPlan: boolean;
  isAnnual: boolean;
  isProcessing: boolean;
  processingPlan: SubscriptionPlan | null;
  onSubscribe: (plan: SubscriptionPlan, isAnnual: boolean) => void;
  formatCurrency: (amount: number) => string;
  getDiscountBadgeText: (percentage: number) => string;
}

const planIcons = {
  BASIC: Zap,
  PRO: Rocket,
  ENTERPRISE: Crown,
};

const planColors = {
  BASIC: {
    gradient: "from-blue-500 to-cyan-500",
    bg: "bg-blue-50",
    text: "text-blue-700",
    border: "border-blue-200",
    icon: "text-blue-600",
  },
  PRO: {
    gradient: "from-orange-500 to-red-500",
    bg: "bg-orange-50",
    text: "text-orange-700",
    border: "border-orange-200",
    icon: "text-orange-600",
  },
  ENTERPRISE: {
    gradient: "from-purple-500 to-pink-500",
    bg: "bg-purple-50",
    text: "text-purple-700",
    border: "border-purple-200",
    icon: "text-purple-600",
  },
};

const planLabels = {
  BASIC: "Starter",
  PRO: "Popular",
  ENTERPRISE: "Premium",
};

export default function PlanCard({
  planKey,
  planDetails,
  isCurrentPlan,
  isAnnual,
  isProcessing,
  processingPlan,
  onSubscribe,
  formatCurrency,
  getDiscountBadgeText,
}: PlanCardProps) {
  const IconComponent = planIcons[planKey];
  const colors = planColors[planKey];
  const label = planLabels[planKey];

  return (
    <Card
      className={`relative overflow-hidden transition-all duration-300 hover:scale-[1.02] hover:shadow-xl ${
        isCurrentPlan
          ? `ring-2 ring-offset-2 ring-offset-background bg-gradient-to-br ${colors.gradient} text-white`
          : "hover:shadow-lg border-2 hover:border-primary/20"
      }`}
    >
      {/* Plan Label */}
      <div
        className={`absolute top-0 right-0 left-0 text-center py-2 text-xs font-semibold ${
          isCurrentPlan
            ? "bg-white/20 text-white"
            : `bg-gradient-to-r ${colors.gradient} text-white`
        }`}
      >
        {isCurrentPlan ? "Current Plan" : label}
      </div>

      <CardHeader className="pt-12 pb-4">
        <div className="flex items-center justify-between mb-4">
          <div
            className={`p-3 rounded-xl ${
              isCurrentPlan ? "bg-white/20" : colors.bg
            }`}
          >
            <IconComponent
              className={`h-6 w-6 ${isCurrentPlan ? "text-white" : colors.icon}`}
            />
          </div>
          {planKey === "PRO" && !isCurrentPlan && (
            <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white border-0">
              <Sparkles className="h-3 w-3 mr-1" />
              Most Popular
            </Badge>
          )}
        </div>

        <CardTitle
          className={`text-2xl font-bold ${
            isCurrentPlan ? "text-white" : "text-foreground"
          }`}
        >
          {planDetails.name}
        </CardTitle>

        <CardDescription
          className={`${
            isCurrentPlan ? "text-white/80" : "text-muted-foreground"
          }`}
        >
          Perfect for {planKey === "BASIC" ? "small teams" : planKey === "PRO" ? "growing businesses" : "large enterprises"}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Pricing */}
        <div className="space-y-2">
          {isAnnual && planDetails.originalPrice && (
            <div className="flex items-center gap-2">
              <span
                className={`text-lg line-through ${
                  isCurrentPlan ? "text-white/60" : "text-muted-foreground"
                }`}
              >
                {formatCurrency(planDetails.originalPrice)}
              </span>
              <Badge
                variant="secondary"
                className="bg-green-100 text-green-700 text-xs"
              >
                {getDiscountBadgeText(planDetails.discountPercentage!)}
              </Badge>
            </div>
          )}

          <div className="flex items-baseline gap-1">
            <span
              className={`text-4xl font-bold ${
                isCurrentPlan ? "text-white" : "text-foreground"
              }`}
            >
              {planDetails.price.split("/")[0]}
            </span>
            {planDetails.price.includes("/") && (
              <span
                className={`text-sm ${
                  isCurrentPlan ? "text-white/80" : "text-muted-foreground"
                }`}
              >
                /{planDetails.price.split("/")[1]}
              </span>
            )}
          </div>

          {isAnnual && planDetails.savings && (
            <p
              className={`text-sm font-medium ${
                isCurrentPlan ? "text-green-200" : "text-green-600"
              }`}
            >
              Save {formatCurrency(planDetails.savings)} per year
            </p>
          )}
        </div>

        <Separator className={isCurrentPlan ? "bg-white/20" : ""} />

        {/* Features */}
        <div className="space-y-3">
          <h4
            className={`font-semibold text-sm ${
              isCurrentPlan ? "text-white" : "text-foreground"
            }`}
          >
            What's included:
          </h4>
          <ul className="space-y-2">
            {planDetails.features.slice(0, 5).map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                <CheckCircle
                  className={`h-4 w-4 mt-0.5 flex-shrink-0 ${
                    isCurrentPlan ? "text-green-200" : "text-green-500"
                  }`}
                />
                <span
                  className={`text-sm ${
                    isCurrentPlan ? "text-white/90" : "text-foreground"
                  }`}
                >
                  {feature}
                </span>
              </li>
            ))}
          </ul>

          {planDetails.features.length > 5 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={`text-xs ${
                      isCurrentPlan
                        ? "text-white/80 hover:text-white hover:bg-white/10"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    +{planDetails.features.length - 5} more features
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="space-y-1">
                    {planDetails.features.slice(5).map((feature, index) => (
                      <p key={index} className="text-xs">
                        • {feature}
                      </p>
                    ))}
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </CardContent>

      <CardFooter className="pt-6">
        <Button
          variant={isCurrentPlan ? "secondary" : "default"}
          size="lg"
          className={`w-full font-semibold transition-all duration-200 ${
            isCurrentPlan
              ? "bg-white text-gray-900 hover:bg-white/90"
              : `bg-gradient-to-r ${colors.gradient} hover:shadow-lg hover:scale-105 text-white border-0`
          }`}
          disabled={isCurrentPlan || isProcessing}
          onClick={() => onSubscribe(planKey, isAnnual)}
        >
          {processingPlan === planKey ? (
            <div className="flex items-center gap-2">
              <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
              Processing...
            </div>
          ) : isCurrentPlan ? (
            "Current Plan"
          ) : (
            `Choose ${planDetails.name}`
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}