"use client";

import { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { cn } from "@/lib/utils";
import { Role } from "@prisma/client";
import { PermissionCheck } from "@/components/auth/permission-check";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card, CardContent } from "@/components/ui/card";
import {
  UserCircle,
  Users,
  Palette,
  Bell,
  CreditCard,
  Receipt,
  ShieldCheck,
  HelpCircle,
  ExternalLink,
  Menu,
  Settings,
  ChevronRight,
  LayoutGrid,
} from "lucide-react";

interface ModernSettingsLayoutProps {
  children: React.ReactNode;
}

interface SettingsNavItem {
  title: string;
  href: string;
  icon: React.ElementType;
  roles?: Role[];
  description: string;
  category: "account" | "preferences" | "billing" | "security" | "support";
}

const settingsNavItems: SettingsNavItem[] = [
  {
    title: "Profile",
    href: "/dashboard/settings/profile",
    icon: UserCircle,
    description: "Kelola informasi pribadi dan preferensi akun",
    category: "account",
  },
  {
    title: "Tampilan",
    href: "/dashboard/settings/appearance",
    icon: Palette,
    description: "Sesuaikan tema dan tampilan aplikasi",
    category: "preferences",
  },
  {
    title: "Notifikasi",
    href: "/dashboard/settings/notifications",
    icon: Bell,
    description: "Atur preferensi notifikasi dan pemberitahuan",
    category: "preferences",
  },
  {
    title: "Billing",
    href: "/dashboard/settings/billing",
    icon: CreditCard,
    description: "Kelola metode pembayaran dan riwayat tagihan",
    category: "billing",
  },
  {
    title: "Plan & Tagihan",
    href: "/dashboard/settings/plans",
    icon: Receipt,
    description: "Lihat dan upgrade paket langganan Anda",
    category: "billing",
  },
  {
    title: "Redirect Settings",
    href: "/dashboard/settings/redirections",
    icon: ExternalLink,
    description: "Konfigurasi pengalihan URL dan domain",
    category: "preferences",
  },
  {
    title: "Keamanan",
    href: "/dashboard/settings/security",
    icon: ShieldCheck,
    description: "Kelola keamanan akun dan autentikasi",
    category: "security",
  },
  {
    title: "Karyawan",
    href: "/dashboard/settings/employees",
    icon: Users,
    description: "Kelola tim dan hak akses karyawan",
    category: "account",
    roles: [Role.OWNER],
  },
  {
    title: "Bisnis",
    href: "/dashboard/settings/business",
    icon: LayoutGrid,
    description: "Pengaturan informasi bisnis dan perusahaan",
    category: "account",
  },
  {
    title: "Support",
    href: "/dashboard/settings/support",
    icon: HelpCircle,
    description: "Bantuan, dokumentasi, dan dukungan teknis",
    category: "support",
  },
];

const categoryLabels = {
  account: "Akun & Profil",
  preferences: "Preferensi",
  billing: "Billing & Langganan",
  security: "Keamanan",
  support: "Bantuan",
};

const categoryColors = {
  account: "bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300",
  preferences:
    "bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300",
  billing:
    "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300",
  security: "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300",
  support:
    "bg-orange-50 dark:bg-orange-900/20 text-orange-700 dark:text-orange-300",
};

export default function ModernSettingsLayout({
  children,
}: ModernSettingsLayoutProps) {
  const pathname = usePathname();
  const [activeItem, setActiveItem] = useState("");
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const matchingItem = settingsNavItems.find((item) =>
      pathname.includes(item.href)
    );

    if (matchingItem) {
      setActiveItem(matchingItem.href);
    } else if (pathname === "/dashboard/settings") {
      setActiveItem("/dashboard/settings/profile");
    }
  }, [pathname]);

  const groupedItems = settingsNavItems.reduce(
    (acc, item) => {
      if (!acc[item.category]) {
        acc[item.category] = [];
      }
      acc[item.category].push(item);
      return acc;
    },
    {} as Record<string, SettingsNavItem[]>
  );

  const SettingsNavigation = () => (
    <div className="space-y-6">
      <div className="px-4">
        <div className="flex items-center gap-3 mb-6">
          <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-2 rounded-xl">
            <Settings className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Pengaturan
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Kelola preferensi Anda
            </p>
          </div>
        </div>
      </div>

      <ScrollArea className="h-[calc(100vh-12rem)] px-2">
        <div className="space-y-6">
          {Object.entries(groupedItems).map(([category, items]) => (
            <div key={category} className="space-y-2">
              <div className="px-2">
                <Badge
                  variant="secondary"
                  className={cn(
                    "text-xs font-medium px-2 py-1 rounded-md",
                    categoryColors[category as keyof typeof categoryColors]
                  )}
                >
                  {categoryLabels[category as keyof typeof categoryLabels]}
                </Badge>
              </div>

              <div className="space-y-1">
                {items.map((item) => {
                  const NavItemContent = (
                    <Link
                      href={item.href}
                      className={cn(
                        "group flex items-center gap-3 px-3 py-3 mx-2 rounded-xl transition-all duration-200 hover:scale-[1.02]",
                        activeItem === item.href
                          ? "bg-gradient-to-r from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/25"
                          : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
                      )}
                      onClick={() => {
                        setActiveItem(item.href);
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <div
                        className={cn(
                          "p-2 rounded-lg transition-colors",
                          activeItem === item.href
                            ? "bg-white/20"
                            : "bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700"
                        )}
                      >
                        <item.icon className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">
                          {item.title}
                        </div>
                        <div
                          className={cn(
                            "text-xs truncate mt-0.5",
                            activeItem === item.href
                              ? "text-white/80"
                              : "text-gray-500 dark:text-gray-400"
                          )}
                        >
                          {item.description}
                        </div>
                      </div>
                      <ChevronRight
                        className={cn(
                          "h-4 w-4 transition-transform",
                          activeItem === item.href ? "rotate-90" : ""
                        )}
                      />
                    </Link>
                  );

                  if (item.roles) {
                    return (
                      <PermissionCheck
                        key={item.href}
                        requiredRoles={item.roles}
                      >
                        {NavItemContent}
                      </PermissionCheck>
                    );
                  }

                  return <div key={item.href}>{NavItemContent}</div>;
                })}
              </div>

              {category !== "support" && (
                <div className="px-4">
                  <Separator className="my-2" />
                </div>
              )}
            </div>
          ))}
        </div>
      </ScrollArea>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Mobile Header */}
        <div className="lg:hidden mb-6">
          <Card className="border-0 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="bg-gradient-to-br from-indigo-500 to-purple-600 p-2 rounded-lg">
                    <Settings className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-lg font-bold text-gray-900 dark:text-white">
                      Pengaturan
                    </h1>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Kelola preferensi Anda
                    </p>
                  </div>
                </div>
                <Sheet
                  open={isMobileMenuOpen}
                  onOpenChange={setIsMobileMenuOpen}
                >
                  <SheetTrigger asChild>
                    <Button variant="outline" size="icon" className="lg:hidden">
                      <Menu className="h-5 w-5" />
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="left" className="w-80 p-0">
                    <SheetHeader className="p-6 border-b">
                      <SheetTitle>Menu Pengaturan</SheetTitle>
                      <SheetDescription>
                        Pilih kategori pengaturan yang ingin Anda kelola
                      </SheetDescription>
                    </SheetHeader>
                    <SettingsNavigation />
                  </SheetContent>
                </Sheet>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-10 gap-6">
          {/* Desktop Sidebar */}
          <div className="hidden lg:block lg:col-span-3">
            <Card className="border-0 shadow-sm sticky top-6">
              <CardContent className="p-0">
                <SettingsNavigation />
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-7">
            <Card className="border-0 shadow-sm min-h-[600px]">
              <CardContent className="p-0">
                <div className="h-full">{children}</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
