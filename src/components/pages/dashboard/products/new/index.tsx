"use client";

import React, { useEffect, useTransition, useState } from "react";
// Link is no longer needed
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { addProduct } from "@/actions/entities/products";
import { useProductLimits } from "@/hooks/useSubscriptionLimits";

import DashboardLayout from "@/components/layout/dashboardlayout";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

import { EnhancedProductSchema } from "./types";
import { ProductFormValues } from "./types";
import ProductFormTabs from "./components/ProductFormTabs";
import { ArrowLeft, Check, Save, PackagePlus, AlertCircle } from "lucide-react";
import { useUnsavedChangesWarning } from "@/hooks/useUnsavedChangesWarning";
import {
  deleteProductImage,
  uploadProductImage,
} from "@/actions/uploads/images";

const EnhancedAddProductPage: React.FC = () => {
  const router = useRouter();
  // We don't need pathname for this component
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isPending, startTransition] = useTransition();
  const [imageUrl, setImageUrl] = React.useState<string>("");
  const [isUploading, setIsUploading] = React.useState(false);
  const [previewUrl, setPreviewUrl] = React.useState<string>("");
  const [fileInputKey, setFileInputKey] = React.useState<number>(0);
  const fileInputRef = React.useRef<HTMLInputElement | null>(null);
  const [isFormValid, setIsFormValid] = useState(false);

  const {
    canCreateProduct,
    productMessage,
    currentProductUsage,
    productLimit,
    isLoading: limitsLoading,
  } = useProductLimits();

  // Initialize the form with enhanced schema
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(EnhancedProductSchema),
    defaultValues: {
      name: "",
      description: "",
      sku: "",
      price: 0,
      wholesalePrice: undefined,
      cost: 0,
      stock: 0,
      image: "",
      categoryId: "",
      barcode: "",
      salePriceTaxRate: 0,
      wholesalePriceTaxRate: 0,
      costPriceTaxRate: 0,
      hasVariants: false,
      trackInventory: true,
      minStockLevel: 0,
      weight: 0,
      length: 0,
      width: 0,
      height: 0,
      unit: "Pcs",
      unitId: "",
      tags: [],
      colorVariants: [],
      isDraft: false,
    },
  });

  // Handle image deletion
  const handleImageDelete = async (imageUrl: string) => {
    try {
      const result = await deleteProductImage(imageUrl);
      if (result.success) {
        toast.success("Gambar berhasil dihapus");
        setImageUrl("");
        setPreviewUrl("");
      } else {
        toast.error(result.error || "Gagal menghapus gambar");
      }
    } catch (error) {
      console.error("Delete Error:", error);
      toast.error("Gagal menghapus gambar");
    }
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Create a preview URL for the selected image
    const objectUrl = URL.createObjectURL(file);
    setPreviewUrl(objectUrl);

    // Upload the image to Vercel Blob
    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadProductImage(formData);

      if (result.success && result.url) {
        setImageUrl(result.url);
        form.setValue("image", result.url);
        toast.success("Gambar berhasil diunggah");
      } else {
        toast.error(result.error || "Gagal mengunggah gambar");
        // Clear the file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      }
    } catch (error) {
      console.error("Upload Error:", error);
      toast.error("Gagal mengunggah gambar");
    } finally {
      setIsUploading(false);
    }
  };

  // Function to highlight invalid fields with red borders
  const highlightInvalidFields = (fieldNames: string[]) => {
    fieldNames.forEach((fieldName) => {
      // Special handling for categoryId field
      if (fieldName === "categoryId") {
        // Find the category combobox button within the categoryId form field
        const categoryFormField = document.querySelector(
          '[data-field="categoryId"]'
        );
        if (categoryFormField) {
          const comboboxButton = categoryFormField.querySelector(
            'button[role="combobox"]'
          );
          if (comboboxButton) {
            comboboxButton.classList.add(
              "border-red-500",
              "ring-1",
              "ring-red-500"
            );
          }
        }
        return;
      }

      // Standard handling for other input fields
      const inputElement = document.querySelector(
        `[name="${fieldName}"]`
      ) as HTMLElement;
      if (inputElement) {
        // Find the closest parent with form-control class and add red border
        const formControl =
          inputElement.closest(".form-control") || inputElement.parentElement;
        if (formControl) {
          formControl.classList.add("border-red-500", "ring-1", "ring-red-500");
        }
      }
    });
  };

  const onSubmit = (values: ProductFormValues) => {
    // Check subscription limits before submission
    if (!canCreateProduct) {
      toast.error(productMessage || "Batas produk tercapai untuk paket Anda.");
      return;
    }

    // Reset any previous validation styling
    document.querySelectorAll(".border-red-500").forEach((el) => {
      el.classList.remove("border-red-500", "ring-1", "ring-red-500");
    });

    // Also reset any combobox buttons that might have validation styling
    document.querySelectorAll('button[role="combobox"]').forEach((el) => {
      el.classList.remove("border-red-500", "ring-1", "ring-red-500");
    });

    // Track invalid fields
    const invalidFields: string[] = [];

    // Validate required fields
    if (!values.name) {
      toast.error("Nama Produk wajib diisi");
      invalidFields.push("name");
    }

    if (!values.categoryId) {
      toast.error("Kategori Produk wajib diisi");
      invalidFields.push("categoryId");
    }

    if (!values.price || values.price <= 0) {
      toast.error("Harga Jual wajib diisi dengan nilai lebih dari 0");
      invalidFields.push("price");
    }

    // If there are invalid fields, highlight them and stop submission
    if (invalidFields.length > 0) {
      highlightInvalidFields(invalidFields);
      return;
    }

    startTransition(async () => {
      try {
        // Include all necessary fields including categoryId and variants
        const productData = {
          name: values.name,
          description: values.description,
          sku: values.sku,
          price: values.price,
          wholesalePrice: values.wholesalePrice,
          cost: values.cost,
          stock: values.stock,
          image: values.image,
          categoryId: values.categoryId,
          barcode: values.barcode,
          salePriceTaxRate: values.salePriceTaxRate,
          wholesalePriceTaxRate: values.wholesalePriceTaxRate,
          costPriceTaxRate: values.costPriceTaxRate,
          hasVariants: values.hasVariants,
          trackInventory: values.trackInventory,
          minStockLevel: values.minStockLevel,
          weight: values.weight,
          length: values.length,
          width: values.width,
          height: values.height,
          unit: values.unit,
          unitId: values.unitId, // Add unitId field
          tags: values.tags,
          isDraft: false, // Always set to false when publishing
          colorVariants: values.hasVariants ? values.colorVariants || [] : [],
        };

        const result = await addProduct(productData);
        if (result.success) {
          toast.success(result.success);
          // Reset form and state
          form.reset();
          setImageUrl("");
          setPreviewUrl("");
          // Reset file input by incrementing the key
          setFileInputKey((prev) => prev + 1);
          // Redirect to product detail page
          if (result.productId) {
            router.push(`/dashboard/products/detail/${result.productId}`);
          } else {
            router.push("/dashboard/products");
          }
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Watch form values for summary and validation
  const formValues = form.watch();

  // Listen for unitSelected event from UnitCombobox
  useEffect(() => {
    const handleUnitSelected = (event: any) => {
      const { name } = event.detail;
      if (name) {
        form.setValue("unit", name);
      }
    };

    window.addEventListener("unitSelected", handleUnitSelected);

    return () => {
      window.removeEventListener("unitSelected", handleUnitSelected);
    };
  }, [form]);

  // Check form validity
  useEffect(() => {
    const isValid = form.formState.isValid;
    setIsFormValid(isValid);
  }, [form.formState.isValid]);

  // Check for unsaved changes
  useEffect(() => {
    // Consider form has unsaved changes if any meaningful data is entered
    const hasChanges = !!(
      formValues.name ||
      formValues.price > 0 ||
      formValues.description ||
      formValues.image
    );
    setHasUnsavedChanges(hasChanges);
  }, [formValues]);

  // Use the unsaved changes warning hook
  const {
    showExitDialog,
    setShowExitDialog,
    handleNavigation,
    confirmNavigation,
    cancelNavigation,
  } = useUnsavedChangesWarning(hasUnsavedChanges);

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Set isDraft to true and include color variants if hasVariants is true
        const productData = {
          ...formValues,
          isDraft: true,
          unit: formValues.unit,
          unitId: formValues.unitId, // Add unitId field
          colorVariants: formValues.hasVariants
            ? formValues.colorVariants || []
            : [],
        };

        const result = await addProduct(productData);
        if (result.success) {
          toast.success("Produk berhasil disimpan sebagai draft!");
          // Redirect to product detail page
          if (result.productId) {
            router.push(`/dashboard/products/detail/${result.productId}`);
          } else {
            router.push("/dashboard/products");
          }
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <DashboardLayout>
      <div className="w-full px-4">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <PackagePlus className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold">Tambah Produk Baru</h1>
          </div>
          <Button
            variant="outline"
            className="gap-2 cursor-pointer"
            onClick={() => handleNavigation("/dashboard/products")}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Button>
        </div>

        {/* Subscription Limit Warning */}
        {productLimit && currentProductUsage !== undefined && (
          <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950 mb-6">
            <CardContent className="pt-0">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-orange-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-1">
                  <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                    Batas Produk
                  </p>
                  <p className="text-sm text-orange-700 dark:text-orange-300">
                    Anda telah menggunakan {currentProductUsage} dari{" "}
                    {productLimit} produk yang tersedia.
                    {productLimit - currentProductUsage <= 5 && (
                      <span className="font-medium">
                        {" "}
                        Sisa {productLimit - currentProductUsage} produk lagi.
                      </span>
                    )}
                  </p>
                  {productLimit - currentProductUsage <= 0 && (
                    <p className="text-sm text-orange-800 dark:text-orange-200 font-medium">
                      Upgrade paket Anda untuk menambah lebih banyak produk.
                    </p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="w-full">
              {/* Main Form Content */}
              <ProductFormTabs
                control={form.control}
                isPending={isPending}
                handleImageUpload={handleImageUpload}
                isUploading={isUploading}
                previewUrl={previewUrl}
                fileInputRef={fileInputRef}
                fileInputKey={fileInputKey}
                onDeleteImage={handleImageDelete}
              />
            </div>

            {/* Form Actions */}
            <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                disabled={isPending}
                className="cursor-pointer"
                onClick={() => handleNavigation("/dashboard/products")}
              >
                Batal
              </Button>
              <Button
                type="button"
                variant="secondary"
                disabled={isPending || !isFormValid}
                className="gap-2 cursor-pointer"
                onClick={saveAsDraft}
              >
                <Save className="h-4 w-4" />
                <span>Simpan ke Draft</span>
              </Button>
              <Button
                type="submit"
                disabled={isPending}
                className="gap-2 cursor-pointer"
              >
                {isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>Menyimpan...</span>
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4" />
                    <span>Publish Produk</span>
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>

      {/* Unsaved Changes Dialog */}
      <AlertDialog open={showExitDialog} onOpenChange={setShowExitDialog}>
        <AlertDialogContent className="w-[95%] max-w-md md:max-w-xl lg:max-w-2xl">
          <AlertDialogHeader>
            <AlertDialogTitle>Perubahan Belum Tersimpan</AlertDialogTitle>
            <AlertDialogDescription>
              Anda memiliki perubahan yang belum tersimpan. Jika Anda
              meninggalkan halaman ini, perubahan Anda akan hilang.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-2 md:justify-end">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 w-full">
              <AlertDialogCancel
                onClick={cancelNavigation}
                className="cursor-pointer w-full"
              >
                Kembali ke Form
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={confirmNavigation}
                className="cursor-pointer bg-red-500 hover:bg-red-600 w-full"
              >
                Buang Perubahan
              </AlertDialogAction>
              <Button
                type="button"
                variant="default"
                className="cursor-pointer w-full"
                onClick={() => {
                  saveAsDraft();
                  setShowExitDialog(false);
                }}
              >
                <Save className="h-4 w-4 mr-2" />
                Simpan ke Draft
              </Button>
            </div>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </DashboardLayout>
  );
};

export default EnhancedAddProductPage;
