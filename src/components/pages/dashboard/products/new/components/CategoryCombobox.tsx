"use client";

import * as React from "react";
import { Check, ChevronsUpDown, Plus } from "lucide-react";
import { toast } from "sonner";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { addCategory } from "@/actions/entities/categories";
import { FormControl } from "@/components/ui/form";
import { ControllerRenderProps } from "react-hook-form";
import { ProductFormValues } from "../types";
import CategoryManagementDialog from "./CategoryManagementDialog";

interface CategoryComboboxProps {
  categories: { id: string; name: string }[];
  isLoadingCategories: boolean;
  onCategoryAdded: (category: { id: string; name: string }) => void;
  onCategoriesUpdated: () => void;
  field: ControllerRenderProps<ProductFormValues, "categoryId">;
  isPending: boolean;
}

export function CategoryCombobox({
  categories,
  isLoadingCategories,
  onCategoryAdded,
  onCategoriesUpdated,
  field,
  isPending,
}: CategoryComboboxProps) {
  const [open, setOpen] = React.useState(false);
  const [inputValue, setInputValue] = React.useState("");
  const [isAddingCategory, setIsAddingCategory] = React.useState(false);
  const [showAllCategories, setShowAllCategories] = React.useState(false);

  const handleAddCategory = async () => {
    if (!inputValue.trim()) {
      toast.error("Nama kategori tidak boleh kosong");
      return;
    }

    // Check for duplicate category in current user's categories
    const trimmedInput = inputValue.trim();
    const existingCategory = categories.find(
      (category) => category.name.toLowerCase() === trimmedInput.toLowerCase()
    );

    if (existingCategory) {
      toast.error(
        `Kategori "${trimmedInput}" sudah ada! Silakan pilih dari daftar atau gunakan nama yang berbeda.`
      );
      return;
    }

    setIsAddingCategory(true);
    try {
      const result = await addCategory({ name: trimmedInput });

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.category) {
        toast.success(result.success);
        onCategoryAdded(result.category);
        field.onChange(result.category.id);
        setOpen(false);
        setInputValue("");
      }
    } catch (error) {
      toast.error("Terjadi kesalahan saat menambahkan kategori");
      console.error(error);
    } finally {
      setIsAddingCategory(false);
    }
  };

  const handleInputChange = (value: string) => {
    setInputValue(value);
    // Show all categories when searching, otherwise show only recent ones
    setShowAllCategories(value.trim().length > 0);
  };

  const handleSelect = (currentValue: string) => {
    console.log("Selected category ID:", currentValue);
    // Always set the selected value without toggling
    field.onChange(currentValue);
    setOpen(false);
    setInputValue("");
  };

  return (
    <Popover
      open={open}
      onOpenChange={(isOpen) => {
        setOpen(isOpen);
        // Clear input value and reset view when closing the popover
        if (!isOpen) {
          setInputValue("");
          setShowAllCategories(false);
        }
      }}
    >
      <PopoverTrigger asChild>
        <FormControl className="form-control">
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className={cn(
              "w-full justify-between cursor-pointer h-12 border-gray-200 dark:border-gray-600 focus:border-green-500 focus:ring-green-500/20 transition-all duration-200 bg-white dark:bg-gray-800",
              !field.value && "text-muted-foreground"
            )}
            disabled={isPending || isLoadingCategories}
            onClick={() => setOpen(!open)}
            type="button"
          >
            {field.value
              ? categories.find((category) => category.id === field.value)
                  ?.name || "Kategori terpilih"
              : "Pilih kategori atau ketik untuk tambah baru"}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </FormControl>
      </PopoverTrigger>
      <PopoverContent className="p-0 w-[var(--radix-popover-trigger-width)]">
        <div className="max-h-[200px] overflow-auto">
          {/* Management Link */}
          <div className="p-2 border-b bg-muted/30">
            <CategoryManagementDialog
              onCategoriesUpdated={onCategoriesUpdated}
            />
          </div>

          <div className="p-2 border-b">
            <input
              className="w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-primary"
              placeholder="Cari atau ketik untuk tambah kategori..."
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
            />
          </div>

          {/* Empty state */}
          {categories.length === 0 && !inputValue && (
            <div className="p-2 text-sm text-center text-muted-foreground">
              Belum ada kategori
            </div>
          )}

          {/* Create New Category Button - Always show when there's input */}
          {inputValue && (
            <div className="flex flex-col gap-2 p-2 border-b">
              <Button
                type="button"
                size="sm"
                className="cursor-pointer w-full"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  handleAddCategory();
                }}
                disabled={isAddingCategory}
              >
                <Plus className="mr-2 h-4 w-4" />
                {isAddingCategory ? "Menambahkan..." : `Tambah "${inputValue}"`}
              </Button>
            </div>
          )}

          {/* Category list */}
          <div className="py-1">
            {/* Filter categories based on search input and limit to 3 if not searching */}
            {categories
              .filter(
                (category) =>
                  showAllCategories ||
                  inputValue ||
                  categories.indexOf(category) < 3
              )
              .filter(
                (category) =>
                  !inputValue ||
                  category.name.toLowerCase().includes(inputValue.toLowerCase())
              )
              .map((category) => (
                <div
                  key={category.id}
                  className="flex items-center px-2 py-1.5 text-sm hover:bg-accent hover:text-accent-foreground cursor-pointer"
                  onClick={() => {
                    console.log(
                      "Clicked on category:",
                      category.name,
                      "with ID:",
                      category.id
                    );
                    handleSelect(category.id);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      field.value === category.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {category.name}
                </div>
              ))}

            {/* Show "View All" button if there are more than 3 categories and not already showing all */}
            {!showAllCategories && !inputValue && categories.length > 3 && (
              <div
                className="flex justify-center items-center py-2 text-sm text-primary hover:underline cursor-pointer"
                onClick={() => setShowAllCategories(true)}
              >
                Lihat Semua Kategori ({categories.length})
              </div>
            )}
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}

export default CategoryCombobox;
