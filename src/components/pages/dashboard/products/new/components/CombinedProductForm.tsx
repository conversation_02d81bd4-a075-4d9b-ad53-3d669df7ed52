"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
// Icons are not used in this component
import { ProductFormValues } from "../types";
import { Control } from "react-hook-form";
import ProductBasicInfo from "./ProductBasicInfo";
import ProductPricingInventory from "./ProductPricingInventory";
import ProductImagesSection from "./ProductImagesSection";
import ProductAdvancedOptions from "./ProductAdvancedOptions";
import ProductColorVariants from "./ProductColorVariants";
import { Separator } from "@/components/ui/separator";

interface CombinedProductFormProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
  handleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploading: boolean;
  previewUrl: string;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  fileInputKey: number;
  onDeleteImage?: (imageUrl: string) => Promise<void>;
}

const CombinedProductForm: React.FC<CombinedProductFormProps> = ({
  control,
  isPending,
  handleImageUpload,
  isUploading,
  previewUrl,
  fileInputRef,
  fileInputKey,
  onDeleteImage,
}) => {
  return (
    <Card>
      {/* Formulir Produk */}
      <CardHeader></CardHeader>
      <CardContent className="space-y-8">
        {/* Basic Information Section */}
        <div>
          <ProductBasicInfo control={control} isPending={isPending} />
        </div>

        <Separator />

        {/* Pricing & Inventory Section */}
        <div>
          <ProductPricingInventory control={control} isPending={isPending} />
        </div>

        <Separator />

        {/* Images Section */}
        <div>
          <ProductImagesSection
            control={control}
            isPending={isPending}
            handleImageUpload={handleImageUpload}
            isUploading={isUploading}
            previewUrl={previewUrl}
            fileInputRef={fileInputRef}
            fileInputKey={fileInputKey}
            onDeleteImage={onDeleteImage}
          />
        </div>

        <Separator />

        {/* Color Variants and Advanced Options Section - Side by Side */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Color Variants Section */}
          <div>
            <ProductColorVariants control={control} isPending={isPending} />
          </div>

          {/* Advanced Options Section */}
          <div>
            <ProductAdvancedOptions control={control} isPending={isPending} />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CombinedProductForm;
