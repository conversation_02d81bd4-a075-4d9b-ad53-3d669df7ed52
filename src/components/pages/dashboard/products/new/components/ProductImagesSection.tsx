"use client";

import React, { useState } from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { ProductFormValues } from "../types";
import Image from "next/image";
import { ImageIcon, X, Image as ImageLucide } from "lucide-react";
import { cn } from "@/lib/utils";

interface ProductImagesSectionProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
  handleImageUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploading: boolean;
  previewUrl: string;
  fileInputRef: React.RefObject<HTMLInputElement | null>;
  fileInputKey: number;
  onDeleteImage?: (imageUrl: string) => Promise<void>;
}

const ProductImagesSection: React.FC<ProductImagesSectionProps> = ({
  control,
  isPending,
  handleImageUpload,
  isUploading,
  previewUrl,
  fileInputRef,
  fileInputKey,
  onDeleteImage,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);
  return (
    <div className="space-y-6">
      <FormField
        control={control}
        name="image"
        render={({ field }) => (
          <FormItem>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Upload Area Title */}
              <div>
                <FormLabel className="flex items-center gap-1.5">
                  <ImageLucide className="h-4 w-4 text-indigo-500" />
                  Gambar Produk{" "}
                  <span className="text-xs text-gray-500">(Opsional)</span>
                </FormLabel>
                <FormDescription>
                  Format yang didukung: JPG, PNG, GIF.
                </FormDescription>
              </div>

              {/* Preview Area Title */}
              <div>
                <div className="font-medium flex items-center gap-1.5">
                  <ImageIcon className="h-4 w-4 text-gray-500" />
                  Pratinjau Gambar
                </div>
              </div>
            </div>

            {/* Hidden field for the image URL */}
            <input type="hidden" {...field} />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              {/* Upload Area */}
              <div>
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-6 flex flex-col items-center justify-center text-center",
                    "hover:border-primary/50 transition-colors cursor-pointer",
                    "bg-muted/50 h-[280px] w-full"
                  )}
                  onClick={() => fileInputRef.current?.click()}
                >
                  <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
                  <div className="space-y-1">
                    <p className="text-sm font-medium">
                      Klik untuk mengunggah gambar
                    </p>
                    <p className="text-xs text-muted-foreground">
                      atau seret dan lepas file di sini
                    </p>
                  </div>

                  {/* Hidden file input */}
                  <FormControl>
                    <Input
                      key={fileInputKey}
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      ref={fileInputRef}
                      disabled={isPending || isUploading}
                      className="hidden"
                    />
                  </FormControl>
                </div>
              </div>

              {/* Preview Area */}
              <div>
                <div className="relative w-full h-[280px] border-2 rounded-lg overflow-hidden bg-muted/50 dark:bg-gray-800 flex items-center justify-center">
                  {previewUrl ? (
                    <>
                      <Image
                        src={previewUrl}
                        alt="Preview"
                        fill
                        style={{ objectFit: "contain" }}
                      />
                      <Button
                        type="button"
                        size="icon"
                        variant="destructive"
                        className="absolute top-2 right-2 h-8 w-8 cursor-pointer"
                        onClick={async (e) => {
                          e.stopPropagation();

                          // If there's an image URL and onDeleteImage handler
                          if (field.value && onDeleteImage) {
                            setIsDeleting(true);
                            try {
                              // Call the delete handler
                              await onDeleteImage(field.value);
                            } catch (error) {
                              console.error("Error deleting image:", error);
                            } finally {
                              setIsDeleting(false);
                            }
                          }

                          // Reset the file input and form field
                          if (fileInputRef.current) {
                            fileInputRef.current.value = "";
                          }
                          field.onChange("");
                        }}
                        disabled={isPending || !previewUrl || isDeleting}
                      >
                        {isDeleting ? (
                          <div className="h-4 w-4 border-2 border-t-transparent border-white rounded-full animate-spin" />
                        ) : (
                          <X className="h-4 w-4" />
                        )}
                      </Button>
                    </>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      Tidak ada gambar
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* File information below both frames */}
            <div className="mt-4">
              {/* Upload Status */}
              {isUploading && (
                <div className="flex items-center justify-center mb-2">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2 text-sm text-muted-foreground">
                    Mengunggah...
                  </span>
                </div>
              )}

              {/* File Size Info */}
              <div className="text-xs text-muted-foreground">
                <p>Ukuran maksimum: 5MB</p>
                <p>Resolusi yang disarankan: 1000x1000 piksel</p>
              </div>
            </div>

            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default ProductImagesSection;
