"use client";

import React from "react";
import { Control } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ProductFormValues } from "../types";
import { Separator } from "@/components/ui/separator";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Weight,
  MoveHorizontal,
  MoveVertical,
  Package2,
  Info,
} from "lucide-react";

interface ProductAdvancedOptionsProps {
  control: Control<ProductFormValues>;
  isPending: boolean;
}

const ProductAdvancedOptions: React.FC<ProductAdvancedOptionsProps> = ({
  control,
  isPending,
}) => {
  return (
    <div className="space-y-8">
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem
          value="shipping-info"
          className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-200"
        >
          <AccordionTrigger className="py-3 px-4 hover:no-underline bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all duration-300 cursor-pointer group">
            <h3 className="text-base font-medium group-hover:translate-x-0.5 transition-transform duration-300 flex items-center gap-2">
              <Info className="h-5 w-5 text-primary" />
              Informasi Lainnya
            </h3>
          </AccordionTrigger>
          <AccordionContent className="px-4">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-3 pt-4">
              <FormField
                control={control}
                name="weight"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1.5">
                      <Weight className="h-4 w-4 text-red-500" />
                      Berat (gram)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="0"
                        value={
                          String(field.value) === "0" ? "" : String(field.value)
                        }
                        onFocus={() => {
                          // Clear the field if it's just "0"
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          // If empty, set to "0"
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={(e) => {
                          // Only allow numbers for weight fields
                          if (
                            !(e.key >= "0" && e.key <= "9") &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete" &&
                            e.key !== "Tab" &&
                            e.key !== "Escape" &&
                            e.key !== "Enter"
                          ) {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          // Remove any non-numeric characters
                          const value = e.target.value.replace(/[^0-9]/g, "");
                          field.onChange(value || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="length"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1.5">
                      <MoveHorizontal className="h-4 w-4 text-blue-500" />
                      Panjang (cm)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="0"
                        value={
                          String(field.value) === "0" ? "" : String(field.value)
                        }
                        onFocus={() => {
                          // Clear the field if it's just "0"
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          // If empty, set to "0"
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={(e) => {
                          // Allow decimal point for dimension fields
                          if (
                            !(e.key >= "0" && e.key <= "9") &&
                            e.key !== "." &&
                            e.key !== "," &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete" &&
                            e.key !== "Tab" &&
                            e.key !== "Escape" &&
                            e.key !== "Enter"
                          ) {
                            e.preventDefault();
                          }
                          // Convert comma to decimal point
                          if (e.key === ",") {
                            e.preventDefault();
                            const input = e.target as HTMLInputElement;
                            const start = input.selectionStart || 0;
                            const end = input.selectionEnd || 0;
                            const value = input.value;
                            input.value =
                              value.substring(0, start) +
                              "." +
                              value.substring(end);
                            input.selectionStart = input.selectionEnd =
                              start + 1;
                          }
                          // Prevent multiple decimal points
                          if (
                            e.key === "." &&
                            (e.target as HTMLInputElement).value.includes(".")
                          ) {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          // Remove any non-numeric characters except decimal point
                          const value = e.target.value.replace(/[^0-9.]/g, "");
                          // Ensure only one decimal point
                          const parts = value.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");
                          field.onChange(sanitized || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="width"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1.5">
                      <MoveHorizontal className="h-4 w-4 text-green-500" />
                      Lebar (cm)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="0"
                        value={
                          String(field.value) === "0" ? "" : String(field.value)
                        }
                        onFocus={() => {
                          // Clear the field if it's just "0"
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          // If empty, set to "0"
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={(e) => {
                          // Allow decimal point for dimension fields
                          if (
                            !(e.key >= "0" && e.key <= "9") &&
                            e.key !== "." &&
                            e.key !== "," &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete" &&
                            e.key !== "Tab" &&
                            e.key !== "Escape" &&
                            e.key !== "Enter"
                          ) {
                            e.preventDefault();
                          }
                          // Convert comma to decimal point
                          if (e.key === ",") {
                            e.preventDefault();
                            const input = e.target as HTMLInputElement;
                            const start = input.selectionStart || 0;
                            const end = input.selectionEnd || 0;
                            const value = input.value;
                            input.value =
                              value.substring(0, start) +
                              "." +
                              value.substring(end);
                            input.selectionStart = input.selectionEnd =
                              start + 1;
                          }
                          // Prevent multiple decimal points
                          if (
                            e.key === "." &&
                            (e.target as HTMLInputElement).value.includes(".")
                          ) {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          // Remove any non-numeric characters except decimal point
                          const value = e.target.value.replace(/[^0-9.]/g, "");
                          // Ensure only one decimal point
                          const parts = value.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");
                          field.onChange(sanitized || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="height"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1.5">
                      <MoveVertical className="h-4 w-4 text-purple-500" />
                      Tinggi (cm)
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="0"
                        value={
                          String(field.value) === "0" ? "" : String(field.value)
                        }
                        onFocus={() => {
                          // Clear the field if it's just "0"
                          if (String(field.value) === "0") {
                            field.onChange("");
                          }
                        }}
                        onBlur={() => {
                          // If empty, set to "0"
                          if (!field.value && field.value !== 0) {
                            field.onChange("0");
                          }
                        }}
                        onKeyDown={(e) => {
                          // Allow decimal point for dimension fields
                          if (
                            !(e.key >= "0" && e.key <= "9") &&
                            e.key !== "." &&
                            e.key !== "," &&
                            e.key !== "Backspace" &&
                            e.key !== "Delete" &&
                            e.key !== "Tab" &&
                            e.key !== "Escape" &&
                            e.key !== "Enter"
                          ) {
                            e.preventDefault();
                          }
                          // Convert comma to decimal point
                          if (e.key === ",") {
                            e.preventDefault();
                            const input = e.target as HTMLInputElement;
                            const start = input.selectionStart || 0;
                            const end = input.selectionEnd || 0;
                            const value = input.value;
                            input.value =
                              value.substring(0, start) +
                              "." +
                              value.substring(end);
                            input.selectionStart = input.selectionEnd =
                              start + 1;
                          }
                          // Prevent multiple decimal points
                          if (
                            e.key === "." &&
                            (e.target as HTMLInputElement).value.includes(".")
                          ) {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          // Remove any non-numeric characters except decimal point
                          const value = e.target.value.replace(/[^0-9.]/g, "");
                          // Ensure only one decimal point
                          const parts = value.split(".");
                          const sanitized =
                            parts[0] +
                            (parts.length > 1
                              ? "." + parts.slice(1).join("")
                              : "");
                          field.onChange(sanitized || "0");
                        }}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default ProductAdvancedOptions;
