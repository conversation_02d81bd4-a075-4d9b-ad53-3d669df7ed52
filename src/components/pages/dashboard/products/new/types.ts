import { z } from "zod";
import { ProductSchema } from "@/schemas/zod";

// Define the schema for color variants
export const ColorVariantSchema = z.object({
  id: z.string().optional(), // Optional for new variants
  sku: z.string().nullable().optional(),
  colorName: z.string().min(1, { message: "<PERSON>a warna wajib diisi" }),
  colorCode: z.string().min(1, { message: "Kode warna wajib diisi" }),
  image: z.string().nullable().optional(),
});

// Extend the base ProductSchema with additional fields for the enhanced UI
export const EnhancedProductSchema = ProductSchema.extend({
  // Additional fields for the enhanced UI
  categoryId: z.string().optional(),
  unitId: z.string().optional(),
  barcode: z.string().optional(),
  // Individual tax rates for each price type
  salePriceTaxRate: z.coerce.number().min(0).max(100).optional(),
  wholesalePriceTaxRate: z.coerce.number().min(0).max(100).optional(),
  costPriceTaxRate: z.coerce.number().min(0).max(100).optional(),
  hasVariants: z.boolean().default(false).optional(),
  trackInventory: z.boolean().default(true).optional(),
  minStockLevel: z.coerce.number().int().nonnegative().optional(),
  // Support both individual dimension fields and nested dimensions object
  length: z.coerce.number().nonnegative().optional(),
  width: z.coerce.number().nonnegative().optional(),
  height: z.coerce.number().nonnegative().optional(),
  dimensions: z
    .object({
      length: z.coerce.number().nonnegative().optional(),
      width: z.coerce.number().nonnegative().optional(),
      height: z.coerce.number().nonnegative().optional(),
    })
    .optional(),
  // Add color variants
  colorVariants: z.array(ColorVariantSchema).optional(),
  // Draft status
  isDraft: z.boolean().default(false).optional(),
});

// Define the type for the form values
export type ProductFormValues = z.infer<typeof EnhancedProductSchema>;

// Define the type for product categories
export interface Category {
  id: string;
  name: string;
}

// Define the type for product tags
export interface Tag {
  id: string;
  name: string;
}

// Define the type for color variants
export type ColorVariant = z.infer<typeof ColorVariantSchema>;
