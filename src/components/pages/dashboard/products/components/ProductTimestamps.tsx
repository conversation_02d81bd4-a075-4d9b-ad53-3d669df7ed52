"use client";

import React from "react";
import { Clock } from "lucide-react";

interface ProductTimestampsProps {
  createdAt: Date;
  updatedAt: Date;
}

const ProductTimestamps: React.FC<ProductTimestampsProps> = ({
  createdAt,
  updatedAt,
}) => {
  return (
    <div className="mt-8 pt-4 border-t border-gray-200 dark:border-gray-700">
      <div className="flex flex-wrap gap-x-6 gap-y-2 text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Dibuat:</span>
          <span>
            {new Date(createdAt).toLocaleDateString("id-ID", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })}
          </span>
        </div>
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3" />
          <span>Diperbarui:</span>
          <span>
            {new Date(updatedAt).toLocaleDateString("id-ID", {
              day: "numeric",
              month: "long",
              year: "numeric",
            })}
          </span>
        </div>
      </div>
    </div>
  );
};

export default ProductTimestamps;
