"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { DatePicker } from "@/components/ui/date-picker";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  FileSpreadsheet,
  FileText,
  AlertCircle,
  CheckCircle,
  Info,
  Calendar,
  Settings,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createProductImportTemplate } from "@/utils/imports/products";
import { importProducts } from "@/actions/import/products";
import { createProductsExcelReport } from "@/utils/exports/productsExport";
import { getProductReportDataWithFilters } from "@/actions/reports";
import {
  generateDateStringForFilename,
  validateExportPeriod,
} from "@/utils/dateUtils";

interface ImportSummary {
  productsCreated: number;
  categoriesCreated: number;
  unitsCreated: number;
  variantsCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
}

interface ProductImportExportProps {
  onRefresh?: () => void; // Callback to refresh the products list after import
}

// Function to calculate column width based on content
const getColumnWidth = (data: any[], header: string): number => {
  const maxLength = Math.max(
    ...data.map((item) => (item ? String(item).length : 0)),
    header.length
  );
  return Math.min(Math.max(maxLength + 2, 10), 50); // Set a reasonable min and max width
};

// Function to create product-only Excel report
const createProductOnlyExcelReport = (
  products: any[],
  options: {
    reportTitle: string;
    includeSummary: boolean;
    totalProducts: number;
  }
) => {
  const workbook = XLSX.utils.book_new();

  // Create header info
  const headerData = [
    [options.reportTitle],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [`Total Produk: ${options.totalProducts}`],
  ];

  // Define column headers
  const columnHeaders = [
    "ID Produk",
    "Nama Produk",
    "Deskripsi",
    "SKU",
    "Barcode",
    "Kategori Produk",
    "Unit",
    "Stok",
    "Harga Beli",
    "Harga Jual",
    "Harga Grosir",
    "Tag Produk",
    "Varian Warna",
  ];

  // Prepare product data
  const productData = products.map((product) => {
    // Format tags as comma-separated string
    const tagsString =
      product.tags && product.tags.length > 0 ? product.tags.join(", ") : "-";

    // Format color variants as comma-separated string
    const variantsString =
      product.variants && product.variants.length > 0
        ? product.variants
            .map(
              (variant: any) => `${variant.colorName} (${variant.colorCode})`
            )
            .join(", ")
        : "-";

    return [
      product.id || "-",
      product.name || "-",
      product.description || "-",
      product.sku || "-",
      product.barcode || "-",
      product.category?.name || "Tidak ada kategori",
      product.unit || "pcs",
      product.stock || 0,
      product.cost || 0,
      product.price || 0,
      product.wholesalePrice || 0,
      tagsString,
      variantsString,
    ];
  });

  // Combine all data
  const worksheetData = [...headerData, [], columnHeaders, ...productData];

  // Create worksheet
  const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

  // Set column widths dynamically
  const colData = worksheetData.slice(4); // Data rows
  const columnWidths = columnHeaders.map((header, colIndex) => {
    const colValues = colData.map((row) => row[colIndex]);
    return { wch: getColumnWidth(colValues, header) };
  });
  worksheet["!cols"] = columnWidths;

  // --- Apply professional styling ---

  // Title section style (rows 1-3)
  const titleStyle = {
    font: { bold: true, sz: 18, color: { rgb: "FFFFFF" } },
    alignment: { horizontal: "center", vertical: "center" },
    fill: { fgColor: { rgb: "2196F3" } }, // Blue background
  };
  const subtitleStyle = {
    font: { sz: 11, color: { rgb: "424242" } },
    alignment: { horizontal: "center", vertical: "center" },
  };
  const totalStyle = {
    font: { bold: true, sz: 12, color: { rgb: "424242" } },
    alignment: { horizontal: "center", vertical: "center" },
  };

  if (worksheet["A1"]) {
    worksheet["A1"].s = titleStyle;
    worksheet["B1"] = { s: titleStyle }; // Merge cells A1 to M1 for the title
    worksheet["M1"] = { s: titleStyle };
    worksheet["!merges"] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: columnHeaders.length - 1 } },
    ];
  }
  if (worksheet["A2"]) worksheet["A2"].s = subtitleStyle;
  if (worksheet["A3"]) worksheet["A3"].s = totalStyle;

  // Column header style (row 5)
  const columnHeaderStyle = {
    font: { bold: true, sz: 12, color: { rgb: "FFFFFF" } },
    alignment: { horizontal: "center", vertical: "center" },
    fill: { fgColor: { rgb: "1976D2" } }, // Darker blue
    border: {
      top: { style: "thin", color: { rgb: "9E9E9E" } },
      bottom: { style: "thin", color: { rgb: "9E9E9E" } },
      left: { style: "thin", color: { rgb: "9E9E9E" } },
      right: { style: "thin", color: { rgb: "9E9E9E" } },
    },
  };

  const headerRowIndex = 5;
  columnHeaders.forEach((_, colIndex) => {
    const cellAddress = XLSX.utils.encode_cell({
      r: headerRowIndex - 1,
      c: colIndex,
    });
    if (worksheet[cellAddress]) {
      worksheet[cellAddress].s = columnHeaderStyle;
    }
  });

  // Data row styles (starting from row 6)
  const dataRowIndexStart = 6;
  productData.forEach((row, rowIndex) => {
    const isEvenRow = rowIndex % 2 === 0;
    const baseStyle = {
      font: { sz: 11, color: { rgb: "424242" } },
      alignment: { horizontal: "left", vertical: "center" },
      fill: { fgColor: { rgb: isEvenRow ? "F5F5F5" : "FFFFFF" } }, // Zebra stripes
      border: {
        top: { style: "thin", color: { rgb: "E0E0E0" } },
        bottom: { style: "thin", color: { rgb: "E0E0E0" } },
        left: { style: "thin", color: { rgb: "E0E0E0" } },
        right: { style: "thin", color: { rgb: "E0E0E0" } },
      },
    };
    const numberStyle = {
      ...baseStyle,
      alignment: { horizontal: "right", vertical: "center" },
      numFmt: "#,##0", // Format numbers with thousands separator
    };
    const currencyStyle = {
      ...numberStyle,
      numFmt: `Rp#,##0`, // Example for Rupiah currency
    };

    row.forEach((cell, colIndex) => {
      const cellAddress = XLSX.utils.encode_cell({
        r: dataRowIndexStart - 1 + rowIndex,
        c: colIndex,
      });
      if (worksheet[cellAddress]) {
        if (colIndex >= 7 && colIndex <= 10) {
          // Number columns: Stok, Harga Beli, Harga Jual, Harga Grosir
          worksheet[cellAddress].s = numberStyle;
        } else {
          worksheet[cellAddress].s = baseStyle;
        }
      }
    });
  });

  // Add worksheet to workbook
  XLSX.utils.book_append_sheet(workbook, worksheet, "Data Produk");

  // Create Info Dokumen sheet
  const createInfoDocumentSheet = (): XLSX.WorkSheet => {
    const infoData = [
      ["INFORMASI DOKUMEN EXPORT DATA PRODUK"],
      [""],
      ["TENTANG DOKUMEN:"],
      ["• Nama File: Export Data Produk"],
      ["• Versi: 1.0"],
      ["• Tanggal Dibuat: " + new Date().toLocaleDateString("id-ID")],
      ["• Format: Microsoft Excel (.xlsx)"],
      ["• Tujuan: Export data produk dari sistem manajemen inventori"],
      [""],
      ["DESKRIPSI:"],
      ["Dokumen ini berisi data produk yang telah diekspor dari sistem"],
      ["manajemen inventori. Data mencakup informasi lengkap produk"],
      ["seperti nama, deskripsi, harga, stok, kategori, dan varian."],
      [""],
      ["STRUKTUR DATA:"],
      ["• Sheet 'Data Produk': Berisi seluruh data produk"],
      ["• ID Produk: Kode unik untuk setiap produk"],
      ["• Nama Produk: Nama lengkap produk"],
      ["• Deskripsi: Penjelasan detail produk"],
      ["• SKU: Stock Keeping Unit (kode inventori)"],
      ["• Barcode: Kode barcode produk"],
      ["• Kategori Produk: Pengelompokan produk"],
      ["• Unit: Satuan produk (pcs, kg, liter, dll)"],
      ["• Stok: Jumlah stok tersedia"],
      ["• Harga Beli: Harga pembelian/modal"],
      ["• Harga Jual: Harga jual kepada pelanggan"],
      ["• Harga Grosir: Harga khusus untuk pembelian grosir"],
      ["• Tag Produk: Label/kategori tambahan"],
      ["• Varian Warna: Pilihan warna yang tersedia"],
      [""],
      ["CATATAN PENTING:"],
      ["• Data ini merupakan snapshot pada waktu export"],
      ["• Untuk data terkini, lakukan export ulang"],
      ["• Jangan mengubah struktur data jika akan diimport kembali"],
      ["• Backup data secara berkala untuk keamanan"],
      [""],
      ["KONTAK SUPPORT:"],
      ["• Email: <EMAIL>"],
      ["• WhatsApp: +62 812-3456-7890"],
      ["• Website: www.kasironline.com"],
      ["• Jam Operasional: Senin-Jumat 08:00-17:00 WIB"],
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(infoData);

    // Apply styles to title
    if (worksheet["A1"]) {
      worksheet["A1"].s = {
        font: { bold: true, sz: 16, color: { rgb: "FFFFFF" } },
        alignment: { horizontal: "center", vertical: "center" },
        fill: { fgColor: { rgb: "1976D2" } },
      };
    }

    // Apply styles to section headers
    const sectionHeaders = [3, 10, 15, 31, 37];
    sectionHeaders.forEach((row) => {
      const cellAddress = `A${row}`;
      if (worksheet[cellAddress]) {
        worksheet[cellAddress].s = {
          font: { bold: true, sz: 12, color: { rgb: "424242" } },
          fill: { fgColor: { rgb: "E3F2FD" } },
        };
      }
    });

    // Set column width
    worksheet["!cols"] = [{ wch: 80 }];

    return worksheet;
  };

  // Add Info Dokumen sheet
  const infoDocumentSheet = createInfoDocumentSheet();
  XLSX.utils.book_append_sheet(workbook, infoDocumentSheet, "Info Dokumen");

  return workbook;
};

export const ProductImportExport: React.FC<ProductImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
  });

  // Download template function
  const downloadTemplate = () => {
    try {
      const workbook = createProductImportTemplate();
      const fileName = `template-import-produk-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template");
    }
  };

  // Handle import function
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file maksimal 10MB");
      return;
    }

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/i)) {
      toast.error("Format file harus Excel (.xlsx atau .xls)");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result = await importProducts(arrayBuffer);
      setImportProgress(80);

      if (result.success) {
        setImportProgress(100);
        setImportSummary(result.summary || null);
        toast.success(
          result.success + " Lihat notifikasi untuk detail lengkap."
        );

        // Auto-refresh data on successful import to show newly imported items
        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500); // Small delay to ensure notifications are processed
        }
      } else if (result.error) {
        setImportSummary(result.summary || null);
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor file");
    } finally {
      setIsImporting(false);
      setImportProgress(0);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle advanced export function
  const handleAdvancedExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Validate export period
      const validationError = validateExportPeriod(exportConfig.reportType, {
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      if (validationError) {
        toast.error(validationError);
        return;
      }

      setExportProgress(20);

      // Determine date range and period label
      let startDate: Date | undefined;
      let endDate: Date | undefined;
      let dateRange: string = "custom";
      let periodLabel: string = "";

      if (exportConfig.reportType === "harian") {
        startDate = new Date(exportConfig.selectedDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(exportConfig.selectedDate);
        endDate.setHours(23, 59, 59, 999);
        periodLabel = `Harian - ${exportConfig.selectedDate.toLocaleDateString("id-ID")}`;
      } else if (exportConfig.reportType === "bulanan") {
        startDate = new Date(
          exportConfig.selectedYear,
          exportConfig.selectedMonth,
          1
        );
        endDate = new Date(
          exportConfig.selectedYear,
          exportConfig.selectedMonth + 1,
          0
        );
        endDate.setHours(23, 59, 59, 999);
        const monthNames = [
          "Januari",
          "Februari",
          "Maret",
          "April",
          "Mei",
          "Juni",
          "Juli",
          "Agustus",
          "September",
          "Oktober",
          "November",
          "Desember",
        ];
        periodLabel = `Bulanan - ${monthNames[exportConfig.selectedMonth]} ${exportConfig.selectedYear}`;
      } else if (exportConfig.reportType === "tahunan") {
        startDate = new Date(exportConfig.selectedYear, 0, 1);
        endDate = new Date(exportConfig.selectedYear, 11, 31);
        endDate.setHours(23, 59, 59, 999);
        periodLabel = `Tahunan - ${exportConfig.selectedYear}`;
      }

      setExportProgress(40);

      // Fetch product data with date filtering
      const productResult = await getProductReportDataWithFilters({
        dateRange,
        startDate,
        endDate,
        category: undefined,
      });

      setExportProgress(70);

      if (productResult.error) {
        console.error("Product data fetch error:", productResult.error);
        throw new Error(productResult.error);
      }

      console.log(
        "Product data fetched:",
        productResult.data?.length || 0,
        "products"
      );
      console.log("Export config:", exportConfig);
      console.log("Date range:", { startDate, endDate, dateRange });

      // Check if we have data
      if (!productResult.data || productResult.data.length === 0) {
        toast.error(
          "Tidak ada data produk untuk diekspor pada periode yang dipilih"
        );
        return;
      }

      // Prepare report data structure
      const reportData = {
        reportType: "produk",
        products: productResult.data,
        summary: {
          period: periodLabel,
          generatedAt: new Date(),
          totalProducts: productResult.data?.length || 0,
        },
      };

      setExportProgress(85);

      // Generate Excel export using new template
      const workbook = createProductsExcelReport(
        reportData.products,
        periodLabel,
        {
          companyName: "Kasir Online",
          reportTitle: `Data Produk - ${periodLabel}`,
        }
      );

      setExportProgress(100);

      // Generate filename based on selected date range
      const dateString = generateDateStringForFilename(
        exportConfig.reportType,
        {
          selectedDate: exportConfig.selectedDate,
          selectedMonth: exportConfig.selectedMonth,
          selectedYear: exportConfig.selectedYear,
        }
      );

      const fileName = `data-produk-${exportConfig.reportType}-${dateString}.xlsx`;
      XLSX.writeFile(workbook, fileName);

      toast.success("Data produk berhasil diekspor!");
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data produk");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Produk
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data produk dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Produk:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data produk sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>

            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full flex items-center gap-2 cursor-pointer"
              >
                <FileSpreadsheet className="h-4 w-4" />
                Download Template Excel
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Mengimpor data produk...</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  Hasil Import:
                </h4>
                <div className="text-sm space-y-1">
                  <p>
                    ✅ Produk berhasil dibuat: {importSummary.productsCreated}
                  </p>
                  <p>
                    📁 Kategori baru dibuat: {importSummary.categoriesCreated}
                  </p>
                  <p>📏 Satuan baru dibuat: {importSummary.unitsCreated}</p>
                  <p>🎨 Varian dibuat: {importSummary.variantsCreated}</p>

                  {importSummary.errors && importSummary.errors.length > 0 && (
                    <div className="mt-3">
                      <p className="text-red-600 font-medium flex items-center gap-1">
                        <AlertCircle className="h-4 w-4" />
                        Error:
                      </p>
                      <div className="max-h-32 overflow-y-auto">
                        {importSummary.errors
                          .slice(0, 10)
                          .map((error: string, index: number) => (
                            <p key={index} className="text-xs text-red-600">
                              • {error}
                            </p>
                          ))}
                        {importSummary.errors.length > 10 && (
                          <p className="text-xs text-red-600">
                            ... dan {importSummary.errors.length - 10} error
                            lainnya
                          </p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Close Button */}
            {importSummary && (
              <div className="flex justify-end pt-4">
                <Button
                  onClick={() => {
                    setShowImportDialog(false);
                    setImportSummary(null);
                    if (fileInputRef.current) {
                      fileInputRef.current.value = "";
                    }
                  }}
                  className="flex items-center gap-2"
                >
                  Tutup
                </Button>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Advanced Export Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Data Produk
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Pilih periode dan format yang ingin diekspor untuk data produk
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Report Type Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Periode Export</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-slate-50 dark:hover:bg-slate-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <CardContent className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1 text-blue-500" />
                      <p className="text-xs font-medium">{type.label}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            <Separator />

            {/* Date/Period Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Pilih Periode</Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9 cursor-pointer"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9 w-full">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {[
                        "Januari",
                        "Februari",
                        "Maret",
                        "April",
                        "Mei",
                        "Juni",
                        "Juli",
                        "Agustus",
                        "September",
                        "Oktober",
                        "November",
                        "Desember",
                      ].map((month, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {month}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-11"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection - Excel Only */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <div className="flex items-center gap-2 p-3 border rounded-md bg-gray-50 dark:bg-gray-800">
                <FileSpreadsheet className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium">Excel (.xlsx)</span>
                <span className="text-xs text-gray-500 ml-auto">
                  Dengan sheet &apos;Data Produk&apos; dan &apos;Info
                  Dokumen&apos;
                </span>
              </div>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeCharts"
                    checked={exportConfig.includeCharts}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeCharts: checked as boolean,
                      }))
                    }
                    disabled
                  />
                  <Label
                    htmlFor="includeCharts"
                    className="text-sm cursor-pointer text-slate-500"
                  >
                    Sertakan grafik (Segera Hadir)
                  </Label>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              className="cursor-pointer"
              variant="outline"
              onClick={() => setShowExportDialog(false)}
            >
              Batal
            </Button>
            <Button
              onClick={handleAdvancedExport}
              disabled={isExporting}
              className="flex items-center gap-2 cursor-pointer"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data Produk"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Progress Dialog */}
      {isExporting && (
        <Dialog open={isExporting}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Mengekspor Data Produk
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <span>Memproses data...</span>
                <span>{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} />
              <p className="text-sm text-slate-600">
                Mohon tunggu, sedang memproses dan mengunduh file data produk.
              </p>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};
