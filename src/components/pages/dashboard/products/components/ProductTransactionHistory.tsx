"use client";

import React, { useState, useEffect } from "react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import {
  Package,
  ShoppingCart,
  TrendingUp,
  Loader2,
  ChevronLeft,
  ChevronRight,
  Calendar,
  Building,
  Hash,
  DollarSign,
  Receipt,
  FileText,
  User,
} from "lucide-react";

interface PurchaseTransaction {
  id: string;
  date: string;
  supplier: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  invoiceNumber: string;
}

interface SalesTransaction {
  id: string;
  date: string;
  customer: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  invoiceNumber: string;
}

interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalCount: number;
  limit: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
}

interface ProductTransactionHistoryProps {
  productId: string;
}

// API functions to fetch real data with pagination
const fetchPurchaseHistory = async (
  productId: string,
  page: number = 1,
  limit: number = 10
): Promise<{
  purchases: PurchaseTransaction[];
  pagination: PaginationInfo;
}> => {
  try {
    const response = await fetch(
      `/api/products/${productId}/purchases?page=${page}&limit=${limit}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch purchase history");
    }
    const data = await response.json();
    return {
      purchases: data.purchases || [],
      pagination: data.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: limit,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  } catch (error) {
    console.error("Error fetching purchase history:", error);
    return {
      purchases: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: limit,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  }
};

const fetchSalesHistory = async (
  productId: string,
  page: number = 1,
  limit: number = 10
): Promise<{ sales: SalesTransaction[]; pagination: PaginationInfo }> => {
  try {
    const response = await fetch(
      `/api/products/${productId}/sales?page=${page}&limit=${limit}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch sales history");
    }
    const data = await response.json();
    return {
      sales: data.sales || [],
      pagination: data.pagination || {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: limit,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  } catch (error) {
    console.error("Error fetching sales history:", error);
    return {
      sales: [],
      pagination: {
        currentPage: 1,
        totalPages: 1,
        totalCount: 0,
        limit: limit,
        hasNextPage: false,
        hasPrevPage: false,
      },
    };
  }
};

// Purchase History Table Component
interface PurchaseHistoryTableProps {
  purchases: PurchaseTransaction[];
  loading: boolean;
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
}

const PurchaseHistoryTable: React.FC<PurchaseHistoryTableProps> = ({
  purchases,
  loading,
  pagination,
  onPageChange,
}) => {
  if (loading) {
    return (
      <div className="text-center py-12">
        <Loader2 className="h-8 w-8 text-blue-500 mx-auto mb-4 animate-spin" />
        <p className="text-gray-500 dark:text-gray-400">
          Memuat riwayat pembelian...
        </p>
      </div>
    );
  }

  if (purchases.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">
          Belum ada riwayat pembelian untuk produk ini
        </p>
      </div>
    );
  }

  // Table UI untuk Riwayat Pembelian
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center">
          <ShoppingCart className="h-5 w-5 text-blue-600 dark:text-blue-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Riwayat Pembelian
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Daftar transaksi pembelian produk ini
          </p>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm text-xs sm:text-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px]">
            <thead>
              <tr className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-b border-blue-200 dark:border-blue-800">
                {[
                  {
                    label: "Tanggal",
                    icon: Calendar,
                    align: "text-left",
                  },
                  {
                    label: "Supplier",
                    icon: Building,
                    align: "text-left",
                  },
                  {
                    label: "Qty",
                    icon: Hash,
                    align: "text-right",
                  },
                  {
                    label: "Harga Satuan",
                    icon: DollarSign,
                    align: "text-right",
                  },
                  {
                    label: "Total",
                    icon: Receipt,
                    align: "text-right",
                  },
                  {
                    label: "No. Invoice",
                    icon: FileText,
                    align: "text-left",
                  },
                ].map((col, i) => {
                  const IconComponent = col.icon;
                  return (
                    <th
                      key={i}
                      className={`${col.align} py-2 px-3 sm:py-4 sm:px-6 font-semibold text-blue-900 dark:text-blue-100`}
                    >
                      <div
                        className={`flex items-center ${col.align.includes("right") ? "justify-end" : ""} gap-1 sm:gap-2`}
                      >
                        <IconComponent className="w-3 h-3 sm:w-4 sm:h-4" />
                        {col.label}
                      </div>
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
              {purchases.map((purchase, index) => (
                <tr
                  key={purchase.id}
                  className={`hover:bg-blue-50 dark:hover:bg-blue-900/10 transition-colors duration-200 ${
                    index % 2 === 0
                      ? "bg-gray-50/50 dark:bg-gray-800/50"
                      : "bg-white dark:bg-gray-800"
                  }`}
                >
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(purchase.date).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </span>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                        <Building className="w-3 h-3 sm:w-4 sm:h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {purchase.supplier}
                        </p>
                        <p className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                          Supplier
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <div className="inline-flex items-center gap-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-2 py-0.5 rounded-full text-[11px] sm:text-sm font-medium">
                      {purchase.quantity}
                      <span className="text-[10px]">unit</span>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      Rp {purchase.unitPrice.toLocaleString("id-ID")}
                    </span>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <span className="font-bold text-blue-600 dark:text-blue-400 text-sm sm:text-base">
                      Rp {purchase.totalAmount.toLocaleString("id-ID")}
                    </span>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <span className="inline-block border rounded px-2 py-0.5 text-[10px] sm:text-xs bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                      {purchase.invoiceNumber}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-t border-blue-200 dark:border-blue-800 px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4 text-xs sm:text-sm">
            <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-blue-700 dark:text-blue-300">
              <span>
                Total Transaksi:{" "}
                <span className="font-semibold">{purchases.length}</span>
              </span>
              <span>
                Total Qty:{" "}
                <span className="font-semibold">
                  {purchases.reduce((sum, p) => sum + p.quantity, 0)} unit
                </span>
              </span>
            </div>
            <div className="text-blue-700 dark:text-blue-300 text-right">
              <span>Total Pembelian: </span>
              <span className="font-bold text-blue-600 dark:text-blue-400 text-sm sm:text-base">
                Rp{" "}
                {purchases
                  .reduce((sum, p) => sum + p.totalAmount, 0)
                  .toLocaleString("id-ID")}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span>
            Menampilkan {(pagination.currentPage - 1) * pagination.limit + 1} -{" "}
            {Math.min(
              pagination.currentPage * pagination.limit,
              pagination.totalCount
            )}{" "}
            dari {pagination.totalCount} transaksi
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
            className="flex items-center gap-1"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="hidden md:block">Sebelumnya</span>
          </Button>

          <div className="flex items-center gap-1">
            {Array.from(
              { length: Math.min(5, pagination.totalPages) },
              (_, i) => {
                let pageNum;
                if (pagination.totalPages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.currentPage <= 3) {
                  pageNum = i + 1;
                } else if (
                  pagination.currentPage >=
                  pagination.totalPages - 2
                ) {
                  pageNum = pagination.totalPages - 4 + i;
                } else {
                  pageNum = pagination.currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={
                      pageNum === pagination.currentPage ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              }
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
            className="flex items-center gap-1"
          >
            <span className="hidden md:block">Selanjutnya</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

// Sales History Table Component
interface SalesHistoryTableProps {
  sales: SalesTransaction[];
  loading: boolean;
  pagination: PaginationInfo;
  onPageChange: (page: number) => void;
}

const SalesHistoryTable: React.FC<SalesHistoryTableProps> = ({
  sales,
  loading,
  pagination,
  onPageChange,
}) => {
  if (loading) {
    return (
      <div className="text-center py-12">
        <Loader2 className="h-8 w-8 text-green-500 mx-auto mb-4 animate-spin" />
        <p className="text-gray-500 dark:text-gray-400">
          Memuat riwayat penjualan...
        </p>
      </div>
    );
  }

  if (sales.length === 0) {
    return (
      <div className="text-center py-8">
        <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500 dark:text-gray-400">
          Belum ada riwayat penjualan untuk produk ini
        </p>
      </div>
    );
  }

  // Table UI untuk Riwayat Penjualan
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
          <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Riwayat Penjualan
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Daftar transaksi penjualan produk ini
          </p>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden shadow-sm text-xs sm:text-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px]">
            <thead>
              <tr className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-b border-green-200 dark:border-green-800">
                {[
                  {
                    label: "Tanggal",
                    icon: Calendar,
                    align: "text-left",
                  },
                  {
                    label: "Customer",
                    icon: User,
                    align: "text-left",
                  },
                  {
                    label: "Qty",
                    icon: Hash,
                    align: "text-right",
                  },
                  {
                    label: "Harga Satuan",
                    icon: DollarSign,
                    align: "text-right",
                  },
                  {
                    label: "Total",
                    icon: Receipt,
                    align: "text-right",
                  },
                  {
                    label: "No. Invoice",
                    icon: FileText,
                    align: "text-left",
                  },
                ].map((col, i) => {
                  const IconComponent = col.icon;
                  return (
                    <th
                      key={i}
                      className={`${col.align} py-2 px-3 sm:py-4 sm:px-6 font-semibold text-green-900 dark:text-green-100`}
                    >
                      <div
                        className={`flex items-center ${col.align.includes("right") ? "justify-end" : ""} gap-1 sm:gap-2`}
                      >
                        <IconComponent className="w-3 h-3 sm:w-4 sm:h-4" />
                        {col.label}
                      </div>
                    </th>
                  );
                })}
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100 dark:divide-gray-700">
              {sales.map((sale, index) => (
                <tr
                  key={sale.id}
                  className={`hover:bg-green-50 dark:hover:bg-green-900/10 transition-colors duration-200 ${
                    index % 2 === 0
                      ? "bg-gray-50/50 dark:bg-gray-800/50"
                      : "bg-white dark:bg-gray-800"
                  }`}
                >
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <div className="flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-gray-900 dark:text-gray-100">
                        {new Date(sale.date).toLocaleDateString("id-ID", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        })}
                      </span>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                        <User className="w-3 h-3 sm:w-4 sm:h-4 text-green-600 dark:text-green-400" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900 dark:text-gray-100">
                          {sale.customer}
                        </p>
                        <p className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400">
                          Customer
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <div className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-2 py-0.5 rounded-full text-[11px] sm:text-sm font-medium">
                      {sale.quantity}
                      <span className="text-[10px]">unit</span>
                    </div>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <span className="font-medium text-gray-900 dark:text-gray-100">
                      Rp {sale.unitPrice.toLocaleString("id-ID")}
                    </span>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6 text-right">
                    <span className="font-bold text-green-600 dark:text-green-400 text-sm sm:text-base">
                      Rp {sale.totalAmount.toLocaleString("id-ID")}
                    </span>
                  </td>
                  <td className="py-2 px-3 sm:py-4 sm:px-6">
                    <span className="inline-block border rounded px-2 py-0.5 text-[10px] sm:text-xs bg-gray-50 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-800 dark:text-gray-200">
                      {sale.invoiceNumber}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/30 dark:to-emerald-950/30 border-t border-green-200 dark:border-green-800 px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-4 text-xs sm:text-sm">
            <div className="flex flex-wrap items-center gap-3 sm:gap-4 text-green-700 dark:text-green-300">
              <span>
                Total Transaksi:{" "}
                <span className="font-semibold">{sales.length}</span>
              </span>
              <span>
                Total Qty:{" "}
                <span className="font-semibold">
                  {sales.reduce((sum, s) => sum + s.quantity, 0)} unit
                </span>
              </span>
            </div>
            <div className="text-green-700 dark:text-green-300 text-right">
              <span>Total Penjualan: </span>
              <span className="font-bold text-green-600 dark:text-green-400 text-sm sm:text-base">
                Rp{" "}
                {sales
                  .reduce((sum, s) => sum + s.totalAmount, 0)
                  .toLocaleString("id-ID")}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-6 py-4 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
          <span>
            Menampilkan {(pagination.currentPage - 1) * pagination.limit + 1} -{" "}
            {Math.min(
              pagination.currentPage * pagination.limit,
              pagination.totalCount
            )}{" "}
            dari {pagination.totalCount} transaksi
          </span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage - 1)}
            disabled={!pagination.hasPrevPage}
            className="flex items-center gap-1"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="hidden md:block">Sebelumnya</span>
          </Button>

          <div className="flex items-center gap-1">
            {Array.from(
              { length: Math.min(5, pagination.totalPages) },
              (_, i) => {
                let pageNum;
                if (pagination.totalPages <= 5) {
                  pageNum = i + 1;
                } else if (pagination.currentPage <= 3) {
                  pageNum = i + 1;
                } else if (
                  pagination.currentPage >=
                  pagination.totalPages - 2
                ) {
                  pageNum = pagination.totalPages - 4 + i;
                } else {
                  pageNum = pagination.currentPage - 2 + i;
                }

                return (
                  <Button
                    key={pageNum}
                    variant={
                      pageNum === pagination.currentPage ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => onPageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              }
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => onPageChange(pagination.currentPage + 1)}
            disabled={!pagination.hasNextPage}
            className="flex items-center gap-1"
          >
            <span className="hidden md:block">Selanjutnya</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

const ProductTransactionHistory: React.FC<ProductTransactionHistoryProps> = ({
  productId,
}) => {
  const [purchases, setPurchases] = useState<PurchaseTransaction[]>([]);
  const [sales, setSales] = useState<SalesTransaction[]>([]);
  const [purchasesLoading, setPurchasesLoading] = useState(true);
  const [salesLoading, setSalesLoading] = useState(true);

  // Pagination state
  const [purchasePagination, setPurchasePagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
    hasNextPage: false,
    hasPrevPage: false,
  });
  const [salesPagination, setSalesPagination] = useState<PaginationInfo>({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    limit: 10,
    hasNextPage: false,
    hasPrevPage: false,
  });

  // Load purchase history with pagination
  const loadPurchaseHistory = async (page: number = 1) => {
    setPurchasesLoading(true);
    try {
      const data = await fetchPurchaseHistory(productId, page, 10);
      setPurchases(data.purchases);
      setPurchasePagination(data.pagination);
    } catch (error) {
      console.error("Failed to load purchase history:", error);
    } finally {
      setPurchasesLoading(false);
    }
  };

  // Load sales history with pagination
  const loadSalesHistory = async (page: number = 1) => {
    setSalesLoading(true);
    try {
      const data = await fetchSalesHistory(productId, page, 10);
      setSales(data.sales);
      setSalesPagination(data.pagination);
    } catch (error) {
      console.error("Failed to load sales history:", error);
    } finally {
      setSalesLoading(false);
    }
  };

  useEffect(() => {
    loadPurchaseHistory(1);
    loadSalesHistory(1);
  }, [productId]);

  return (
    <div className="space-y-6 mt-6">
      {/* Section Title */}
      <div className="text-center space-y-4">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Riwayat Transaksi
        </h2>
        <Separator className="max-w-xs mx-auto" />
      </div>

      {/* Transaction History Tabs */}
      <Card className="overflow-hidden pt-0">
        <CardContent className="p-0">
          <Tabs defaultValue="purchases" className="w-full">
            <TabsList className="grid w-full grid-cols-2 rounded-none border-b bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-900 p-0 h-auto">
              <TabsTrigger
                value="purchases"
                className="group relative flex items-center justify-center gap-3 rounded-none rounded-tl-md border-b-3 border-transparent py-4 px-6 text-gray-600 dark:text-gray-400 transition-all duration-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 dark:hover:text-blue-400 data-[state=active]:border-blue-500 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-50 data-[state=active]:to-blue-100 dark:data-[state=active]:from-blue-950/30 dark:data-[state=active]:to-blue-900/30 data-[state=active]:text-blue-700 dark:data-[state=active]:text-blue-300 data-[state=active]:shadow-sm"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-data-[state=active]:bg-blue-200 dark:group-data-[state=active]:bg-blue-800/50 transition-colors duration-300">
                    <ShoppingCart className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-sm">Pembelian</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 group-data-[state=active]:text-blue-600 dark:group-data-[state=active]:text-blue-400">
                      Riwayat Beli
                    </div>
                  </div>
                </div>
                {/* Active indicator */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-blue-500 transform scale-x-0 group-data-[state=active]:scale-x-100 transition-transform duration-300 origin-center"></div>
              </TabsTrigger>
              <TabsTrigger
                value="sales"
                className="group relative flex items-center justify-center gap-3 rounded-none rounded-tr-md border-b-3 border-transparent py-4 px-6 text-gray-600 dark:text-gray-400 transition-all duration-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 data-[state=active]:border-green-500 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-50 data-[state=active]:to-green-100 dark:data-[state=active]:from-green-950/30 dark:data-[state=active]:to-green-900/30 data-[state=active]:text-green-700 dark:data-[state=active]:text-green-300 data-[state=active]:shadow-sm"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-lg bg-green-100 dark:bg-green-900/30 flex items-center justify-center group-data-[state=active]:bg-green-200 dark:group-data-[state=active]:bg-green-800/50 transition-colors duration-300">
                    <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                  </div>
                  <div className="text-left">
                    <div className="font-semibold text-sm">Penjualan</div>
                    <div className="text-xs text-gray-500 dark:text-gray-400 group-data-[state=active]:text-green-600 dark:group-data-[state=active]:text-green-400">
                      Riwayat Jual
                    </div>
                  </div>
                </div>
                {/* Active indicator */}
                <div className="absolute bottom-0 left-0 right-0 h-1 bg-green-500 transform scale-x-0 group-data-[state=active]:scale-x-100 transition-transform duration-300 origin-center"></div>
              </TabsTrigger>
            </TabsList>

            {/* Purchase History Tab */}
            <TabsContent value="purchases" className="p-4 mt-0">
              <PurchaseHistoryTable
                purchases={purchases}
                loading={purchasesLoading}
                pagination={purchasePagination}
                onPageChange={loadPurchaseHistory}
              />
            </TabsContent>

            {/* Sales History Tab */}
            <TabsContent value="sales" className="p-4 mt-0">
              <SalesHistoryTable
                sales={sales}
                loading={salesLoading}
                pagination={salesPagination}
                onPageChange={loadSalesHistory}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProductTransactionHistory;
