"use client";

import React from "react";
import { Box, Info, Layers } from "lucide-react";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ProductDetailsTab from "./ProductDetailsTab";
import ProductInventoryTab from "./ProductInventoryTab";

interface Category {
  id: string;
  name: string;
}

interface ProductVariant {
  id: string;
  sku: string | null;
  colorName: string;
  colorCode: string;
  price: number | null;
  stock: number;
  image: string | null;
  productId: string;
}

interface ProductTabsProps {
  id: string; // Add product ID
  name: string;
  sku: string | null;
  description: string | null;
  barcode: string | null;
  taxRate?: number;
  tags?: string[];
  stock: number;
  minStockLevel?: number;
  trackInventory?: boolean;
  hasVariants?: boolean;
  weight?: number | null;
  dimensions?: {
    length?: number;
    width?: number;
    height?: number;
  };
  variants?: ProductVariant[];
}

const ProductTabs: React.FC<ProductTabsProps> = ({
  id,
  name,
  sku,
  description,
  barcode,
  taxRate,
  tags,
  stock,
  minStockLevel,
  trackInventory,
  hasVariants,
  weight,
  dimensions,
  variants,
}) => {
  return (
    <Tabs defaultValue="details" className="w-full">
      <TabsList className="grid grid-cols-2 mb-4 w-full md:w-fit">
        <TabsTrigger
          value="details"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Info className="h-4 w-4" />
          <span>Detail</span>
        </TabsTrigger>
        <TabsTrigger
          value="inventory"
          className="flex items-center gap-2 cursor-pointer"
        >
          <Layers className="h-4 w-4" />
          <span>Inventaris</span>
        </TabsTrigger>
      </TabsList>

      {/* Details Tab */}
      <TabsContent value="details">
        <ProductDetailsTab
          id={id}
          name={name}
          sku={sku}
          barcode={barcode}
          taxRate={taxRate}
          description={description}
          tags={tags}
        />
      </TabsContent>

      {/* Inventory Tab */}
      <TabsContent value="inventory">
        <ProductInventoryTab
          stock={stock}
          minStockLevel={minStockLevel}
          trackInventory={trackInventory}
          hasVariants={hasVariants}
          weight={weight}
          dimensions={dimensions}
          variants={variants}
        />
      </TabsContent>
    </Tabs>
  );
};

export default ProductTabs;
