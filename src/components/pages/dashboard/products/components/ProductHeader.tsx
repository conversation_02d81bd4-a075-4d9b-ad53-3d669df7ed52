"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ArrowLeft, Edit, Trash, FileCheck, FileX } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { deleteProduct } from "@/actions/entities/products";

interface ProductHeaderProps {
  productId: string;
  productName: string;
  isDraft?: boolean;
}

const ProductHeader: React.FC<ProductHeaderProps> = ({
  productId,
  productName,
  isDraft = false,
}) => {
  const router = useRouter();
  const [isDeleting, setIsDeleting] = useState(false);

  // Handle delete product
  const handleDeleteProduct = async () => {
    setIsDeleting(true);
    try {
      const result = await deleteProduct(productId);
      if (result.success) {
        toast.success(result.success);
        router.push("/dashboard/products");
      } else if (result.error) {
        toast.error(result.error);
      }
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error("Terjadi kesalahan saat menghapus produk.");
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          onClick={() => router.back()}
          className="h-13 w-9 cursor-pointer hidden md:flex"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold">{productName}</h1>
            {isDraft ? (
              <Badge
                variant="outline"
                className="bg-amber-50 text-amber-700 border-amber-200 flex items-center gap-1"
              >
                <FileX className="h-3 w-3" />
                Draft
              </Badge>
            ) : (
              <Badge
                variant="outline"
                className="bg-green-50 text-green-700 border-green-200 flex items-center gap-1"
              >
                <FileCheck className="h-3 w-3" />
                Published
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground">Detail informasi produk</p>
        </div>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" asChild className="gap-2 cursor-pointer">
          <Link href={`/dashboard/products/edit/${productId}`}>
            <Edit className="h-4 w-4" />
            Edit
          </Link>
        </Button>
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button variant="destructive" className="gap-2 cursor-pointer">
              <Trash className="h-4 w-4" />
              Hapus
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Konfirmasi Hapus</AlertDialogTitle>
              <AlertDialogDescription>
                Apakah Anda yakin ingin menghapus produk{" "}
                <span className="font-semibold">{productName}</span>? Tindakan
                ini tidak dapat dibatalkan.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="cursor-pointer">
                Batal
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDeleteProduct}
                disabled={isDeleting}
                className="cursor-pointer bg-red-500 hover:bg-red-600"
              >
                {isDeleting ? "Menghapus..." : "Hapus"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </div>
  );
};

export default ProductHeader;
