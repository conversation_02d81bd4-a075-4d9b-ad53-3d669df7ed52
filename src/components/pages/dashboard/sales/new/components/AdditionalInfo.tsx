"use client";

import React, { useState, useRef } from "react";
import { Control, useFormContext } from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { SaleFormValues } from "../types";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  FileText,
  Upload,
  X,
  File,
  Image as ImageIcon,
  Paperclip,
} from "lucide-react";
import { toast } from "sonner";
import {
  uploadSalesDocument,
  deleteSalesDocument,
} from "@/actions/uploads/documents";

interface AdditionalInfoProps {
  control: Control<SaleFormValues>;
  isPending: boolean;
  items?: SaleFormValues["items"];
}

const AdditionalInfo: React.FC<AdditionalInfoProps> = ({
  control,
  isPending,
  items = [],
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [fileInputKey, setFileInputKey] = useState<number>(Date.now());
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const form = useFormContext();

  // We don't need to calculate totals here anymore

  // Function to get icon based on file extension
  const getFileIcon = (filename: string) => {
    const extension = filename.split(".").pop()?.toLowerCase();

    if (["jpg", "jpeg", "png", "gif", "webp"].includes(extension || "")) {
      return <ImageIcon className="h-4 w-4 text-blue-500" aria-hidden="true" />;
    } else if (["pdf", "doc", "docx", "txt"].includes(extension || "")) {
      return <FileText className="h-4 w-4 text-red-500" />;
    } else {
      return <File className="h-4 w-4 text-gray-500" />;
    }
  };

  // Handle file upload
  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file size (max 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      toast.error("Ukuran file maksimal 10MB");
      // Reset the file input by updating the key
      setFileInputKey(Date.now());
      return;
    }

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const result = await uploadSalesDocument(formData);

      if (result.success && result.url) {
        // Get the current lampiran array from the form
        const currentLampiran = form.getValues("lampiran") || [];

        // Store both the URL and the original filename
        const newAttachment = {
          url: result.url,
          filename: result.filename || file.name,
        };

        // Update the form with the new attachment
        form.setValue("lampiran", [...currentLampiran, newAttachment], {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        toast.success("Dokumen berhasil diunggah");
      } else {
        toast.error(result.error || "Gagal mengunggah dokumen");
      }
    } catch (error) {
      console.error("Upload Error:", error);
      toast.error("Gagal mengunggah dokumen");
    } finally {
      setIsUploading(false);
      // Reset the file input by updating the key
      setFileInputKey(Date.now());
    }
  };

  // Handle file deletion
  const handleDeleteFile = async (index: number, url: string) => {
    try {
      const result = await deleteSalesDocument(url);

      if (result.success) {
        // Get the current lampiran array from the form
        const currentLampiran = form.getValues("lampiran") || [];

        // Remove the file at the specified index
        const newLampiran = [...currentLampiran];
        newLampiran.splice(index, 1);

        // Update the form
        form.setValue("lampiran", newLampiran, {
          shouldDirty: true,
          shouldTouch: true,
          shouldValidate: true,
        });

        toast.success("Dokumen berhasil dihapus");
      } else {
        toast.error(result.error || "Gagal menghapus dokumen");
      }
    } catch (error) {
      console.error("Delete Error:", error);
      toast.error("Gagal menghapus dokumen");
    }
  };

  // Calculate subtotal and total for display with proper discount and tax handling
  const priceIncludesTax = form.watch("priceIncludesTax");

  // Check if any items have discounts applied
  const hasDiscounts = items.some((item) => {
    const discountRate = parseFloat(item?.discount || "0");
    return discountRate > 0;
  });

  // Calculate subtotal (before tax, after discount)
  const subtotal = items.reduce((sum, item) => {
    const quantity = item?.quantity ?? 0;
    const price = item?.priceAtSale ?? 0;
    const discountRate = parseFloat(item?.discount || "0") / 100;

    // Apply discount to price
    const discountAmount = price * discountRate;
    const priceAfterDiscount = price - discountAmount;

    // Subtotal is quantity * price after discount (before tax)
    return sum + quantity * priceAfterDiscount;
  }, 0);

  // Calculate total (with tax if applicable)
  const total = items.reduce((sum, item) => {
    const quantity = item?.quantity ?? 0;
    const price = item?.priceAtSale ?? 0;
    const discountRate = parseFloat(item?.discount || "0") / 100;
    const taxRate = parseFloat(item?.tax || "0") / 100;

    // Apply discount to price
    const discountAmount = price * discountRate;
    const priceAfterDiscount = price - discountAmount;

    let itemSubtotal;
    if (priceIncludesTax) {
      // If price includes tax, the total is simply quantity * price after discount
      itemSubtotal = quantity * priceAfterDiscount;
    } else {
      // If price doesn't include tax, we add tax to the price after discount
      const subtotalBeforeTax = quantity * priceAfterDiscount;
      const taxAmount = subtotalBeforeTax * taxRate;
      itemSubtotal = subtotalBeforeTax + taxAmount;
    }

    return sum + itemSubtotal;
  }, 0);

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      {/* Left Column - Memo and Lampiran */}
      <div className="space-y-6">
        {/* Memo Field */}
        <div>
          <div className="flex items-center gap-1.5 mb-2">
            <FileText className="h-4 w-4 text-purple-600" />
            <h4 className="text-sm font-medium">Memo</h4>
          </div>
          <FormField
            control={control}
            name="memo"
            render={({ field }) => {
              const currentLength = field.value?.length || 0;
              const maxLength = 1000;
              const remainingChars = maxLength - currentLength;

              return (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="Tambahkan memo untuk penjualan ini..."
                      className="resize-none h-[80px]"
                      {...field}
                      disabled={isPending}
                      maxLength={maxLength}
                      onChange={(e) => {
                        if (e.target.value.length <= maxLength) {
                          field.onChange(e);
                        }
                      }}
                    />
                  </FormControl>
                  <div className="flex justify-between items-center mt-1">
                    <FormDescription className="text-xs">
                      Memo internal untuk referensi
                    </FormDescription>
                    <span
                      className={`text-xs ${remainingChars < 50 ? "text-orange-500" : remainingChars < 10 ? "text-red-500" : "text-gray-500"}`}
                    >
                      {currentLength}/{maxLength}
                    </span>
                  </div>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        </div>

        {/* Lampiran Field */}
        <div>
          <div className="flex items-center gap-1.5 mb-2">
            <Paperclip className="h-4 w-4 text-amber-600" />
            <h4 className="text-sm font-medium">Lampiran</h4>
          </div>
          <div className="border border-dashed rounded-lg p-2 flex flex-row items-center justify-between bg-muted/30">
            <input
              type="file"
              ref={fileInputRef}
              key={fileInputKey}
              className="hidden"
              onChange={handleFileUpload}
              accept="image/*,.pdf,.doc,.docx,.txt"
            />
            <div className="flex items-center">
              <Upload className="h-5 w-5 text-muted-foreground mr-2" />
              <div className="text-left">
                <p className="text-xs">Unggah dokumen</p>
                <p className="text-xs text-muted-foreground">
                  PDF, gambar, atau dokumen
                </p>
              </div>
            </div>
            <Button
              type="button"
              variant="outline"
              className="cursor-pointer h-8"
              disabled={isPending || isUploading}
              onClick={() => fileInputRef.current?.click()}
              size="sm"
            >
              Pilih File
            </Button>
          </div>

          {/* Display uploaded files */}
          <FormField
            control={control}
            name="lampiran"
            render={({ field }) => (
              <FormItem className="w-full">
                {field.value && field.value.length > 0 ? (
                  <div className="w-full">
                    <ul className="space-y-1 text-left mt-2">
                      {field.value.map(
                        (
                          file: { url: string; filename: string },
                          index: number
                        ) => {
                          return (
                            <li
                              key={index}
                              className="flex items-center justify-between bg-muted p-1 rounded-md text-xs"
                            >
                              <div className="flex items-center gap-1.5">
                                {getFileIcon(file.filename)}
                                <span className="truncate max-w-[180px]">
                                  {file.filename}
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  handleDeleteFile(index, file.url);
                                }}
                                disabled={isPending || isUploading}
                                className="cursor-pointer h-6 w-6 p-0"
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </li>
                          );
                        }
                      )}
                    </ul>
                  </div>
                ) : null}
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Right Column - Summary */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6">
        <h4 className="text-sm font-medium mb-4 text-gray-900 dark:text-gray-100 underline italic">
          Ringkasan Penjualan
        </h4>
        <div className="space-y-3">
          {/* Subtotal */}
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-600 dark:text-gray-400">
              {hasDiscounts ? "Sub-total (setelah diskon):" : "Sub-total:"}
            </span>
            <span className="font-medium text-gray-900 dark:text-gray-100">
              Rp{" "}
              {subtotal.toLocaleString("id-ID", { minimumFractionDigits: 0 })}
            </span>
          </div>

          {/* Tax information if applicable */}
          {!priceIncludesTax &&
            items.some((item) => parseFloat(item?.tax || "0") > 0) && (
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Pajak:
                </span>
                <span className="font-medium text-gray-900 dark:text-gray-100">
                  Rp{" "}
                  {(total - subtotal).toLocaleString("id-ID", {
                    minimumFractionDigits: 0,
                  })}
                </span>
              </div>
            )}

          {/* Total */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
            <div className="flex justify-between items-center">
              <span className="text-base font-semibold text-gray-900 dark:text-gray-100">
                Total:
              </span>
              <span className="text-base font-bold text-gray-900 dark:text-gray-100">
                Rp {total.toLocaleString("id-ID", { minimumFractionDigits: 0 })}
              </span>
            </div>
          </div>

          {/* Additional info */}
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            {priceIncludesTax
              ? "Harga sudah termasuk pajak"
              : "Harga belum termasuk pajak"}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdditionalInfo;
