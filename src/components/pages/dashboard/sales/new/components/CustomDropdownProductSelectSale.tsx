"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { createPortal } from "react-dom";
import {
  Control,
  UseFieldArrayRemove,
  FieldValues,
  useFormContext,
} from "react-hook-form";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@heroicons/react/24/outline";
import { ChevronDown, ChevronUp, Plus } from "lucide-react";
import {
  SaleFormValues,
  Product,
} from "@/components/pages/dashboard/sales/new/types";
import { formatCurrency } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";

interface CustomDropdownProductSelectSaleProps {
  control: Control<SaleFormValues>;
  index: number;
  field: FieldValues; // Used by useFieldArray, even if not directly referenced
  products: Product[];
  items: SaleFormValues["items"];
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
  isPending: boolean;
  canRemove: boolean;
  activeEventDiscounts?: any[];
}

const CustomDropdownProductSelectSale: React.FC<
  CustomDropdownProductSelectSaleProps
> = ({
  control,
  index,
  field: _, // Unused but required by useFieldArray
  products,
  items,
  remove,
  handleProductChange,
  isPending,
  canRemove,
  activeEventDiscounts = [],
}) => {
  const form = useFormContext<SaleFormValues>();
  const item = items[index];
  const quantity = item?.quantity || 0;
  const price = item?.priceAtSale || 0;
  const discountRate = parseFloat(item?.discount || "0") / 100;
  const taxRate = parseFloat(item?.tax || "0") / 100;
  const priceIncludesTax = form.watch("priceIncludesTax");

  // State for wholesale price warning dialog
  const [showWholesaleWarning, setShowWholesaleWarning] = useState(false);

  // Check if current product has an active event discount
  const currentProductId = item?.productId;
  const hasEventDiscount: boolean = Boolean(
    currentProductId &&
      activeEventDiscounts.some((discount) =>
        discount.products.some((p: any) => p.productId === currentProductId)
      )
  );
  const eventDiscount = hasEventDiscount
    ? activeEventDiscounts.find((discount) =>
        discount.products.some((p: any) => p.productId === currentProductId)
      ) || null // Ensure it's null if not found
    : null;

  // Apply discount to price
  const discountAmount = price * discountRate;
  const priceAfterDiscount = price - discountAmount;

  // Calculate subtotal based on whether price includes tax
  // Helper function to calculate item subtotal
  const calculateItemSubtotal = (
    qty: number,
    priceAtSale: number,
    discount: string,
    tax: string,
    priceIncludesTax: boolean
  ) => {
    const discountRate = parseFloat(discount || "0") / 100;
    const taxRate = parseFloat(tax || "0") / 100;

    const discountAmount = priceAtSale * discountRate;
    const priceAfterDiscount = priceAtSale - discountAmount;

    let itemSubtotal;
    if (priceIncludesTax) {
      itemSubtotal = qty * priceAfterDiscount;
    } else {
      const subtotalBeforeTax = qty * priceAfterDiscount;
      const taxAmount = subtotalBeforeTax * taxRate;
      itemSubtotal = subtotalBeforeTax + taxAmount;
    }
    return itemSubtotal;
  };

  const subtotal = calculateItemSubtotal(
    quantity,
    price,
    item?.discount || "0",
    item?.tax || "0",
    priceIncludesTax
  );

  return (
    <tr className="border-b border-gray-200 dark:border-gray-700 h-20">
      {/* Wholesale Checkbox */}
      <td className="py-4 px-2 text-center" style={{ minWidth: "80px" }}>
        <FormField
          control={control}
          name={`items.${index}.isWholesale`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <FormControl>
                <Checkbox
                  checked={formField.value}
                  onCheckedChange={(checked) => {
                    // Get the selected product to check wholesale price availability
                    const selectedProduct = products.find(
                      (p) => p.id === items[index]?.productId
                    );

                    if (selectedProduct && checked) {
                      // Check if product has wholesale price
                      if (
                        !selectedProduct.wholesalePrice ||
                        selectedProduct.wholesalePrice <= 0
                      ) {
                        // Show warning dialog if no wholesale price is set
                        setShowWholesaleWarning(true);
                        return; // Don't change the checkbox state
                      }
                    }

                    formField.onChange(checked);

                    if (selectedProduct) {
                      const newPrice = checked
                        ? selectedProduct.wholesalePrice ||
                          selectedProduct.price
                        : selectedProduct.price;

                      // Update the price at sale
                      form.setValue(`items.${index}.priceAtSale`, newPrice);

                      // Check if this product has an active event discount and preserve it
                      const currentProductId = items[index]?.productId;
                      const eventDiscount = activeEventDiscounts.find(
                        (discount) =>
                          discount.products.some(
                            (p: any) => p.productId === currentProductId
                          )
                      );

                      if (eventDiscount) {
                        // Preserve the event discount when switching wholesale mode
                        const discountPercentage = Number(
                          eventDiscount.discountPercentage
                        );
                        form.setValue(
                          `items.${index}.discount`,
                          discountPercentage.toString()
                        );
                      }

                      // Recalculate total using the helper function
                      const currentItems = form.getValues("items");
                      const priceIncludesTax = form.watch("priceIncludesTax");
                      const total = currentItems.reduce(
                        (sum: number, currentItem: any) => {
                          return (
                            sum +
                            calculateItemSubtotal(
                              currentItem?.quantity ?? 0,
                              currentItem?.priceAtSale ?? 0,
                              currentItem?.discount || "0",
                              currentItem?.tax || "0",
                              priceIncludesTax
                            )
                          );
                        },
                        0
                      );

                      form.setValue("totalAmount", total);
                    }
                  }}
                  disabled={isPending}
                  className="cursor-pointer"
                />
              </FormControl>
            </FormItem>
          )}
        />
      </td>

      {/* Product Selection */}
      <td className="py-4 pl-0 pr-2" style={{ minWidth: "300px" }}>
        <FormField
          control={control}
          name={`items.${index}.productId`}
          render={({ field: formField, fieldState }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <ProductDropdown
                    value={formField.value}
                    onChange={(value) => {
                      formField.onChange(value);
                      handleProductChange(index, value);
                    }}
                    products={products}
                    disabled={isPending}
                    hasError={!!fieldState.error}
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Quantity */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.quantity`}
          render={({ field: formField, fieldState }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    {...formField}
                    onChange={(e) => {
                      const value = parseInt(e.target.value);
                      formField.onChange(value || 1);

                      // Force recalculation of total immediately
                      const currentItems = form.getValues("items");

                      // Update the current item's quantity
                      if (currentItems[index]) {
                        currentItems[index].quantity = value || 1;
                      }

                      // Recalculate the total using the helper function
                      const priceIncludesTax = form.watch("priceIncludesTax");
                      const total = currentItems.reduce(
                        (sum: number, currentItem: any) => {
                          return (
                            sum +
                            calculateItemSubtotal(
                              currentItem?.quantity ?? 0,
                              currentItem?.priceAtSale ?? 0,
                              currentItem?.discount || "0",
                              currentItem?.tax || "0",
                              priceIncludesTax
                            )
                          );
                        },
                        0
                      );

                      // Update the total in the form
                      form.setValue("totalAmount", total);
                    }}
                    disabled={isPending}
                    className={`w-full ${fieldState.error ? "border-red-500 dark:border-red-400" : ""}`}
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Unit */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.unit`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <Input
                    type="text"
                    {...formField}
                    disabled={true}
                    className="bg-gray-100 dark:bg-gray-800 cursor-not-allowed"
                  />
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Price */}
      <td className="py-4 px-2" style={{ minWidth: "150px" }}>
        <FormField
          control={control}
          name={`items.${index}.priceAtSale`}
          render={({ field: formField, fieldState }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        Rp
                      </span>
                    </div>
                    <Input
                      type="text"
                      value={formatCurrency(formField.value, false)}
                      onChange={(e) => {
                        // Remove all non-numeric characters (including dots)
                        const value = e.target.value.replace(/[^0-9]/g, "");

                        // Parse the sanitized value as a number for the form state
                        const numericValue = parseInt(value);

                        // Update the form field with the numeric value
                        formField.onChange(
                          isNaN(numericValue) ? 0 : numericValue
                        );

                        // Force recalculation of total immediately
                        const currentItems = form.getValues("items");

                        // Update the current item's priceAtSale
                        if (currentItems[index]) {
                          currentItems[index].priceAtSale = isNaN(numericValue)
                            ? 0
                            : numericValue;
                        }

                        // Recalculate the total using the helper function
                        const priceIncludesTax = form.watch("priceIncludesTax");
                        const total = currentItems.reduce(
                          (sum: number, currentItem: any) => {
                            return (
                              sum +
                              calculateItemSubtotal(
                                currentItem?.quantity ?? 0,
                                currentItem?.priceAtSale ?? 0,
                                currentItem?.discount || "0",
                                currentItem?.tax || "0",
                                priceIncludesTax
                              )
                            );
                          },
                          0
                        );

                        // Update the total in the form
                        form.setValue("totalAmount", total);
                      }}
                      disabled={isPending}
                      className={`pl-10 w-full ${fieldState.error ? "border-red-500 dark:border-red-400" : ""}`}
                    />
                  </div>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Discount */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.discount`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <span className="text-gray-500 dark:text-gray-400">
                        %
                      </span>
                    </div>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Input
                            type="text"
                            placeholder="0"
                            {...formField}
                            disabled={isPending || hasEventDiscount}
                            readOnly={hasEventDiscount}
                            className={`pl-8 w-full ${hasEventDiscount ? "bg-blue-50 dark:bg-blue-900/20 border-blue-300 dark:border-blue-600" : ""}`}
                            onChange={
                              hasEventDiscount
                                ? undefined
                                : (e) => {
                                    // Only allow numbers and decimal point
                                    const value = e.target.value.replace(
                                      /[^0-9.]/g,
                                      ""
                                    );
                                    formField.onChange(value);

                                    // Force recalculation of total immediately
                                    const currentItems =
                                      form.getValues("items");

                                    // Update the current item's discount
                                    if (currentItems[index]) {
                                      currentItems[index].discount = value;
                                    }

                                    // Recalculate the total with discount and tax
                                    const priceIncludesTax =
                                      form.watch("priceIncludesTax");
                                    const total = currentItems.reduce(
                                      (sum: number, item: any) => {
                                        const quantity = item?.quantity ?? 0;
                                        const price = item?.priceAtSale ?? 0;
                                        const discountRate =
                                          parseFloat(item?.discount || "0") /
                                          100;
                                        const taxRate =
                                          parseFloat(item?.tax || "0") / 100;

                                        // Apply discount to price
                                        const discountAmount =
                                          price * discountRate;
                                        const priceAfterDiscount =
                                          price - discountAmount;

                                        let itemSubtotal;
                                        if (priceIncludesTax) {
                                          // If price includes tax, the total is simply quantity * price after discount
                                          itemSubtotal =
                                            quantity * priceAfterDiscount;
                                        } else {
                                          // If price doesn't include tax, we add tax to the price after discount
                                          const subtotalBeforeTax =
                                            quantity * priceAfterDiscount;
                                          const taxAmount =
                                            subtotalBeforeTax * taxRate;
                                          itemSubtotal =
                                            subtotalBeforeTax + taxAmount;
                                        }

                                        return sum + itemSubtotal;
                                      },
                                      0
                                    );

                                    // Update the total in the form
                                    form.setValue("totalAmount", total);
                                  }
                            }
                          />
                        </TooltipTrigger>
                        <TooltipContent>
                          {hasEventDiscount && eventDiscount ? (
                            <p>Diskon event: {eventDiscount.name}</p>
                          ) : (
                            <p>Masukkan persentase diskon (0-100)</p>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Tax */}
      <td className="py-4 px-2" style={{ minWidth: "100px" }}>
        <FormField
          control={control}
          name={`items.${index}.tax`}
          render={({ field: formField }) => (
            <FormItem className="space-y-0">
              <div className="relative">
                <FormControl>
                  <div className="relative">
                    <Input
                      type="text"
                      className="pr-6"
                      placeholder="0"
                      {...formField}
                      disabled={isPending}
                      onChange={(e) => {
                        // Only allow numbers and decimal point
                        const value = e.target.value.replace(/[^0-9.]/g, "");
                        formField.onChange(value);

                        // Force recalculation of total immediately
                        const currentItems = form.getValues("items");

                        // Update the current item's tax
                        if (currentItems[index]) {
                          currentItems[index].tax = value;
                        }

                        // Recalculate the total using the helper function
                        const priceIncludesTax = form.watch("priceIncludesTax");
                        const total = currentItems.reduce(
                          (sum: number, currentItem: any) => {
                            return (
                              sum +
                              calculateItemSubtotal(
                                currentItem?.quantity ?? 0,
                                currentItem?.priceAtSale ?? 0,
                                currentItem?.discount || "0",
                                currentItem?.tax || "0",
                                priceIncludesTax
                              )
                            );
                          },
                          0
                        );

                        // Update the total in the form
                        form.setValue("totalAmount", total);
                      }}
                    />
                    <span className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-500 pointer-events-none">
                      %
                    </span>
                  </div>
                </FormControl>
                <div className="absolute top-full left-0 w-full mt-1 z-10">
                  <FormMessage className="text-xs font-medium text-destructive bg-red-50 dark:bg-red-900/20 px-1 py-0.5 rounded-sm inline-block" />
                </div>
              </div>
            </FormItem>
          )}
        />
      </td>

      {/* Subtotal (calculated) */}
      <td className="py-4 px-2" style={{ minWidth: "120px" }}>
        <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
          {formatCurrency(subtotal)}
        </div>
      </td>

      {/* Remove Button */}
      <td className="py-4 px-0 text-right" style={{ minWidth: "70px" }}>
        {canRemove && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => remove(index)}
            disabled={isPending}
            className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 cursor-pointer"
          >
            <TrashIcon className="h-5 w-5" />
          </Button>
        )}
      </td>

      {/* Wholesale Price Warning Dialog */}
      <AlertDialog
        open={showWholesaleWarning}
        onOpenChange={setShowWholesaleWarning}
      >
        <AlertDialogContent className="border-red-500 dark:border-red-400">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-red-600 dark:text-red-400">
              Harga Grosir Belum Ditentukan
            </AlertDialogTitle>
            <AlertDialogDescription>
              Harga grosir belum ditentukan untuk produk ini. Silakan cek
              halaman produk untuk mengatur harga grosir terlebih dahulu.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Tutup</AlertDialogCancel>
            <AlertDialogAction asChild>
              <Link
                href="/dashboard/products"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Kelola Produk
              </Link>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </tr>
  );
};

// Custom Product Dropdown Component with Images and Search
interface ProductDropdownProps {
  value: string;
  onChange: (value: string) => void;
  products: Product[];
  disabled?: boolean;
  hasError?: boolean;
}

const ProductDropdown: React.FC<ProductDropdownProps> = ({
  value,
  onChange,
  products,
  disabled = false,
  hasError = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [position, setPosition] = useState({ top: 0, left: 0 });
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<Product[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const selectedProduct = products.find((product) => product.id === value);

  // Handle mounting for client-side rendering
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Close dropdown when pressing escape
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => {
      document.removeEventListener("keydown", handleEscape);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Update position when dropdown is opened, scrolled, or window is resized
  useEffect(() => {
    const updatePosition = () => {
      if (isOpen && dropdownRef.current) {
        const rect = dropdownRef.current.getBoundingClientRect();
        const dropdownWidth = 300; // Width of dropdown in pixels

        // Check if dropdown would go off the right edge of the screen
        const rightEdge = rect.left + dropdownWidth;
        const windowWidth = window.innerWidth;

        // If it would go off-screen, adjust the left position
        const adjustedLeft =
          rightEdge > windowWidth
            ? Math.max(0, windowWidth - dropdownWidth)
            : rect.left;

        // Calculate position relative to the viewport (for absolute positioning)
        setPosition({
          top: rect.bottom + window.scrollY + 5,
          left: adjustedLeft,
        });
      }
    };

    updatePosition();

    // Add event listeners for resize and scroll
    window.addEventListener("resize", updatePosition);
    window.addEventListener("scroll", updatePosition, true);

    return () => {
      window.removeEventListener("resize", updatePosition);
      window.removeEventListener("scroll", updatePosition, true);
    };
  }, [isOpen]);

  // Search products from API
  const searchProducts = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);
    try {
      const response = await fetch(
        `/api/products/search?q=${encodeURIComponent(query)}&limit=10`
      );
      if (!response.ok) throw new Error("Failed to search products");

      const data = await response.json();
      setSearchResults(data.products || []);
    } catch (error) {
      console.error("Error searching products:", error);
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      searchProducts(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Get products to display - either search results or 3 newest products
  const displayProducts = searchTerm
    ? searchResults
    : [...products]
        .sort((a, b) => {
          // Sort by createdAt if available, otherwise use id as fallback
          if (a.createdAt && b.createdAt) {
            const dateA =
              a.createdAt instanceof Date ? a.createdAt : new Date(a.createdAt);
            const dateB =
              b.createdAt instanceof Date ? b.createdAt : new Date(b.createdAt);
            return dateB.getTime() - dateA.getTime();
          }
          // Fallback to id comparison if createdAt is not available
          return a.id > b.id ? -1 : 1; // Newer IDs are typically longer/greater
        })
        .slice(0, 3);

  return (
    <div className="relative" ref={dropdownRef}>
      <div
        className={`flex items-center justify-between w-full rounded-md border ${
          disabled
            ? "bg-gray-100 dark:bg-gray-800 border-gray-300 dark:border-gray-700"
            : hasError
              ? "bg-white dark:bg-gray-700 border-red-500 dark:border-red-400"
              : "bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600"
        } py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm text-gray-900 dark:text-gray-100 cursor-pointer`}
        onClick={() => !disabled && setIsOpen(!isOpen)}
      >
        <div className="flex items-center gap-3 overflow-hidden">
          {selectedProduct?.image ? (
            <div className="relative h-7 w-7 rounded-sm overflow-hidden">
              <Image
                src={selectedProduct.image}
                alt={selectedProduct.name}
                fill
                sizes="28px"
                className="object-cover"
                unoptimized
              />
            </div>
          ) : selectedProduct ? (
            <div className="h-7 w-7 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                No img
              </span>
            </div>
          ) : (
            <div className="h-7 w-7 bg-gray-100 dark:bg-gray-700 rounded-sm flex items-center justify-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                Img
              </span>
            </div>
          )}
          <span className="truncate font-medium">
            {selectedProduct ? selectedProduct.name : "Pilih Produk"}
          </span>
        </div>
        {!disabled &&
          (isOpen ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          ))}
      </div>

      {mounted &&
        isOpen &&
        !disabled &&
        createPortal(
          <>
            <div
              className="fixed inset-0 z-40"
              onClick={() => setIsOpen(false)}
            />
            <div
              className="absolute z-50 w-[300px] rounded-md bg-white dark:bg-gray-800 shadow-xl max-h-[300px] overflow-auto border border-gray-200 dark:border-gray-700"
              style={{
                top: position.top,
                left: position.left,
                maxWidth: "calc(100vw - 20px)",
              }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="py-1">
                {/* Search input */}
                <div className="px-3 py-2 border-b border-gray-200 dark:border-gray-700">
                  <div className="relative">
                    <input
                      ref={searchInputRef}
                      type="text"
                      placeholder="Cari produk..."
                      className="w-full px-3 py-1.5 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-sm text-gray-900 dark:text-gray-100 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                </div>

                {/* Default option */}
                <div
                  className="px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-700"
                  onClick={() => {
                    onChange("");
                    setIsOpen(false);
                  }}
                >
                  Pilih Produk
                </div>

                {/* Loading indicator */}
                {isSearching && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Mencari...
                  </div>
                )}

                {/* No results message */}
                {!isSearching && searchTerm && displayProducts.length === 0 && (
                  <div className="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 text-center">
                    Tidak ada produk ditemukan
                  </div>
                )}

                {/* Product list */}
                {displayProducts.map((product) => {
                  const isOutOfStock = product.stock <= 0;
                  const isLowStock = product.stock > 0 && product.stock <= 5;

                  return (
                    <div
                      key={product.id}
                      className={`px-3 py-2 text-sm ${
                        isOutOfStock
                          ? "text-gray-400 dark:text-gray-600 cursor-not-allowed bg-gray-50 dark:bg-gray-800/50"
                          : "text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                      }`}
                      onClick={() => {
                        if (!isOutOfStock) {
                          onChange(product.id);
                          setIsOpen(false);
                        }
                      }}
                    >
                      <div className="flex items-center justify-between gap-3">
                        <div className="flex items-center gap-3 flex-1 min-w-0">
                          {product.image ? (
                            <div className="relative h-8 w-8 rounded-sm overflow-hidden">
                              <Image
                                src={product.image}
                                alt={product.name}
                                fill
                                sizes="32px"
                                className={`object-cover ${isOutOfStock ? "opacity-50" : ""}`}
                                unoptimized
                              />
                            </div>
                          ) : (
                            <div
                              className={`h-8 w-8 bg-gray-200 dark:bg-gray-600 rounded-sm flex items-center justify-center ${isOutOfStock ? "opacity-50" : ""}`}
                            >
                              <span className="text-xs text-gray-500 dark:text-gray-400">
                                No img
                              </span>
                            </div>
                          )}
                          <div className="flex-1 min-w-0">
                            <div className="truncate">{product.name}</div>
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                              Stok: {product.stock}
                            </div>
                          </div>
                        </div>

                        {/* Stock status badge */}
                        <div className="flex-shrink-0">
                          {isOutOfStock ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                              Habis
                            </span>
                          ) : isLowStock ? (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400">
                              Sedikit
                            </span>
                          ) : (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                              Tersedia
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}

                {/* Add Product Link */}
                <Link
                  href="/dashboard/products/new"
                  className="mt-2 px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-t border-gray-200 dark:border-gray-700 flex items-center gap-2"
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(false);
                  }}
                >
                  <Plus className="h-4 w-4" />
                  <span>Tambah Produk</span>
                </Link>
              </div>
            </div>
          </>,
          document.body
        )}
    </div>
  );
};

export default CustomDropdownProductSelectSale;
