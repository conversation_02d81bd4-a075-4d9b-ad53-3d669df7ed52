"use client";

import React, { useState, useEffect, useTransition } from "react";
import { useRouter } from "next/navigation";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import {
  updateSale,
  getNextTransactionNumber,
  getNextInvoiceNumber,
} from "@/actions/entities/sales";
import { getActiveEventDiscounts } from "@/actions/event-discounts";
import { EventDiscount } from "@/components/pages/dashboard/event-discounts/types";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { SaleFormValues, Product, EnhancedSaleSchema } from "../new/types";
import CombinedSaleForm from "../new/components/CombinedSaleForm";
import { ArrowLeft, Check, Save } from "lucide-react";

// Define the Sale interface
interface SaleItem {
  id: string;
  quantity: number;
  priceAtSale: number;
  productId: string;
  product: Product;
  unit?: string;
  tax?: string;
  discountPercentage?: number;
  discountAmount?: number;
  eventDiscountId?: string;
  eventDiscountName?: string;
  isWholesale?: boolean;
}

interface Sale {
  id: string;
  totalAmount: number;
  saleDate: string;
  items: SaleItem[];
  priceIncludesTax?: boolean;
  createdAt?: string;
  // Customer relationship
  customerId?: string;
  customer?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    NIK?: string;
    NPWP?: string;
  };
  // Additional fields
  customerRefNumber?: string;
  shippingAddress?: string;
  paymentDueDate?: string;
  paymentTerms?: string;
  warehouse?: string;
  tags?: string[];
  memo?: string;
  lampiran?: { url: string; filename: string }[];
  transactionNumber?: string;
  invoiceRef?: string;
}

// Props use imported types
interface EnhancedSaleEditPageProps {
  sale: Sale;
  products: Product[];
}

const EnhancedSaleEditPage: React.FC<EnhancedSaleEditPageProps> = ({
  sale,
  products,
}) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [totalAmount, setTotalAmount] = useState<number>(sale.totalAmount);
  const [activeEventDiscounts, setActiveEventDiscounts] = useState<
    EventDiscount[]
  >([]);

  // Initialize the form with enhanced schema and sale data
  const form = useForm<SaleFormValues>({
    resolver: zodResolver(EnhancedSaleSchema),
    defaultValues: {
      items: sale.items.map((item) => ({
        productId: item.productId,
        quantity: item.quantity,
        priceAtSale: item.priceAtSale,
        unit: item.unit || "Buah", // Default to "Buah" if not provided
        tax: item.tax || "", // Default to empty string if not provided
        discount: item.discountPercentage
          ? item.discountPercentage.toString()
          : "0", // Use stored discount percentage
        isWholesale: item.isWholesale || false, // Use stored wholesale flag
      })),
      totalAmount: sale.totalAmount,
      customerId: sale.customerId || "", // Use actual customer ID from sale
      customerEmail: sale.customer?.email || "",
      customerNIK: sale.customer?.NIK || "",
      customerNPWP: sale.customer?.NPWP || "",
      paymentMethod: "cash",
      amountPaid: 0,
      notes: "",
      // New fields
      transactionDate: new Date(sale.saleDate),
      transactionNumber: sale.transactionNumber || "",
      invoiceRef: sale.invoiceRef || "",
      customerRefNumber: sale.customerRefNumber || "",
      shippingAddress: sale.shippingAddress || "",
      paymentDueDate: sale.paymentDueDate
        ? new Date(sale.paymentDueDate)
        : undefined,
      paymentTerms: sale.paymentTerms || "Net 30",
      warehouse: sale.warehouse || "",
      tags: sale.tags || [],
      memo: sale.memo || "",
      lampiran: sale.lampiran || [],
      priceIncludesTax: sale.priceIncludesTax || false,
    },
  });

  // Get the items field array
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "items",
  });

  // Watch for changes in the items array to calculate the total
  const items = form.watch("items");

  // Fetch active event discounts on component mount
  useEffect(() => {
    const fetchEventDiscounts = async () => {
      try {
        const result = await getActiveEventDiscounts();
        if (result.success && result.data) {
          setActiveEventDiscounts(result.data);

          // After fetching event discounts, check if any products in the sale have active event discounts
          // and restore the discount values
          const currentItems = form.getValues("items");
          let hasUpdates = false;

          currentItems.forEach((item, index) => {
            if (item.productId) {
              const eventDiscount = result.data.find((discount: any) =>
                discount.products.some(
                  (p: any) => p.productId === item.productId
                )
              );

              if (eventDiscount) {
                const discountPercentage = Number(
                  eventDiscount.discountPercentage
                );
                form.setValue(
                  `items.${index}.discount`,
                  discountPercentage.toString()
                );
                hasUpdates = true;
              }
            }
          });

          // If we updated any discounts, recalculate the total
          if (hasUpdates) {
            const total = currentItems.reduce(
              (sum: number, item: SaleFormValues["items"][number]) => {
                const quantity = item?.quantity ?? 0;
                const price = item?.priceAtSale ?? 0;
                const discountRate = parseFloat(item?.discount || "0") / 100;
                const discountAmount = price * discountRate;
                const priceAfterDiscount = price - discountAmount;
                return sum + quantity * priceAfterDiscount;
              },
              0
            );
            setTotalAmount(total);
            form.setValue("totalAmount", total);
          }
        }
      } catch (error) {
        console.error("Error fetching event discounts:", error);
      }
    };

    fetchEventDiscounts();
  }, [form]);

  // Calculate total amount whenever items change
  useEffect(() => {
    const total = items.reduce(
      (sum: number, item: SaleFormValues["items"][number]) => {
        const quantity = item?.quantity ?? 0;
        const price = item?.priceAtSale ?? 0;
        return sum + quantity * price;
      },
      0
    );
    setTotalAmount(total);
    form.setValue("totalAmount", total);
  }, [items, form]);

  // Handle product selection
  const handleProductChange = (index: number, productId: string) => {
    const selectedProduct: Product | undefined = products.find(
      (p) => p.id === productId
    );
    // Check if selectedProduct exists and its price is a number
    if (selectedProduct && typeof selectedProduct.price === "number") {
      const priceValue = selectedProduct.price; // Assign to variable
      form.setValue(`items.${index}.priceAtSale`, priceValue);

      // Check for active event discounts for this product
      const eventDiscount = activeEventDiscounts.find((discount) =>
        discount.products.some((p) => p.product.id === productId)
      );

      if (eventDiscount) {
        // Apply event discount automatically
        const discountPercentage = Number(eventDiscount.discountPercentage);
        form.setValue(`items.${index}.discount`, discountPercentage.toString());

        toast.success(
          `Diskon event "${eventDiscount.name}" (${discountPercentage}%) diterapkan pada produk "${selectedProduct.name}"`
        );
      } else {
        // Clear any existing discount if no event discount applies
        form.setValue(`items.${index}.discount`, "0");
      }

      // Force recalculation of total immediately
      const currentItems = form.getValues("items");

      // Ensure the item exists before trying to update it
      if (currentItems[index]) {
        currentItems[index].priceAtSale = priceValue;
      }

      const total = currentItems.reduce(
        (sum: number, item: SaleFormValues["items"][number]) => {
          const quantity = item?.quantity ?? 0;
          const price = item?.priceAtSale ?? 0;
          return sum + quantity * price;
        },
        0
      );

      setTotalAmount(total);
      form.setValue("totalAmount", total);
    }
  };

  // Handle form submission
  const onSubmit = (values: SaleFormValues) => {
    startTransition(async () => {
      try {
        // In edit mode, use existing values - don't auto-generate
        let autoTransactionNumber =
          values.transactionNumber || sale.transactionNumber;
        let autoInvoiceRef = values.invoiceRef || sale.invoiceRef;

        // Extract the fields for the sale update (only fields in base SaleSchema)
        // Process items to include discount information
        const processedItems = values.items.map((item) => {
          const discountPercentage = parseFloat(item.discount || "0");
          const discountAmount = (item.priceAtSale * discountPercentage) / 100;

          // Find the event discount that was applied to this product
          const eventDiscount = activeEventDiscounts.find((discount) =>
            discount.products.some((p) => p.product.id === item.productId)
          );

          return {
            ...item,
            discountPercentage:
              discountPercentage > 0 ? discountPercentage : undefined,
            discountAmount: discountAmount > 0 ? discountAmount : undefined,
            eventDiscountId: eventDiscount?.id || undefined,
            eventDiscountName: eventDiscount?.name || undefined,
          };
        });

        const saleData = {
          items: processedItems,
          totalAmount: values.totalAmount,
          transactionNumber: autoTransactionNumber,
          invoiceRef: autoInvoiceRef,
          isDraft: false, // Always set to false when publishing
          // Customer relationship
          customerId: values.customerId,
          // Additional fields
          customerRefNumber: values.customerRefNumber,
          shippingAddress: values.shippingAddress,
          paymentDueDate: values.paymentDueDate,
          paymentTerms: values.paymentTerms,
          warehouse: values.warehouse,
          tags: values.tags,
          memo: values.memo,
          lampiran: values.lampiran,
          priceIncludesTax: values.priceIncludesTax,
        };

        const result = await updateSale(sale.id, saleData);
        if (result.success) {
          toast.success(result.success);
          // Redirect after a short delay
          router.push(
            `/dashboard/sales/detail/${autoTransactionNumber || sale.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  // Save as draft to database
  const saveAsDraft = () => {
    startTransition(async () => {
      try {
        // Get current form values
        const values = form.getValues();

        // Generate transaction number and invoice reference if needed
        let autoTransactionNumber = values.transactionNumber;
        let autoInvoiceRef = values.invoiceRef;

        if (!autoTransactionNumber) {
          const transactionResult = await getNextTransactionNumber("TRX");
          if (transactionResult.success) {
            autoTransactionNumber = transactionResult.nextNumber;
          }
        }

        if (!autoInvoiceRef) {
          const invoiceResult = await getNextInvoiceNumber();
          if (invoiceResult.success) {
            autoInvoiceRef = invoiceResult.nextNumber;
          }
        }

        // Extract the fields for the sale update (only fields in base SaleSchema)
        // Process items to include discount information
        const processedItems = values.items.map((item) => {
          const discountPercentage = parseFloat(item.discount || "0");
          const discountAmount = (item.priceAtSale * discountPercentage) / 100;

          // Find the event discount that was applied to this product
          const eventDiscount = activeEventDiscounts.find((discount) =>
            discount.products.some((p) => p.product.id === item.productId)
          );

          return {
            ...item,
            discountPercentage:
              discountPercentage > 0 ? discountPercentage : undefined,
            discountAmount: discountAmount > 0 ? discountAmount : undefined,
            eventDiscountId: eventDiscount?.id || undefined,
            eventDiscountName: eventDiscount?.name || undefined,
          };
        });

        const saleData = {
          items: processedItems,
          totalAmount: values.totalAmount,
          transactionNumber: autoTransactionNumber,
          invoiceRef: autoInvoiceRef,
          isDraft: true, // Set to true when saving as draft
          // Customer relationship
          customerId: values.customerId,
          // Additional fields
          customerRefNumber: values.customerRefNumber,
          shippingAddress: values.shippingAddress,
          paymentDueDate: values.paymentDueDate,
          paymentTerms: values.paymentTerms,
          warehouse: values.warehouse,
          tags: values.tags,
          memo: values.memo,
          lampiran: values.lampiran,
          priceIncludesTax: values.priceIncludesTax,
        };

        const result = await updateSale(sale.id, saleData);
        if (result.success) {
          toast.success("Penjualan berhasil disimpan sebagai draft!");
          // Redirect after a short delay
          router.push(
            `/dashboard/sales/detail/${autoTransactionNumber || sale.id}`
          );
        } else if (result.error) {
          toast.error(result.error);
        } else {
          toast.error("Terjadi kesalahan yang tidak diketahui.");
        }
      } catch (error) {
        console.error("Submit Error:", error);
        toast.error("Gagal menghubungi server.");
      }
    });
  };

  return (
    <div className="w-full px-4 py-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold">Edit Penjualan</h1>
          <p className="text-muted-foreground">
            Perbarui transaksi penjualan dengan mengisi detail di bawah ini
          </p>
        </div>
        <Button variant="outline" asChild className="gap-2 hidden md:flex">
          <Link
            href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
          >
            <ArrowLeft className="h-4 w-4" />
            Kembali
          </Link>
        </Button>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <div className="space-y-6">
            {/* Main Form Content */}
            <CombinedSaleForm
              control={form.control}
              isPending={isPending}
              products={products}
              items={items}
              fields={fields}
              append={append}
              remove={remove}
              handleProductChange={handleProductChange}
              totalAmount={totalAmount}
              createdAt={sale.createdAt}
              setValue={form.setValue}
              trigger={form.trigger}
              activeEventDiscounts={activeEventDiscounts}
            />
          </div>

          {/* Form Actions */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700 flex justify-between md:justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              asChild
              disabled={isPending}
            >
              <Link
                href={`/dashboard/sales/detail/${sale.transactionNumber || sale.id}`}
              >
                Batal
              </Link>
            </Button>
            <Button
              type="button"
              variant="secondary"
              disabled={isPending}
              className="gap-2 cursor-pointer hover:bg-primary/10"
              onClick={saveAsDraft}
            >
              <Save className="h-4 w-4" />
              <span>
                <span className="hidden md:inline">Simpan ke </span>Draft
              </span>
            </Button>
            <Button
              type="submit"
              disabled={isPending}
              className="gap-2 cursor-pointer"
            >
              {isPending ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  <span>Menyimpan...</span>
                </>
              ) : (
                <>
                  <Check className="h-4 w-4" />
                  <span>Simpan Perubahan</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default EnhancedSaleEditPage;
