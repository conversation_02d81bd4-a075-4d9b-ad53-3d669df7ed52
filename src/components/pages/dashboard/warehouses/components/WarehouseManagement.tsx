"use client";

import React, { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { Plus, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Warehouse, WarehouseColumnVisibility } from "@/types/warehouse";
import { getWarehouses } from "@/actions/entities/warehouses";
import { WarehouseTable } from "./WarehouseTable";
import { WarehouseDialog } from "./WarehouseDialog";
import { WarehouseSummaryCards } from "./WarehouseSummaryCards";
import { WarehouseActions } from "./WarehouseActions";
import { defaultWarehouseColumnVisibility } from "../config/columnConfig";

export const WarehouseManagement: React.FC = () => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState<Warehouse | null>(
    null
  );
  const [columnVisibility, setColumnVisibility] =
    useState<WarehouseColumnVisibility>(() => {
      if (typeof window !== "undefined") {
        const savedVisibility = localStorage.getItem(
          "warehouseColumnVisibility"
        );
        return savedVisibility
          ? JSON.parse(savedVisibility)
          : defaultWarehouseColumnVisibility;
      }
      return defaultWarehouseColumnVisibility;
    });

  // Load warehouses
  const loadWarehouses = async () => {
    try {
      setLoading(true);
      const data = await getWarehouses();
      setWarehouses(data);
    } catch (error) {
      console.error("Error loading warehouses:", error);
      toast.error("Gagal memuat data gudang");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadWarehouses();
  }, []);

  // Save column visibility to localStorage whenever it changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(
        "warehouseColumnVisibility",
        JSON.stringify(columnVisibility)
      );
    }
  }, [columnVisibility]);

  // Filter warehouses based on search term
  const filteredWarehouses = warehouses.filter(
    (warehouse) =>
      warehouse.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (warehouse.description &&
        warehouse.description
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      (warehouse.address &&
        warehouse.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleCreateWarehouse = () => {
    setEditingWarehouse(null);
    setIsDialogOpen(true);
  };

  const handleEditWarehouse = (warehouse: Warehouse) => {
    setEditingWarehouse(warehouse);
    setIsDialogOpen(true);
  };

  const handleDialogClose = () => {
    setIsDialogOpen(false);
    setEditingWarehouse(null);
  };

  const handleWarehouseSuccess = () => {
    loadWarehouses();
    handleDialogClose();
  };

  const handleRefresh = () => {
    loadWarehouses();
    toast.success("Data gudang berhasil diperbarui");
  };

  const handleExport = () => {
    toast.info("Fitur export akan segera hadir!");
  };

  // Type-safe wrapper for column visibility changes
  const handleColumnVisibilityChange = useCallback(
    (visibility: Record<string, boolean>) => {
      setColumnVisibility(visibility as unknown as WarehouseColumnVisibility);
    },
    []
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <WarehouseSummaryCards warehouses={warehouses} />

      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2 w-full sm:w-auto">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari gudang..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="flex gap-2">
          {/* Action Buttons Component */}
          <WarehouseActions
            columnVisibility={columnVisibility}
            onColumnVisibilityChange={handleColumnVisibilityChange}
            onRefresh={handleRefresh}
            onExport={handleExport}
            loading={loading}
          />

          {/* Add Warehouse Button - kept in original position */}
          <Button className="cursor-pointer" onClick={handleCreateWarehouse}>
            <Plus className="h-4 w-4" />
            Tambah Gudang
          </Button>
        </div>
      </div>

      {/* Warehouses Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Daftar Gudang</span>
            <Badge variant="secondary">
              {filteredWarehouses.length} gudang
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <WarehouseTable
            warehouses={filteredWarehouses}
            columnVisibility={columnVisibility}
            onEdit={handleEditWarehouse}
            onRefresh={loadWarehouses}
          />
        </CardContent>
      </Card>

      {/* Warehouse Dialog */}
      <WarehouseDialog
        open={isDialogOpen}
        onClose={handleDialogClose}
        warehouse={editingWarehouse}
        onSuccess={handleWarehouseSuccess}
      />
    </div>
  );
};
