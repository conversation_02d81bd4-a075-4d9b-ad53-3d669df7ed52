"use client";

import React, { useState } from "react";
import { Control } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Eye, EyeOff } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  OnboardingFormData,
  positionOptions,
  employeeCountOptions,
} from "../types";
import PhoneInput from "./PhoneInput";

interface BusinessProfileStepProps {
  control: Control<OnboardingFormData>;
  isPending: boolean;
}

const BusinessProfileStep: React.FC<BusinessProfileStepProps> = ({
  control,
  isPending,
}) => {
  const [showPassword, setShowPassword] = useState(false);

  // Password strength checker
  const checkPasswordStrength = (password: string) => {
    let strength = 0;
    let feedback = [];

    if (password.length >= 8) strength++;
    else feedback.push("Minimal 8 karakter");

    if (/[a-z]/.test(password)) strength++;
    else feedback.push("Huruf kecil");

    if (/[A-Z]/.test(password)) strength++;
    else feedback.push("Huruf besar");

    if (/\d/.test(password)) strength++;
    else feedback.push("Angka");

    if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      strength++;
      feedback = feedback.filter((f) => f !== "Karakter khusus");
    }

    return { strength, feedback };
  };

  return (
    <div className="space-y-5">
      {/* Company Name */}
      <FormField
        control={control}
        name="companyName"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-semibold">
              Nama Perusahaan
              <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <FormControl>
              <Input
                placeholder="Masukkan nama perusahaan"
                {...field}
                disabled={isPending}
                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Company Username */}
      <FormField
        control={control}
        name="companyUsername"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-semibold">
              Username Perusahaan
              <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <FormControl>
              <Input
                placeholder="username_perusahaan"
                {...field}
                disabled={isPending}
                className="h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
              />
            </FormControl>
            <FormDescription className="text-gray-500 text-sm">
              Username unik untuk perusahaan Anda (3-20 karakter)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Phone Number */}
      <PhoneInput control={control} isPending={isPending} />

      {/* Password */}
      <FormField
        control={control}
        name="password"
        render={({ field }) => {
          const { strength, feedback } = checkPasswordStrength(
            field.value || ""
          );

          return (
            <FormItem>
              <FormLabel className="text-gray-700 font-semibold">
                Buat Password Baru
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type={showPassword ? "text" : "password"}
                    placeholder="Masukkan password"
                    {...field}
                    disabled={isPending}
                    className="h-11 pr-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500 text-gray-600"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent cursor-pointer"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </div>
              </FormControl>

              {/* Password Strength Indicator */}
              {field.value && (
                <div className="space-y-2">
                  <div className="flex gap-1">
                    {[1, 2, 3, 4].map((level) => (
                      <div
                        key={level}
                        className={`h-1.5 flex-1 rounded-full transition-colors duration-300 ${
                          strength >= level
                            ? strength <= 2
                              ? "bg-red-500"
                              : strength === 3
                                ? "bg-yellow-500"
                                : "bg-green-500"
                            : "bg-gray-200"
                        }`}
                      />
                    ))}
                  </div>
                  {feedback.length > 0 && (
                    <p className="text-xs text-gray-500">
                      Diperlukan: {feedback.join(", ")}
                    </p>
                  )}
                </div>
              )}
              <FormMessage />
            </FormItem>
          );
        }}
      />

      {/* Position */}
      <FormField
        control={control}
        name="position"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-semibold">
              Jabatan
              <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              disabled={isPending}
            >
              <FormControl>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer transition-all duration-200 hover:border-blue-400 text-gray-600">
                  <SelectValue placeholder="Pilih jabatan Anda" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {positionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Employee Count */}
      <FormField
        control={control}
        name="employeeCount"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-semibold">
              Jumlah Karyawan
              <span className="text-red-500 ml-1">*</span>
            </FormLabel>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              disabled={isPending}
            >
              <FormControl>
                <SelectTrigger className="w-full h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 cursor-pointer transition-all duration-200 hover:border-blue-400 text-gray-600">
                  <SelectValue placeholder="Pilih jumlah karyawan" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {employeeCountOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default BusinessProfileStep;
