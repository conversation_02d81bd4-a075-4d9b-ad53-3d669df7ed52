"use client";

import React from "react";
import { OnboardingStep } from "../types";

interface StepIndicatorProps {
  currentStep: OnboardingStep;
  totalSteps: number;
}

const StepIndicator: React.FC<StepIndicatorProps> = ({
  currentStep,
  totalSteps,
}) => {
  const steps = [
    { number: 1, title: "Profil Bisnis", description: "Informasi dasar" },
    { number: 2, title: "Preferensi", description: "Pengaturan akun" },
  ];

  return (
    <div className="w-full space-y-6">
      {/* Step Labels */}
      <div className="flex justify-between items-center relative">
        {steps.map((step, index) => (
          <div
            key={step.number}
            className="flex flex-col items-center flex-1 relative z-10"
          >
            <div
              className={`w-12 h-12 rounded-full flex items-center justify-center font-bold text-sm transition-all duration-300 border-2 ${
                currentStep >= step.number
                  ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg border-blue-600 transform scale-105"
                  : currentStep === step.number - 1
                    ? "bg-white text-blue-600 border-blue-300 shadow-md"
                    : "bg-gray-100 text-gray-400 border-gray-200"
              }`}
            >
              {currentStep > step.number ? (
                <svg
                  className="w-5 h-5"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              ) : (
                step.number
              )}
            </div>
            <div className="mt-3 text-center">
              <p
                className={`text-sm font-semibold transition-colors duration-300 ${
                  currentStep >= step.number ? "text-blue-600" : "text-gray-500"
                }`}
              >
                {step.title}
              </p>
              <p className="text-xs text-gray-400 mt-1">{step.description}</p>
            </div>
          </div>
        ))}

        {/* Connecting Line Background */}
        <div className="absolute top-6 left-0 right-0 h-0.5 bg-gray-200 -z-10"></div>

        {/* Animated Progress Line */}
        <div
          className="absolute top-6 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-blue-600 -z-10 transition-all duration-700 ease-out rounded-full"
          style={{
            width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%`,
          }}
        ></div>
      </div>

      {/* Enhanced Progress Bar */}
      <div className="relative bg-gray-200 rounded-full h-3 overflow-hidden shadow-inner">
        <div
          className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 via-blue-600 to-blue-700 rounded-full transition-all duration-700 ease-out shadow-sm"
          style={{
            width: `${(currentStep / totalSteps) * 100}%`,
          }}
        >
          {/* Animated shine effect */}
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-30 animate-pulse"></div>
        </div>

        {/* Progress percentage text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-xs text-white font-semibold">
            {Math.round((currentStep / totalSteps) * 100)}%
          </span>
        </div>
      </div>
    </div>
  );
};

export default StepIndicator;
