"use client";

import React, { useState } from "react";
import { Control } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { OnboardingFormData } from "../types";

interface PhoneInputProps {
  control: Control<OnboardingFormData>;
  isPending: boolean;
}

const countryOptions = [
  { code: "+62", country: "ID", flag: "🇮🇩", name: "Indonesia" },
  { code: "+1", country: "US", flag: "🇺🇸", name: "United States" },
  { code: "+44", country: "GB", flag: "🇬🇧", name: "United Kingdom" },
  { code: "+65", country: "SG", flag: "🇸🇬", name: "Singapore" },
  { code: "+60", country: "MY", flag: "🇲🇾", name: "Malaysia" },
  { code: "+66", country: "TH", flag: "🇹🇭", name: "Thailand" },
  { code: "+84", country: "VN", flag: "🇻🇳", name: "Vietnam" },
  { code: "+63", country: "PH", flag: "🇵🇭", name: "Philippines" },
  { code: "+86", country: "CN", flag: "🇨🇳", name: "China" },
  { code: "+81", country: "JP", flag: "🇯🇵", name: "Japan" },
  { code: "+82", country: "KR", flag: "🇰🇷", name: "South Korea" },
  { code: "+91", country: "IN", flag: "🇮🇳", name: "India" },
  { code: "+61", country: "AU", flag: "🇦🇺", name: "Australia" },
];

const PhoneInput: React.FC<PhoneInputProps> = ({ control, isPending }) => {
  const [selectedCountry, setSelectedCountry] = useState(countryOptions[0]);

  return (
    <FormField
      control={control}
      name="phoneNumber"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-gray-700 font-semibold">
            Nomor Handphone
            <span className="text-red-500 ml-1">*</span>
          </FormLabel>
          <FormControl>
            <div className="flex gap-2 items-stretch">
              {/* Country Code Selector */}
              <Select
                value={selectedCountry.code}
                onValueChange={(value) => {
                  const country = countryOptions.find((c) => c.code === value);
                  if (country) {
                    setSelectedCountry(country);
                  }
                }}
                disabled={isPending}
              >
                <SelectTrigger className="w-24 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 flex items-center px-3 py-2">
                  <SelectValue>
                    <div className="flex items-center gap-1">
                      <span className="text-sm">{selectedCountry.flag}</span>
                      <span className="text-xs font-mono text-gray-600">
                        {selectedCountry.code}
                      </span>
                    </div>
                  </SelectValue>
                </SelectTrigger>
                <SelectContent className="max-h-60 bg-white text-gray-600">
                  {countryOptions.map((country) => (
                    <SelectItem
                      key={country.code}
                      value={country.code}
                      className="cursor-pointer focus:bg-gray-100 focus:text-gray-600"
                    >
                      <div className="flex items-center gap-2">
                        <span>{country.flag}</span>
                        <span className="font-mono text-sm">
                          {country.code}
                        </span>
                        <span className="text-sm text-gray-600">
                          {country.name}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Phone Number Input */}
              <Input
                placeholder={
                  selectedCountry.code === "+62"
                    ? "8123456789"
                    : selectedCountry.code === "+1"
                      ? "2345678901"
                      : "123456789"
                }
                {...field}
                value={field.value?.replace(/^\+\d+\s*/, "") || ""}
                onChange={(e) => {
                  const phoneNumber = e.target.value.replace(/\D/g, "");
                  field.onChange(`${selectedCountry.code} ${phoneNumber}`);
                }}
                disabled={isPending}
                className="flex-1 h-11 border-gray-300 focus:border-blue-500 focus:ring-blue-500 px-3 py-2 text-gray-600"
              />
            </div>
          </FormControl>
          <div className="text-xs text-gray-500 mt-1">
            Format: {selectedCountry.code} diikuti nomor tanpa awalan 0
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default PhoneInput;
