"use client";

import React, { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Search } from "lucide-react";
import { toast } from "sonner";
import { EventDiscountSchema } from "@/schemas/zod";
import {
  createEventDiscount,
  updateEventDiscount,
} from "@/actions/event-discounts";
import { getProducts } from "@/lib/get-products";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { EventDiscount } from "./types";

interface Product {
  id: string;
  name: string;
  price: number;
}

interface EventDiscountDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
  editingDiscount?: EventDiscount | null;
}

type FormValues = z.infer<typeof EventDiscountSchema>;

const EventDiscountDialog: React.FC<EventDiscountDialogProps> = ({
  open,
  onClose,
  onSuccess,
  editingDiscount,
}) => {
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [startDateOpen, setStartDateOpen] = useState(false);
  const [endDateOpen, setEndDateOpen] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(EventDiscountSchema),
    defaultValues: {
      name: "",
      description: "",
      discountPercentage: 0,
      startDate: new Date(),
      endDate: new Date(),
      isActive: true,
      productIds: [],
    },
  });

  // Fetch products
  const fetchProducts = async () => {
    try {
      setLoadingProducts(true);
      const products = await getProducts();
      // Transform the products to match our interface
      const transformedProducts = products.map((product) => ({
        id: product.id,
        name: product.name,
        price: product.price,
      }));
      setProducts(transformedProducts);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setLoadingProducts(false);
    }
  };

  useEffect(() => {
    if (open) {
      fetchProducts();
    }
  }, [open]);

  // Reset form when dialog opens/closes or editing discount changes
  useEffect(() => {
    if (open) {
      if (editingDiscount) {
        form.reset({
          name: editingDiscount.name,
          description: editingDiscount.description || "",
          discountPercentage: Number(editingDiscount.discountPercentage),
          startDate: new Date(editingDiscount.startDate),
          endDate: new Date(editingDiscount.endDate),
          isActive: editingDiscount.isActive,
          productIds: editingDiscount.products.map((p) => p.product.id),
        });
      } else {
        form.reset({
          name: "",
          description: "",
          discountPercentage: 0,
          startDate: new Date(),
          endDate: new Date(),
          isActive: true,
          productIds: [],
        });
      }
    }
  }, [open, editingDiscount, form]);

  const onSubmit = async (values: FormValues) => {
    try {
      setLoading(true);

      let result;
      if (editingDiscount) {
        result = await updateEventDiscount(editingDiscount.id, values);
      } else {
        result = await createEventDiscount(values);
      }

      if (result.success) {
        toast.success(result.success);
        onSuccess();
      } else {
        toast.error(result.error || "Failed to save event discount");
      }
    } catch (error) {
      console.error("Error saving event discount:", error);
      toast.error("Failed to save event discount");
    } finally {
      setLoading(false);
    }
  };

  const selectedProductIds = form.watch("productIds");
  const filteredProducts = products.filter((product) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleProductToggle = useCallback(
    (productId: string) => {
      const currentIds = form.getValues("productIds");
      const newIds = currentIds.includes(productId)
        ? currentIds.filter((id) => id !== productId)
        : [...currentIds, productId];
      form.setValue("productIds", newIds, { shouldValidate: true });
    },
    [form]
  );

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto z-[9998] rounded-md">
        <DialogHeader>
          <DialogTitle>
            {editingDiscount ? "Edit Diskon Event" : "Buat Diskon Event"}
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nama Diskon Event <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Contoh: Flash Sale Akhir Tahun"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="discountPercentage"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Persentase Diskon (%){" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        step="0.01"
                        placeholder="10"
                        {...field}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Deskripsi</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Deskripsi diskon event (opsional)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      Tanggal Mulai <span className="text-red-500">*</span>
                    </FormLabel>
                    <Popover
                      modal={true}
                      open={startDateOpen}
                      onOpenChange={setStartDateOpen}
                    >
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pilih tanggal</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[9999]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        onOpenAutoFocus={(e) => e.preventDefault()}
                        onInteractOutside={(e) => e.preventDefault()}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            field.onChange(date);
                            setStartDateOpen(false);
                          }}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>
                      Tanggal Berakhir <span className="text-red-500">*</span>
                    </FormLabel>
                    <Popover
                      modal={true}
                      open={endDateOpen}
                      onOpenChange={setEndDateOpen}
                    >
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pilih tanggal</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent
                        className="w-auto p-0 z-[9999]"
                        align="start"
                        side="bottom"
                        sideOffset={4}
                        onOpenAutoFocus={(e) => e.preventDefault()}
                        onInteractOutside={(e) => e.preventDefault()}
                      >
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            field.onChange(date);
                            setEndDateOpen(false);
                          }}
                          disabled={(date) => date < new Date("1900-01-01")}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Product Selection */}
            <FormField
              control={form.control}
              name="productIds"
              render={() => (
                <FormItem>
                  <FormLabel>
                    Pilih Produk <span className="text-red-500">*</span>
                  </FormLabel>

                  {/* Search */}
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Cari produk..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* Selected Products */}
                  {selectedProductIds.length > 0 && (
                    <div className="space-y-2">
                      <div className="text-sm font-medium">
                        Produk Terpilih ({selectedProductIds.length})
                      </div>
                      <div className="flex flex-wrap gap-2">
                        {selectedProductIds.map((productId) => {
                          const product = products.find(
                            (p) => p.id === productId
                          );
                          return product ? (
                            <Badge key={productId} variant="secondary">
                              {product.name}
                            </Badge>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}

                  {/* Product List */}
                  <div className="border rounded-md max-h-60 overflow-y-auto">
                    {loadingProducts ? (
                      <div className="p-4 text-center text-muted-foreground">
                        Loading products...
                      </div>
                    ) : filteredProducts.length === 0 ? (
                      <div className="p-4 text-center text-muted-foreground">
                        {searchTerm
                          ? "Tidak ada produk yang ditemukan"
                          : "Belum ada produk"}
                      </div>
                    ) : (
                      <div className="p-2 space-y-2">
                        {filteredProducts.map((product) => (
                          <div
                            key={product.id}
                            className="flex items-center space-x-3 p-2 rounded hover:bg-muted cursor-pointer"
                            onClick={() => handleProductToggle(product.id)}
                          >
                            <div onClick={(e) => e.stopPropagation()}>
                              <Checkbox
                                checked={selectedProductIds.includes(
                                  product.id
                                )}
                                onCheckedChange={() =>
                                  handleProductToggle(product.id)
                                }
                              />
                            </div>
                            <div className="flex-1">
                              <div className="font-medium">{product.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {new Intl.NumberFormat("id-ID", {
                                  style: "currency",
                                  currency: "IDR",
                                  minimumFractionDigits: 0,
                                }).format(product.price)}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Active Status */}
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Aktifkan diskon event</FormLabel>
                    <div className="text-sm text-muted-foreground">
                      Diskon event akan aktif sesuai periode yang ditentukan
                    </div>
                  </div>
                </FormItem>
              )}
            />

            {/* Actions */}
            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                className="cursor-pointer"
                onClick={onClose}
              >
                Batal
              </Button>
              <Button
                type="submit"
                className="cursor-pointer"
                disabled={loading}
              >
                {loading ? "Menyimpan..." : editingDiscount ? "Update" : "Buat"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EventDiscountDialog;
