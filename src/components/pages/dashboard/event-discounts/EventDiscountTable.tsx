"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Edit,
  Trash2,
  Power,
  PowerOff,
  Calendar,
  Package,
  Share2,
  LoaderCircle,
} from "lucide-react";
import { toast } from "sonner";
import {
  deleteEventDiscount,
  toggleEventDiscountStatus,
} from "@/actions/event-discounts";
import { format } from "date-fns";
import { id } from "date-fns/locale";
import { EventDiscount } from "./types";

interface EventDiscountTableProps {
  eventDiscounts: EventDiscount[];
  loading: boolean;
  onEdit: (discount: EventDiscount) => void;
  onRefresh: () => void;
}

const EventDiscountTable: React.FC<EventDiscountTableProps> = ({
  eventDiscounts,
  loading,
  onEdit,
  onRefresh,
}) => {
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [togglingId, setTogglingId] = useState<string | null>(null);

  const handleDelete = async (id: string) => {
    try {
      setDeletingId(id);
      const result = await deleteEventDiscount(id);
      if (result.success) {
        toast.success(result.success);
        onRefresh();
      } else {
        toast.error(result.error || "Failed to delete event discount");
      }
    } catch (error) {
      console.error("Error deleting event discount:", error);
      toast.error("Failed to delete event discount");
    } finally {
      setDeletingId(null);
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      setTogglingId(id);
      const result = await toggleEventDiscountStatus(id);
      if (result.success) {
        toast.success(result.success);
        onRefresh();
      } else {
        toast.error(result.error || "Failed to toggle event discount status");
      }
    } catch (error) {
      console.error("Error toggling event discount status:", error);
      toast.error("Failed to toggle event discount status");
    } finally {
      setTogglingId(null);
    }
  };

  const getStatusBadge = (discount: EventDiscount) => {
    if (!discount.isActive) {
      return <Badge variant="secondary">Nonaktif</Badge>;
    }

    const now = new Date();
    const startDate = new Date(discount.startDate);
    const endDate = new Date(discount.endDate);

    if (now < startDate) {
      return <Badge variant="outline">Akan Datang</Badge>;
    } else if (now > endDate) {
      return (
        <Badge variant="destructive" className="text-white dark:text-white/90">
          Berakhir
        </Badge>
      );
    } else {
      return <Badge variant="default">Aktif</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (eventDiscounts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-8 text-center">
        <Package className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Belum ada diskon event</h3>
        <p className="text-muted-foreground">
          Buat diskon event pertama Anda untuk mulai memberikan diskon pada
          produk.
        </p>
      </div>
    );
  }

  return (
    <div className="relative overflow-x-auto bg-white dark:bg-gray-800 shadow-sm border border-gray-200 dark:border-gray-700 rounded-lg">
      <table className="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead className="sticky top-0 z-10 text-xs text-gray-700 dark:text-gray-300 uppercase bg-white shadow-[0_2px_4px_rgba(0,0,0,0.05)] dark:bg-gray-700">
          <tr>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Nama
            </th>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Diskon
            </th>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Periode
            </th>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Produk
            </th>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Penjualan
            </th>
            <th
              scope="col"
              className="px-4 py-3 border-r border-gray-200 dark:border-gray-700"
            >
              Status
            </th>
            <th scope="col" className="px-6 py-3 text-right">
              Aksi
            </th>
          </tr>
        </thead>
        <tbody>
          {eventDiscounts.length > 0 ? (
            eventDiscounts.map((discount) => (
              <tr
                key={discount.id}
                className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 font-medium text-gray-900 dark:text-gray-100">
                  <div>
                    <div className="font-medium">{discount.name}</div>
                    {discount.description && (
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        {discount.description}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400">
                  <Badge variant="outline">
                    {discount.discountPercentage}%
                  </Badge>
                </td>
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400">
                  <div className="text-sm">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {format(new Date(discount.startDate), "dd MMM yyyy", {
                        locale: id,
                      })}
                    </div>
                    <div className="text-gray-400 dark:text-gray-500">
                      s/d{" "}
                      {format(new Date(discount.endDate), "dd MMM yyyy", {
                        locale: id,
                      })}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400">
                  <div className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    {discount._count.products}
                  </div>
                </td>
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400">
                  {discount._count.sales}
                </td>
                <td className="px-4 py-4 whitespace-nowrap border-r border-gray-200 dark:border-gray-700 text-gray-500 dark:text-gray-400">
                  {getStatusBadge(discount)}
                </td>
                <td className="px-6 py-4 text-right whitespace-nowrap border-l border-gray-200 dark:border-gray-700">
                  <div className="flex justify-end space-x-1">
                    {/* Action Button (Toggle Status) */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className={`h-8 w-8 text-white cursor-pointer ${
                            discount.isActive
                              ? "bg-green-500 hover:bg-green-400"
                              : "bg-red-500 hover:bg-red-400"
                          }`}
                          onClick={() => handleToggleStatus(discount.id)}
                          disabled={togglingId === discount.id}
                        >
                          {togglingId === discount.id ? (
                            <LoaderCircle className="h-4 w-4 animate-spin" />
                          ) : discount.isActive ? (
                            <PowerOff className="h-4 w-4" />
                          ) : (
                            <Power className="h-4 w-4" />
                          )}
                          <span className="sr-only">Toggle Status</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{discount.isActive ? "Nonaktifkan" : "Aktifkan"}</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Share Button (WhatsApp) */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-green-500 text-white cursor-pointer hover:bg-green-400"
                          onClick={() => {
                            const message = `Diskon Event: ${discount.name} - ${discount.discountPercentage}% OFF! Periode: ${format(new Date(discount.startDate), "dd MMM yyyy", { locale: id })} - ${format(new Date(discount.endDate), "dd MMM yyyy", { locale: id })}`;
                            const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
                            window.open(whatsappUrl, "_blank");
                          }}
                        >
                          <Share2 className="h-4 w-4" />
                          <span className="sr-only">Share via WhatsApp</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Bagikan</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Edit Button */}
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 bg-blue-500 text-white cursor-pointer hover:bg-blue-400"
                          onClick={() => onEdit(discount)}
                        >
                          <Edit className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Edit</p>
                      </TooltipContent>
                    </Tooltip>

                    {/* Delete Button */}
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 bg-red-500 text-white cursor-pointer hover:bg-red-400"
                              disabled={deletingId === discount.id}
                            >
                              {deletingId === discount.id ? (
                                <LoaderCircle className="h-4 w-4 animate-spin" />
                              ) : (
                                <Trash2 className="h-4 w-4" />
                              )}
                              <span className="sr-only">Delete</span>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Hapus</p>
                          </TooltipContent>
                        </Tooltip>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>
                            Hapus Diskon Event
                          </AlertDialogTitle>
                          <AlertDialogDescription>
                            Apakah Anda yakin ingin menghapus diskon event
                            &quot;
                            {discount.name}&quot;? Tindakan ini tidak dapat
                            dibatalkan.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Batal</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={() => handleDelete(discount.id)}
                            disabled={deletingId === discount.id}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                          >
                            {deletingId === discount.id
                              ? "Menghapus..."
                              : "Hapus"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </td>
              </tr>
            ))
          ) : (
            <tr className="bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
              <td
                colSpan={7}
                className="px-4 py-8 text-center text-gray-500 dark:text-gray-400"
              >
                {loading
                  ? "Memuat data..."
                  : "Tidak ada diskon event yang ditemukan"}
              </td>
            </tr>
          )}
        </tbody>
      </table>
    </div>
  );
};

export default EventDiscountTable;
