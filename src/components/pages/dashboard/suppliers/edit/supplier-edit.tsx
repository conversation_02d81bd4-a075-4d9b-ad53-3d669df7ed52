"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Supplier as PrismaSupplier } from "@prisma/client";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ChevronDown, Store } from "lucide-react";
import { toast } from "sonner";
import { updateSupplier } from "@/actions/entities/suppliers";

interface SupplierEditPageProps {
  supplier: PrismaSupplier;
}

const SupplierEditPage: React.FC<SupplierEditPageProps> = ({ supplier }) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // Parse existing supplier data to match new-contact form structure
  const parseContactName = (contactName: string | null) => {
    if (!contactName) return { firstName: "", middleName: "", lastName: "" };
    const parts = contactName.split(" ");
    return {
      firstName: parts[0] || "",
      middleName: parts.length > 2 ? parts.slice(1, -1).join(" ") : "",
      lastName: parts.length > 1 ? parts[parts.length - 1] : "",
    };
  };

  const parsedName = parseContactName(supplier.contactName);

  // Initialize form data with existing supplier data in new-contact format
  const [formData, setFormData] = useState({
    displayName: supplier.name || "",
    contactGroup: "supplier" as "customer" | "supplier" | "",
    firstName: supplier.firstName || parsedName.firstName,
    middleName: supplier.middleName || parsedName.middleName,
    lastName: supplier.lastName || parsedName.lastName,
    phone: supplier.phone || "",
    telephone: supplier.telephone || "",
    fax: supplier.fax || "",
    email: supplier.email || "",
    identityType: supplier.identityType || "",
    identityNumber: supplier.identityNumber || "",
    NIK: supplier.NIK || "",
    NPWP: supplier.NPWP || "",
    companyName: supplier.companyName || "",
    otherInfo: supplier.otherInfo || "",
    billingAddress: supplier.billingAddress || supplier.address || "",
    shippingAddress: supplier.shippingAddress || "",
    sameAsShipping: supplier.sameAsShipping || false,
    bankName: supplier.bankName || "",
    bankBranch: supplier.bankBranch || "",
    accountHolder: supplier.accountHolder || "",
    accountNumber: supplier.accountNumber || "",
    notes: supplier.notes || "",
  });

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    if (!formData.displayName.trim()) {
      toast.error("Nama tampilan wajib diisi!");
      return;
    }

    setIsLoading(true);
    try {
      // Convert form data back to supplier format with all enhanced fields
      const supplierData = {
        name: formData.displayName,
        firstName: formData.firstName || undefined,
        middleName: formData.middleName || undefined,
        lastName: formData.lastName || undefined,
        contactName:
          formData.firstName && formData.lastName
            ? `${formData.firstName} ${formData.lastName}`.trim()
            : formData.firstName || formData.lastName || undefined,
        phone: formData.phone || undefined,
        telephone: formData.telephone || undefined,
        fax: formData.fax || undefined,
        email: formData.email || "",
        identityType: formData.identityType || undefined,
        identityNumber: formData.identityNumber || undefined,
        NIK: formData.NIK || undefined,
        NPWP: formData.NPWP || undefined,
        companyName: formData.companyName || undefined,
        otherInfo: formData.otherInfo || undefined,
        address: formData.billingAddress || undefined, // Keep for backward compatibility
        billingAddress: formData.billingAddress || undefined,
        shippingAddress: formData.shippingAddress || undefined,
        sameAsShipping: formData.sameAsShipping,
        bankName: formData.bankName || undefined,
        bankBranch: formData.bankBranch || undefined,
        accountHolder: formData.accountHolder || undefined,
        accountNumber: formData.accountNumber || undefined,
        notes: formData.notes || undefined,
      };

      const result = await updateSupplier(supplier.id, supplierData);

      if (result.success) {
        toast.success(result.success);
        router.push(`/dashboard/suppliers/detail/${supplier.id}`);
      } else {
        toast.error(result.error || "Gagal memperbarui supplier");
      }
    } catch (error) {
      console.error("Error updating supplier:", error);
      toast.error("Terjadi kesalahan saat memperbarui supplier");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href={`/dashboard/suppliers/detail/${supplier.id}`}>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center gap-2 cursor-pointer"
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">
              Edit Supplier
            </h1>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="max-w-[900px]">
          {/* Info Kontak Section */}
          <div className="mb-8">
            <div className="flex items-center mb-6">
              <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-green-600 dark:text-green-400 text-sm">
                  👤
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Info kontak
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Display Name */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Nama Tampilan <span className="text-red-500">*</span>
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Ketik nama kontak..."
                  value={formData.displayName}
                  onChange={(e) =>
                    handleInputChange("displayName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Contact Group - Fixed as Supplier */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Grup Kontak
              </label>
              <div className="col-span-3">
                <div className="flex gap-6">
                  <label className="flex items-center cursor-pointer p-3 border rounded-lg bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700">
                    <input
                      type="radio"
                      name="contactGroup"
                      value="supplier"
                      checked={true}
                      disabled={true}
                      className="mr-3 text-green-600 focus:ring-green-500"
                    />
                    <Store className="h-4 w-4 mr-2 text-green-600" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Supplier
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Info Umum Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-green-600 dark:text-green-400 text-sm">
                  📋
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Info umum
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Name Fields */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Nama lengkap
              </label>
              <div className="col-span-3 grid grid-cols-3 gap-4">
                <div>
                  <input
                    type="text"
                    placeholder="Nama depan"
                    value={formData.firstName}
                    onChange={(e) =>
                      handleInputChange("firstName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Nama tengah"
                    value={formData.middleName}
                    onChange={(e) =>
                      handleInputChange("middleName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                <div>
                  <input
                    type="text"
                    placeholder="Nama belakang"
                    value={formData.lastName}
                    onChange={(e) =>
                      handleInputChange("lastName", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
            </div>

            {/* Phone */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nomor handphone
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: 081 2 3374 5678"
                  value={formData.phone}
                  onChange={(e) => handleInputChange("phone", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Telephone */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Telepon
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: 021-12345678"
                  value={formData.telephone}
                  onChange={(e) =>
                    handleInputChange("telephone", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Fax */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Fax
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: 021-87654321"
                  value={formData.fax}
                  onChange={(e) => handleInputChange("fax", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Email */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Email
              </label>
              <div className="col-span-3">
                <input
                  type="email"
                  placeholder="Enter email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Company Name */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nama Perusahaan
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Nama perusahaan"
                  value={formData.companyName}
                  onChange={(e) =>
                    handleInputChange("companyName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Identity Type */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Jenis Identitas
              </label>
              <div className="col-span-3">
                <select
                  value={formData.identityType}
                  onChange={(e) =>
                    handleInputChange("identityType", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="">Pilih jenis identitas</option>
                  <option value="KTP">KTP</option>
                  <option value="SIM">SIM</option>
                  <option value="Passport">Passport</option>
                </select>
              </div>
            </div>

            {/* Identity Number */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nomor Identitas
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Nomor identitas"
                  value={formData.identityNumber}
                  onChange={(e) =>
                    handleInputChange("identityNumber", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* NIK */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                NIK
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="NIK"
                  value={formData.NIK}
                  onChange={(e) => handleInputChange("NIK", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* NPWP */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                NPWP
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="NPWP"
                  value={formData.NPWP}
                  onChange={(e) => handleInputChange("NPWP", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Billing Address */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Alamat penagihan
              </label>
              <div className="col-span-3">
                <textarea
                  placeholder="e.g. Jalan Indonesia Blok L No. 22"
                  rows={3}
                  value={formData.billingAddress}
                  onChange={(e) =>
                    handleInputChange("billingAddress", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>
            </div>

            {/* Same as Shipping Checkbox */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Alamat sama
              </label>
              <div className="col-span-3">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={formData.sameAsShipping}
                    onChange={(e) =>
                      handleInputChange("sameAsShipping", e.target.checked)
                    }
                    className="mr-2 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    Alamat pengiriman sama dengan alamat penagihan
                  </span>
                </label>
              </div>
            </div>

            {/* Shipping Address */}
            {!formData.sameAsShipping && (
              <div className="mb-4 grid grid-cols-4 gap-4 items-start">
                <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                  Alamat pengiriman
                </label>
                <div className="col-span-3">
                  <textarea
                    placeholder="e.g. Jalan Indonesia Blok L No. 22"
                    rows={3}
                    value={formData.shippingAddress}
                    onChange={(e) =>
                      handleInputChange("shippingAddress", e.target.value)
                    }
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                  ></textarea>
                </div>
              </div>
            )}

            {/* Notes */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-start">
              <label className="text-sm text-gray-600 dark:text-gray-400 pt-2">
                Catatan
              </label>
              <div className="col-span-3">
                <textarea
                  placeholder="Masukkan catatan tambahan"
                  rows={3}
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                ></textarea>
              </div>
            </div>
          </div>

          {/* Bank Information Section */}
          <div className="mb-8">
            <div className="flex items-center mb-4">
              <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded flex items-center justify-center mr-2">
                <span className="text-green-600 dark:text-green-400 text-sm">
                  🏦
                </span>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Informasi Bank
              </h3>
              <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
            </div>

            {/* Bank Name */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nama Bank
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: Bank BCA"
                  value={formData.bankName || ""}
                  onChange={(e) =>
                    handleInputChange("bankName", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Bank Branch */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Cabang Bank
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: KCP Jakarta Pusat"
                  value={formData.bankBranch || ""}
                  onChange={(e) =>
                    handleInputChange("bankBranch", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Account Holder */}
            <div className="mb-4 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nama Pemegang Rekening
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Nama sesuai rekening bank"
                  value={formData.accountHolder || ""}
                  onChange={(e) =>
                    handleInputChange("accountHolder", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>

            {/* Account Number */}
            <div className="mb-6 grid grid-cols-4 gap-4 items-center">
              <label className="text-sm text-gray-600 dark:text-gray-400">
                Nomor Rekening
              </label>
              <div className="col-span-3">
                <input
                  type="text"
                  placeholder="Contoh: **********"
                  value={formData.accountNumber || ""}
                  onChange={(e) =>
                    handleInputChange("accountNumber", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 dark:bg-gray-700 dark:text-white"
                />
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <Button
              variant="outline"
              onClick={() =>
                router.push(`/dashboard/suppliers/detail/${supplier.id}`)
              }
            >
              Batal
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isLoading}
              className="bg-green-600 hover:bg-green-700"
            >
              {isLoading ? "Menyimpan..." : "Simpan"}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierEditPage;
