"use client";

import React, { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Progress } from "@/components/ui/progress";
import {
  Download,
  Upload,
  CheckCircle,
  Info,
  Settings,
  AlertCircle,
  TrendingUp,
  Calendar,
} from "lucide-react";
import * as XLSX from "xlsx-js-style";
import { toast } from "sonner";
import { createServiceImportTemplate } from "@/utils/imports/services";
import { importServices } from "@/actions/import/services";
import { getServiceReportData } from "@/actions/reports";
import { DatePicker } from "@/components/ui/date-picker";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createServicesExcelReport } from "@/utils/exports/servicesExport";

interface ImportSummary {
  servicesCreated: number;
  customersCreated: number;
  errors: string[];
}

interface ExportConfig {
  reportType: "harian" | "bulanan" | "tahunan";
  selectedDate: Date;
  selectedMonth: number;
  selectedYear: number;
  includeSummary: boolean;
  includeCharts: boolean;
}

interface ServiceImportExportProps {
  onRefresh?: () => void;
}

export const ServiceImportExport: React.FC<ServiceImportExportProps> = ({
  onRefresh,
}) => {
  // Import states
  const [showImportDialog, setShowImportDialog] = useState(false);
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importSummary, setImportSummary] = useState<ImportSummary | null>(
    null
  );
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Export states
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportConfig, setExportConfig] = useState<ExportConfig>({
    reportType: "bulanan",
    selectedDate: new Date(),
    selectedMonth: new Date().getMonth(),
    selectedYear: new Date().getFullYear(),
    includeSummary: false,
    includeCharts: false,
  });

  // Download template
  const downloadTemplate = () => {
    try {
      const workbook = createServiceImportTemplate();
      const fileName = `template-import-servis-${new Date().toISOString().split("T")[0]}.xlsx`;
      XLSX.writeFile(workbook, fileName);
      toast.success("Template berhasil diunduh!");
    } catch (error) {
      console.error("Template download error:", error);
      toast.error("Gagal mengunduh template. Silakan coba lagi.");
    }
  };

  // Handle import
  const handleImport = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.name.match(/\.(xlsx|xls)$/)) {
      toast.error(
        "Format file tidak didukung. Gunakan file Excel (.xlsx atau .xls)"
      );
      return;
    }

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error("Ukuran file terlalu besar. Maksimal 10MB");
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportSummary(null);

    try {
      setImportProgress(20);

      const arrayBuffer = await file.arrayBuffer();
      setImportProgress(40);

      const result = await importServices(arrayBuffer);
      setImportProgress(80);

      if (result.error) {
        toast.error(result.error);
        setImportSummary(result.summary);
      } else {
        toast.success(result.success || "Import berhasil!");
        setImportSummary(result.summary);

        // Auto-refresh data on successful import
        if (onRefresh) {
          setTimeout(() => {
            onRefresh();
          }, 1500);
        }
      }

      setImportProgress(100);
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Gagal mengimpor data servis");
      setImportSummary({
        servicesCreated: 0,
        customersCreated: 0,
        errors: [
          error instanceof Error ? error.message : "Error tidak diketahui",
        ],
      });
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  // Handle export
  const handleExport = async () => {
    if (!exportConfig.reportType) {
      toast.error("Pilih jenis laporan terlebih dahulu");
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      console.log("Starting export with config:", exportConfig);

      // Fetch service data
      setExportProgress(20);
      const serviceReportResult = await getServiceReportData({
        reportType: exportConfig.reportType,
        selectedDate: exportConfig.selectedDate,
        selectedMonth: exportConfig.selectedMonth,
        selectedYear: exportConfig.selectedYear,
      });

      console.log("Service report result:", serviceReportResult);

      if (serviceReportResult.error) {
        toast.error(serviceReportResult.error);
        return;
      }

      const services = serviceReportResult.data || [];

      if (services.length === 0) {
        toast.error(
          "Tidak ada data servis untuk diekspor pada periode yang dipilih"
        );
        return;
      }

      console.log(`Found ${services.length} services for export:`, services);

      setExportProgress(50);

      // Transform data for the new export template
      const servicesData = services.map((service) => ({
        serviceNumber: service.serviceNumber || "",
        receivedDate: service.receivedDate,
        customerName: service.customerName || "",
        customerPhone: service.customerPhone || "",
        deviceType: service.deviceType || "",
        deviceBrand: service.deviceBrand || "",
        deviceModel: service.deviceModel || "",
        problemDescription: service.problemDescription || "",
        status: service.status || "",
        estimatedCost: service.estimatedCost || 0,
        finalCost: service.finalCost || 0,
      }));

      setExportProgress(70);

      // Determine period label
      let periodLabel = "";
      switch (exportConfig.reportType) {
        case "harian":
          periodLabel = `Harian - ${exportConfig.selectedDate?.toLocaleDateString("id-ID")}`;
          break;
        case "bulanan":
          periodLabel = `Bulanan - ${exportConfig.selectedYear}-${String(exportConfig.selectedMonth + 1).padStart(2, "0")}`;
          break;
        case "tahunan":
          periodLabel = `Tahunan - ${exportConfig.selectedYear}`;
          break;
      }

      // Generate professional Excel report using new template
      const workbook = createServicesExcelReport(
        servicesData,
        [],
        periodLabel,
        {
          companyName: "Kasir Online",
          reportTitle: "Laporan Servis",
        }
      );

      setExportProgress(90);

      // Generate filename
      let filename = "";
      switch (exportConfig.reportType) {
        case "harian":
          filename = `laporan-servis-harian-${exportConfig.selectedDate?.toISOString().split("T")[0]}.xlsx`;
          break;
        case "bulanan":
          filename = `laporan-servis-bulanan-${exportConfig.selectedYear}-${String(exportConfig.selectedMonth + 1).padStart(2, "0")}.xlsx`;
          break;
        case "tahunan":
          filename = `laporan-servis-tahunan-${exportConfig.selectedYear}.xlsx`;
          break;
      }

      // Write and download file
      XLSX.writeFile(workbook, filename);

      setExportProgress(100);

      toast.success(
        `Laporan servis berhasil diekspor dengan ${services.length} data!`
      );
      setShowExportDialog(false);
    } catch (error) {
      console.error("Export error:", error);
      toast.error("Gagal mengekspor data servis");
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  return (
    <div className="flex items-center gap-2">
      {/* Import Button and Dialog */}
      <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-2xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Import Data Servis
            </DialogTitle>
            <DialogDescription className="flex text-left">
              Import data servis dari file Excel dengan mudah dan aman
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Instructions */}
            <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100">
                    Cara Import Data Servis:
                  </h4>
                  <ol className="text-sm text-blue-800 dark:text-blue-200 space-y-1 list-decimal list-inside">
                    <li>
                      Download template Excel dengan klik tombol &quot;Download
                      Template&quot;
                    </li>
                    <li>Isi data servis sesuai format yang tersedia</li>
                    <li>Upload file Excel yang sudah diisi</li>
                    <li>Tunggu proses import selesai</li>
                  </ol>
                </div>
              </div>
            </div>
            {/* Download Template */}
            <div className="space-y-2">
              <h4 className="font-medium">1. Download Template Excel</h4>
              <Button
                onClick={downloadTemplate}
                variant="outline"
                className="w-full cursor-pointer"
              >
                <Download className="mr-2 h-4 w-4" />
                Download Template Servis
              </Button>
            </div>

            <Separator />

            {/* File Upload */}
            <div className="space-y-2">
              <h4 className="font-medium">2. Upload File Excel</h4>
              <div className="border-2 border-dashed border-slate-300 rounded-lg p-6 text-center">
                <Upload className="h-12 w-12 mx-auto text-slate-400 mb-4" />
                <p className="text-sm text-slate-600 mb-2">
                  Klik untuk memilih file atau drag & drop
                </p>
                <p className="text-xs text-slate-500">
                  Format: .xlsx, .xls (Maksimal 10MB)
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".xlsx,.xls"
                  onChange={handleImport}
                  className="hidden"
                />
                <Button
                  variant="outline"
                  onClick={() => fileInputRef.current?.click()}
                  className="mt-4 cursor-pointer"
                  disabled={isImporting}
                >
                  {isImporting ? "Memproses..." : "Pilih File Excel"}
                </Button>
              </div>
            </div>

            {/* Import Progress */}
            {isImporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Import</span>
                  <span>{importProgress}%</span>
                </div>
                <Progress value={importProgress} className="w-full" />
              </div>
            )}

            {/* Import Summary */}
            {importSummary && (
              <div className="space-y-3">
                <h4 className="font-medium">Hasil Import:</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-sm font-medium">Servis Dibuat</span>
                    </div>
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {importSummary.servicesCreated}
                    </div>
                  </div>
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-4 w-4 text-blue-500" />
                      <span className="text-sm font-medium">
                        Pelanggan Dibuat
                      </span>
                    </div>
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {importSummary.customersCreated}
                    </div>
                  </div>
                </div>

                {importSummary.errors.length > 0 && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <AlertCircle className="h-4 w-4 text-red-500" />
                      <span className="text-sm font-medium text-red-800 dark:text-red-200">
                        Error
                      </span>
                    </div>
                    <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                      {importSummary.errors.map((error, index) => (
                        <li key={index}>• {error}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Export Button and Dialog */}
      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogTrigger asChild>
          <Button
            variant="outline"
            className="flex items-center gap-2 cursor-pointer"
          >
            <Download className="h-4 w-4" />
            Export
          </Button>
        </DialogTrigger>
        <DialogContent className="max-w-3xl max-h-[80vh] flex flex-col rounded-md">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Export Data Servis
            </DialogTitle>
            <DialogDescription>
              Pilih periode dan format yang ingin diekspor untuk data servis
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto space-y-4 p-2">
            {/* Period Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Periode Laporan</Label>
              <div className="grid grid-cols-3 gap-2">
                {[
                  { value: "harian", label: "Harian", icon: Calendar },
                  { value: "bulanan", label: "Bulanan", icon: Calendar },
                  { value: "tahunan", label: "Tahunan", icon: Calendar },
                ].map((type) => (
                  <Card
                    key={type.value}
                    className={`cursor-pointer transition-all hover:shadow-sm ${
                      exportConfig.reportType === type.value
                        ? "ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950"
                        : "hover:bg-gray-50 dark:hover:bg-gray-800"
                    }`}
                    onClick={() =>
                      setExportConfig((prev) => ({
                        ...prev,
                        reportType: type.value as any,
                      }))
                    }
                  >
                    <div className="p-3 text-center">
                      <type.icon className="h-5 w-5 mx-auto mb-1" />
                      <div className="text-sm font-medium">{type.label}</div>
                    </div>
                  </Card>
                ))}
              </div>
            </div>

            {/* Date Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                Pilih Tanggal/Periode
              </Label>

              {exportConfig.reportType === "harian" && (
                <div>
                  <DatePicker
                    date={exportConfig.selectedDate}
                    setDate={(date) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedDate: date || new Date(),
                      }));
                    }}
                    placeholder="Pilih tanggal"
                    className="w-full h-9 cursor-pointer"
                  />
                </div>
              )}

              {exportConfig.reportType === "bulanan" && (
                <div className="grid grid-cols-2 gap-2">
                  <Select
                    value={exportConfig.selectedMonth?.toString() || ""}
                    onValueChange={(value) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedMonth: parseInt(value),
                      }));
                    }}
                  >
                    <SelectTrigger className="h-9 w-full">
                      <SelectValue placeholder="Bulan" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 12 }, (_, i) => (
                        <SelectItem key={i} value={i.toString()}>
                          {new Date(0, i).toLocaleDateString("id-ID", {
                            month: "long",
                          })}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="h-11"
                  />
                </div>
              )}

              {exportConfig.reportType === "tahunan" && (
                <div>
                  <Input
                    type="number"
                    min="2020"
                    max="2030"
                    placeholder="Tahun"
                    value={exportConfig.selectedYear || ""}
                    onChange={(e) => {
                      setExportConfig((prev) => ({
                        ...prev,
                        selectedYear:
                          parseInt(e.target.value) || new Date().getFullYear(),
                      }));
                    }}
                    className="w-full h-9"
                  />
                </div>
              )}
            </div>

            <Separator />

            {/* Format Selection - Excel Only */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Format Export</Label>
              <Card className="ring-2 ring-blue-500 bg-blue-50 dark:bg-blue-950">
                <div className="p-3 text-center">
                  <div className="text-lg mb-1">📊</div>
                  <div className="text-sm font-medium">Excel (.xlsx)</div>
                </div>
              </Card>
            </div>

            <Separator />

            {/* Additional Options */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Opsi Tambahan</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeSummary"
                    checked={exportConfig.includeSummary}
                    onCheckedChange={(checked) =>
                      setExportConfig((prev) => ({
                        ...prev,
                        includeSummary: checked as boolean,
                      }))
                    }
                  />
                  <Label
                    htmlFor="includeSummary"
                    className="text-sm cursor-pointer"
                  >
                    Sertakan ringkasan data
                  </Label>
                </div>
              </div>
            </div>

            {/* Export Progress */}
            {isExporting && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Export</span>
                  <span>{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="w-full" />
              </div>
            )}
          </div>

          <div className="flex-shrink-0 flex justify-between pt-4 border-t bg-white dark:bg-gray-900">
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={isExporting}
            >
              Batal
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isExporting ? "Mengekspor..." : "Export Data Servis"}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
