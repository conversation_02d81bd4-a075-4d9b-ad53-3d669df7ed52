import { getServiceStatusDisplayText } from "../types";
import type { Service } from "../types";

/**
 * Horizontal Template 1 - Based on sales template with horizontal orientation
 */
export const renderHorizontalTemplate1 = (service: Service): string => {
  const finalCost = service.finalCost || service.estimatedCost || 0;
  const costNumber =
    typeof finalCost === "number" ? finalCost : Number(finalCost);

  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Faktur Servis - ${service.serviceNumber}</title>
      <style>
        @page {
          size: A4 landscape;
          margin: 1.5cm;
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: #000;
          background-color: white;
          font-size: 12pt;
        }
        .invoice-container {
          max-width: 29.7cm;
          margin: 0 auto;
          padding: 20px;
          box-sizing: border-box;
        }
        .letterhead {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          border-bottom: 2px solid #333;
          padding-bottom: 15px;
        }
        .company-info {
          flex: 2;
        }
        .company-name {
          font-size: 24pt;
          font-weight: bold;
          margin-bottom: 5px;
        }
        .company-details {
          font-size: 10pt;
          color: #666;
          line-height: 1.4;
        }
        .invoice-label {
          flex: 1;
          text-align: right;
        }
        .invoice-title {
          font-size: 18pt;
          font-weight: bold;
          color: #333;
        }
        .service-info {
          display: flex;
          justify-content: space-between;
          margin: 20px 0;
          gap: 40px;
        }
        .customer-info, .service-details {
          flex: 1;
        }
        .info-title {
          font-weight: bold;
          font-size: 11pt;
          margin-bottom: 8px;
          color: #333;
          border-bottom: 1px solid #ddd;
          padding-bottom: 4px;
        }
        .info-content {
          font-size: 10pt;
          line-height: 1.5;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
          font-size: 10pt;
        }
        th, td {
          border: 1px solid #333;
          padding: 8px;
          text-align: left;
        }
        th {
          background-color: #f5f5f5;
          font-weight: bold;
          text-align: center;
        }
        .text-center {
          text-align: center;
        }
        .text-right {
          text-align: right;
        }
        .memo {
          margin: 20px 0;
          padding: 15px;
          background-color: #f9f9f9;
          border-left: 4px solid #333;
        }
        .memo-title {
          font-weight: bold;
          margin-bottom: 8px;
        }
        .signatures {
          display: flex;
          justify-content: space-between;
          margin-top: 40px;
          gap: 40px;
        }
        .signature-box {
          flex: 1;
          text-align: center;
        }
        .signature-line {
          border-bottom: 1px solid #333;
          height: 60px;
          margin-bottom: 8px;
        }
        .signature-name {
          font-weight: bold;
          margin-bottom: 4px;
        }
        .signature-title {
          font-size: 9pt;
          color: #666;
        }
        .footer {
          margin-top: 30px;
          text-align: center;
          font-size: 9pt;
          color: #666;
          border-top: 1px solid #ddd;
          padding-top: 15px;
        }
        @media print {
          body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          .invoice-container {
            padding: 0;
          }
          .no-print {
            display: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="invoice-container">
        <!-- Letterhead -->
        <div class="letterhead">
          <div class="company-info">
            <div class="company-name">KivaPOS</div>
            <div class="company-details">
              Jl. Contoh No. 123, Jakarta<br>
              No. HP: +62 xxx-xxxx-xxxx | Email: <EMAIL>
            </div>
          </div>
          <div class="invoice-label">
            <div class="invoice-title">FAKTUR SERVIS</div>
          </div>
        </div>

        <!-- Service Information -->
        <div class="service-info">
          <div class="customer-info">
            <div class="info-title">Informasi Pelanggan</div>
            <div class="info-content">
              <strong>Nama:</strong> ${service.customerName || "Pelanggan"}<br>
              <strong>No. Servis:</strong> ${service.serviceNumber}<br>
              <strong>Tanggal Diterima:</strong> ${new Date(service.receivedDate).toLocaleDateString("id-ID")}<br>
              ${service.customerPhone ? `<strong>Telepon:</strong> ${service.customerPhone}<br>` : ""}
              ${service.customerEmail ? `<strong>Email:</strong> ${service.customerEmail}<br>` : ""}
            </div>
          </div>
          <div class="service-details">
            <div class="info-title">Detail Perangkat</div>
            <div class="info-content">
              <strong>Jenis:</strong> ${service.deviceType}<br>
              ${service.deviceBrand ? `<strong>Merek:</strong> ${service.deviceBrand}<br>` : ""}
              ${service.deviceModel ? `<strong>Model:</strong> ${service.deviceModel}<br>` : ""}
              <strong>Keluhan:</strong> ${service.problemDescription}<br>
              <strong>Status:</strong> ${getServiceStatusDisplayText(service.status)}
            </div>
          </div>
        </div>

        <!-- Service Details Table -->
        <table>
          <thead>
            <tr>
              <th class="text-center" style="width: 5%;">No.</th>
              <th style="width: 40%;">Deskripsi Servis</th>
              <th class="text-center" style="width: 15%;">Status</th>
              <th class="text-right" style="width: 20%;">Estimasi Biaya</th>
              <th class="text-right" style="width: 20%;">Biaya Final</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-center">1</td>
              <td>${service.problemDescription}</td>
              <td class="text-center">${getServiceStatusDisplayText(service.status)}</td>
              <td class="text-right">${service.estimatedCost ? `Rp ${Number(service.estimatedCost).toLocaleString("id-ID")}` : "-"}</td>
              <td class="text-right">Rp ${costNumber.toLocaleString("id-ID")}</td>
            </tr>
          </tbody>
        </table>

        <!-- Summary Section -->
        <table style="border-collapse: collapse; margin-top: -20px; width: 100%;">
          <tr style="font-weight: bold;">
            <td colspan="4" style="border: none; text-align: right; padding: 4px 10px; line-height: 1.2;">Total Biaya Servis:</td>
            <td style="border: none; text-align: right; padding: 4px 10px; width: 20%; line-height: 1.2;">Rp ${costNumber.toLocaleString("id-ID")}</td>
          </tr>
        </table>

        <!-- Notes Section (if available) -->
        ${
          service.diagnosisNotes
            ? `
        <div class="memo">
          <div class="memo-title">Catatan:</div>
          <div>${service.diagnosisNotes}</div>
        </div>
        `
            : ""
        }

        ${
          service.warrantyPeriod && service.warrantyPeriod > 0
            ? `
        <div class="memo">
          <div class="memo-title">Informasi Garansi:</div>
          <div><strong>Periode Garansi:</strong> ${service.warrantyPeriod} hari dari tanggal penyelesaian servis<br>
          <em>Garansi berlaku untuk kerusakan yang sama dan tidak mencakup kerusakan akibat pemakaian yang tidak wajar.</em></div>
        </div>
        `
            : ""
        }

        <!-- Signatures -->
        <div class="signatures">
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( Teknisi )</div>
            <div class="signature-title">Petugas Servis</div>
          </div>
          <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-name">( ${service.customerName || "Pelanggan"} )</div>
            <div class="signature-title">Pemilik Perangkat</div>
          </div>
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>Faktur ini adalah bukti resmi servis perangkat. Terima kasih atas kepercayaan Anda.</p>
          <p>Dicetak melalui KivaPOS pada ${new Date().toLocaleDateString(
            "id-ID",
            {
              day: "numeric",
              month: "long",
              year: "numeric",
            }
          )}</p>
        </div>
      </div>
    </body>
    </html>
  `;
};
