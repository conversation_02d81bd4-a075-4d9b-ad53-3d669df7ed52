import { renderHorizontalTemplate1 } from "./HorizontalTemplate1";
import { renderVerticalTemplate1 } from "./VerticalTemplate1";
import type { Service } from "../types";

/**
 * Template options for service invoice printing
 */
export type TemplateType =
  | "horizontal1"
  | "horizontal2"
  | "horizontal3"
  | "horizontal4"
  | "vertical1"
  | "vertical2"
  | "vertical3"
  | "vertical4";

/**
 * Template orientation options
 */
export type TemplateOrientation = "horizontal" | "vertical";

/**
 * Get the appropriate template based on the selected template type
 */
export const getServiceInvoiceTemplate = (
  templateType: TemplateType,
  service: Service
): string => {
  switch (templateType) {
    // Horizontal templates
    case "horizontal1":
      return renderHorizontalTemplate1(service);
    case "horizontal2":
      return renderHorizontalTemplate1(service);
    case "horizontal3":
      return renderHorizontalTemplate1(service);
    case "horizontal4":
      return renderHorizontalTemplate1(service);

    // Vertical templates
    case "vertical1":
      return renderVerticalTemplate1(service);
    case "vertical2":
      return renderVerticalTemplate1(service);
    case "vertical3":
      return renderVerticalTemplate1(service);
    case "vertical4":
      return renderVerticalTemplate1(service);

    // Default to horizontal1
    default:
      return renderHorizontalTemplate1(service);
  }
};

/**
 * Get available templates list
 */
export const getAvailableTemplates = (): Array<{
  id: TemplateType;
  name: string;
  orientation: TemplateOrientation;
  description: string;
}> => [
  {
    id: "horizontal1",
    name: "Horizontal 1",
    orientation: "horizontal",
    description: "Template horizontal standar untuk faktur servis",
  },
  {
    id: "horizontal2",
    name: "Horizontal 2",
    orientation: "horizontal",
    description: "Template horizontal dengan detail lebih lengkap",
  },
  {
    id: "horizontal3",
    name: "Horizontal 3",
    orientation: "horizontal",
    description: "Template horizontal dengan layout modern",
  },
  {
    id: "horizontal4",
    name: "Horizontal 4",
    orientation: "horizontal",
    description: "Template horizontal dengan branding kuat",
  },
  {
    id: "vertical1",
    name: "Vertical 1",
    orientation: "vertical",
    description: "Template vertikal standar untuk faktur servis",
  },
  {
    id: "vertical2",
    name: "Vertical 2",
    orientation: "vertical",
    description: "Template vertikal dengan detail lengkap",
  },
  {
    id: "vertical3",
    name: "Vertical 3",
    orientation: "vertical",
    description: "Template vertikal dengan layout modern",
  },
  {
    id: "vertical4",
    name: "Vertical 4",
    orientation: "vertical",
    description: "Template vertikal dengan branding kuat",
  },
];
