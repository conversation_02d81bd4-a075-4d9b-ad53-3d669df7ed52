"use client";

import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { User, Paperclip, Settings, Package } from "lucide-react";
import { ServiceFormValues, Product } from "../types";
import { Control, UseFieldArrayRemove } from "react-hook-form";
import CustomerInfoSection from "./CustomerInfoSection";
import ServiceInfoSection from "./ServiceInfoSection";
import AdditionalInfo from "./AdditionalInfo";
import ServiceSparepartTable from "./ServiceSparepartTable";
import { Separator } from "@/components/ui/separator";

interface CombinedServiceFormProps {
  control: Control<ServiceFormValues>;
  isPending: boolean;
  setValue?: (name: keyof ServiceFormValues, value: any) => void;
  trigger?: (
    name?: keyof ServiceFormValues | (keyof ServiceFormValues)[]
  ) => Promise<boolean>;
  title?: string;
  description?: string;
  isEditMode?: boolean;
  // Sparepart-related props
  products?: Product[];
  spareParts?: ServiceFormValues["spareParts"];
  sparePartFields?: any[];
  appendSparePart?: (value: any) => void;
  removeSparePart?: UseFieldArrayRemove;
  handleSparePartChange?: (index: number, productId: string) => void;
}

const CombinedServiceForm: React.FC<CombinedServiceFormProps> = ({
  control,
  isPending,
  setValue,
  trigger,
  title = "Formulir Servis Baru",
  description = "Lengkapi semua informasi untuk membuat servis baru",
  isEditMode = false,
  // Sparepart-related props
  products = [],
  spareParts = [],
  sparePartFields = [],
  appendSparePart,
  removeSparePart,
  handleSparePartChange,
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-8">
        {/* Customer Information Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <User className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Pelanggan</h3>
          </div>
          <CustomerInfoSection
            control={control}
            isPending={isPending}
            setValue={setValue}
            trigger={trigger}
            isEditMode={isEditMode}
          />
        </div>

        <Separator />

        {/* Service Information Section (Combined) */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Settings className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Servis</h3>
          </div>
          <ServiceInfoSection control={control} isPending={isPending} />
        </div>

        <Separator />

        {/* Sparepart Section */}
        {products.length > 0 &&
          appendSparePart &&
          removeSparePart &&
          handleSparePartChange && (
            <>
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <Package className="h-5 w-5 text-primary" />
                  <h3 className="text-lg font-medium">Sparepart</h3>
                </div>
                <ServiceSparepartTable
                  control={control}
                  isPending={isPending}
                  products={products}
                  spareParts={spareParts}
                  fields={sparePartFields}
                  append={appendSparePart}
                  remove={removeSparePart}
                  handleProductChange={handleSparePartChange}
                />
              </div>

              <Separator />
            </>
          )}

        {/* Additional Info Section */}
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Paperclip className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-medium">Informasi Tambahan</h3>
          </div>
          <AdditionalInfo control={control} isPending={isPending} />
        </div>
      </CardContent>
    </Card>
  );
};

export default CombinedServiceForm;
