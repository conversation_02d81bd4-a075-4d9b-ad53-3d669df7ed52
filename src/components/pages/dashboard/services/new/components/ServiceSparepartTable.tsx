"use client";

import React from "react";
import { ServiceFormValues, Product } from "../types";
import { Control, UseFieldArrayRemove } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { PlusIcon } from "@heroicons/react/24/outline";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/components/ui/form";
import { InfoIcon } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import ServiceSparepartRow from "./ServiceSparepartRow";

interface ServiceSparepartTableProps {
  control: Control<ServiceFormValues>;
  isPending: boolean;
  products: Product[];
  spareParts: ServiceFormValues["spareParts"];
  fields: any[];
  append: (value: any) => void;
  remove: UseFieldArrayRemove;
  handleProductChange: (index: number, productId: string) => void;
}

const ServiceSparepartTable: React.FC<ServiceSparepartTableProps> = ({
  control,
  isPending,
  products,
  spareParts,
  fields,
  append,
  remove,
  handleProductChange,
}) => {
  return (
    <div className="space-y-4 w-full">
      {/* Info Section */}
      <div className="flex items-center space-x-2 mb-4">
        <div className="flex items-center space-x-1">
          <FormLabel className="text-sm font-medium">
            Sparepart yang Digunakan
          </FormLabel>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <InfoIcon className="h-4 w-4 text-gray-400 cursor-help" />
              </TooltipTrigger>
              <TooltipContent>
                <p className="text-xs">
                  Pilih produk/sparepart yang digunakan dalam servis ini.
                  <br />
                  Anda dapat menambahkan beberapa sparepart sekaligus.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      <div
        className="overflow-x-auto"
        style={{ maxWidth: "100%", overflowX: "auto", minHeight: "170px" }}
      >
        <table
          className="w-full border-collapse table-auto"
          style={{ minWidth: "600px" }}
        >
          <thead>
            <tr className="border-b border-gray-200 dark:border-gray-700 bg-gray-100 dark:bg-gray-800 h-12">
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "300px" }}
              >
                Produk/Sparepart <span className="text-red-500 font-bold">*</span>
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "100px" }}
              >
                Kuantitas
              </th>
              <th
                className="text-left py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "100px" }}
              >
                Unit
              </th>
              <th
                className="text-right py-4 px-2 font-medium text-gray-700 dark:text-gray-300"
                style={{ minWidth: "70px" }}
              >
                Aksi
              </th>
            </tr>
          </thead>
          <tbody>
            {fields.map((field, index) => (
              <ServiceSparepartRow
                key={field.id}
                control={control}
                index={index}
                field={field}
                products={products}
                spareParts={spareParts}
                remove={remove}
                handleProductChange={handleProductChange}
                isPending={isPending}
                canRemove={fields.length > 1}
              />
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() =>
            append({
              productId: "",
              quantity: 1,
              unit: "Pcs",
            })
          }
          disabled={isPending}
          className="cursor-pointer"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Tambah Sparepart
        </Button>
      </div>
    </div>
  );
};

export default ServiceSparepartTable;
