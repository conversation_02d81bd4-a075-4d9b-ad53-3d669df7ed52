"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";
import { addCustomer } from "@/actions/entities/customers";
import { Customer } from "../types";

// Customer form schema for the dialog
const customerSchema = z.object({
  name: z.string().min(1, "Nama pelanggan wajib diisi"),
  contactName: z.string().optional(),
  email: z
    .string()
    .email("Format email tidak valid")
    .optional()
    .or(z.literal("")),
  phone: z.string().min(1, "Nomor handphone wajib diisi"),
  address: z.string().optional(),
  NIK: z.string().optional(),
  NPWP: z.string().optional(),
  notes: z.string().optional(),
});

type CustomerFormData = z.infer<typeof customerSchema>;

interface CustomerModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCustomerCreated?: (customer: Customer) => void;
}

const CustomerModal: React.FC<CustomerModalProps> = ({
  open,
  onOpenChange,
  onCustomerCreated,
}) => {
  const [isCreatingCustomer, setIsCreatingCustomer] = React.useState(false);

  // Customer form for dialog
  const customerForm = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      name: "",
      contactName: "",
      email: "",
      phone: "",
      address: "",
      NIK: "",
      NPWP: "",
      notes: "",
    },
  });

  // Customer creation function
  const handleCreateCustomer = async (data: CustomerFormData) => {
    setIsCreatingCustomer(true);
    try {
      // Transform data to match the full customer schema
      const customerData = {
        ...data,
        sameAsShipping: false, // Required field
        billingAddress: data.address || "",
        shippingAddress: data.address || "",
      };

      const result = await addCustomer(customerData);

      if (result.error) {
        toast.error(result.error);
        return;
      }

      if (result.success && result.customer) {
        toast.success(result.success);

        // Create the new customer object
        const newCustomer: Customer = {
          id: result.customer.id,
          name: result.customer.name,
          phone: result.customer.phone || "-",
          email: result.customer.email || "-",
          address: result.customer.address || "-",
          NIK: result.customer.NIK || "-",
          NPWP: result.customer.NPWP || "-",
        };

        // Call the callback with the new customer
        if (onCustomerCreated) {
          onCustomerCreated(newCustomer);
        }

        // Close dialog and reset form
        onOpenChange(false);
        customerForm.reset();
      }
    } catch (error) {
      console.error("Error creating customer:", error);
      toast.error("Terjadi kesalahan saat membuat pelanggan");
    } finally {
      setIsCreatingCustomer(false);
    }
  };

  // Handle customer form submission
  const handleCustomerFormSubmit = customerForm.handleSubmit(handleCreateCustomer);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto rounded-md">
        <DialogHeader>
          <DialogTitle>Tambah Pelanggan Baru</DialogTitle>
          <DialogDescription>
            Buat pelanggan baru untuk servis ini.
          </DialogDescription>
        </DialogHeader>
        <Form {...customerForm}>
          <form onSubmit={handleCustomerFormSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Customer Name */}
              <FormField
                control={customerForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nama Pelanggan{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nama pelanggan"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Contact Name */}
              <FormField
                control={customerForm.control}
                name="contactName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Nama Kontak</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nama kontak"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email */}
              <FormField
                control={customerForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Email pelanggan"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Phone */}
              <FormField
                control={customerForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Nomor Handphone{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor telepon"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* NIK */}
              <FormField
                control={customerForm.control}
                name="NIK"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>NIK</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor Induk Kependudukan"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* NPWP */}
              <FormField
                control={customerForm.control}
                name="NPWP"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>NPWP</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Nomor Pokok Wajib Pajak"
                        {...field}
                        disabled={isCreatingCustomer}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Address */}
            <FormField
              control={customerForm.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Alamat</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Alamat pelanggan"
                      className="resize-none"
                      {...field}
                      disabled={isCreatingCustomer}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-2 pt-4">
              <Button
                className="cursor-pointer"
                type="button"
                variant="outline"
                onClick={() => {
                  onOpenChange(false);
                  customerForm.reset();
                }}
                disabled={isCreatingCustomer}
              >
                Batal
              </Button>
              <Button
                type="submit"
                disabled={isCreatingCustomer}
                className="bg-blue-600 hover:bg-blue-700 cursor-pointer"
              >
                {isCreatingCustomer ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Menyimpan...
                  </>
                ) : (
                  "Simpan Pelanggan"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default CustomerModal;
