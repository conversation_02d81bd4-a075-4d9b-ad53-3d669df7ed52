"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FunnelIcon,
  XMarkIcon,
  AdjustmentsHorizontalIcon,
} from "@heroicons/react/24/outline";

export interface CustomerFilterState {
  category?: string;
  hasEmail?: boolean;
  hasPhone?: boolean;
  hasNIK?: boolean;
  hasNPWP?: boolean;
  isActive?: boolean;
}

interface CustomerFilterProps {
  filters: CustomerFilterState;
  onFilterChange: (filters: CustomerFilterState) => void;
}

export const CustomerFilter: React.FC<CustomerFilterProps> = ({
  filters,
  onFilterChange,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [localFilters, setLocalFilters] =
    useState<CustomerFilterState>(filters);

  // Apply filters
  const applyFilters = () => {
    onFilterChange(localFilters);
    setIsOpen(false);
  };

  // Reset filters
  const resetFilters = () => {
    const emptyFilters: CustomerFilterState = {
      category: undefined,
      hasEmail: undefined,
      hasPhone: undefined,
      hasNIK: undefined,
      hasNPWP: undefined,
      isActive: undefined,
    };
    setLocalFilters(emptyFilters);
    onFilterChange(emptyFilters);
  };

  // Count active filters
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.hasEmail !== undefined) count++;
    if (filters.hasPhone !== undefined) count++;
    if (filters.hasNIK !== undefined) count++;
    if (filters.hasNPWP !== undefined) count++;
    if (filters.isActive !== undefined) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="h-9 relative cursor-pointer"
        >
          <FunnelIcon className=" h-4 w-4" />
          Filter
          {activeFilterCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md rounded-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AdjustmentsHorizontalIcon className="h-5 w-5" />
            Filter Pelanggan
          </DialogTitle>
          <DialogDescription className="flex text-left">
            Gunakan filter untuk mempersempit pencarian pelanggan
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-4 p-2">
          {/* Category Filter */}
          <div className="space-y-2">
            <Label className="text-sm font-medium">Kategori</Label>
            <Select
              value={localFilters.category || "all"}
              onValueChange={(value) =>
                setLocalFilters((prev) => ({
                  ...prev,
                  category: value === "all" ? undefined : value,
                }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Pilih kategori" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Semua Kategori</SelectItem>
                <SelectItem value="regular">Regular</SelectItem>
                <SelectItem value="vip">VIP</SelectItem>
                <SelectItem value="wholesale">Grosir</SelectItem>
                <SelectItem value="corporate">Korporat</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Contact Information Filters */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Informasi Kontak</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasEmail"
                  checked={localFilters.hasEmail === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasEmail: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasEmail" className="text-sm cursor-pointer">
                  Memiliki email
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasPhone"
                  checked={localFilters.hasPhone === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasPhone: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasPhone" className="text-sm cursor-pointer">
                  Memiliki nomor telepon
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Identity Information Filters */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Informasi Identitas</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasNIK"
                  checked={localFilters.hasNIK === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasNIK: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasNIK" className="text-sm cursor-pointer">
                  Memiliki NIK
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="hasNPWP"
                  checked={localFilters.hasNPWP === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      hasNPWP: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="hasNPWP" className="text-sm cursor-pointer">
                  Memiliki NPWP
                </Label>
              </div>
            </div>
          </div>

          <Separator />

          {/* Status Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Status</Label>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isActive"
                  checked={localFilters.isActive === true}
                  onCheckedChange={(checked) =>
                    setLocalFilters((prev) => ({
                      ...prev,
                      isActive: checked ? true : undefined,
                    }))
                  }
                />
                <Label htmlFor="isActive" className="text-sm cursor-pointer">
                  Hanya pelanggan aktif
                </Label>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={resetFilters}
            className="flex items-center gap-2 cursor-pointer"
          >
            <XMarkIcon className="h-4 w-4" />
            Reset
          </Button>
          <div className="flex gap-2">
            <Button
              className="cursor-pointer"
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Batal
            </Button>
            <Button className="cursor-pointer" onClick={applyFilters}>
              Terapkan Filter
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
