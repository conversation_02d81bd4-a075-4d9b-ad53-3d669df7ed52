import React from "react";
import { CustomerStatus } from "../types";
import { Users, UserCheck, UserX } from "lucide-react";

interface CustomerStatusCardsProps {
  customerStatus: CustomerStatus;
}

export const CustomerStatusCards: React.FC<CustomerStatusCardsProps> = ({
  customerStatus,
}) => {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
      {/* Total Customers */}
      <div className="rounded-lg border border-gray-200 bg-blue-50 dark:border-gray-700 dark:bg-blue-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Total Pelanggan
          </div>
          <Users className="h-5 w-5 text-blue-500 dark:text-blue-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Total pelanggan
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {customerStatus.total}
          </div>
        </div>
      </div>

      {/* Active Customers */}
      <div className="rounded-lg border border-gray-200 bg-green-50 dark:border-gray-700 dark:bg-green-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Pelanggan Aktif
          </div>
          <UserCheck className="h-5 w-5 text-green-500 dark:text-green-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Aktif bertransaksi
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {customerStatus.active}
          </div>
        </div>
      </div>

      {/* Inactive Customers */}
      <div className="rounded-lg border border-gray-200 bg-red-50 dark:border-gray-700 dark:bg-red-900/20">
        <div className="flex justify-between items-center p-4">
          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Pelanggan Tidak Aktif
          </div>
          <UserX className="h-5 w-5 text-red-500 dark:text-red-400" />
        </div>
        <div className="px-4 pb-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Tidak aktif bertransaksi
          </div>
          <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            {customerStatus.inactive}
          </div>
        </div>
      </div>
    </div>
  );
};
