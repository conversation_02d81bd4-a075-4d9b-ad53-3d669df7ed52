"use client";

import React from "react";
import { BarChartComponent, formatCurrency } from "./BarChartComponent";
import { Button } from "@/components/ui/button";
import Link from "next/link";

// Type for purchase trend data
export interface PurchaseTrendDataPoint {
  name: string;
  total: number;
}

interface PurchaseTrendBarChartProps {
  data: PurchaseTrendDataPoint[];
  title?: string;
  description?: string;
  showFooter?: boolean;
  className?: string;
}

export function PurchaseTrendBarChart({
  data,
  title = "Tren Pembelian",
  description = "Pembelian 6 bulan terakhir",
  showFooter = false,
  className = "",
}: PurchaseTrendBarChartProps) {
  // Transform data to ensure it works with the BarChartComponent
  const chartData = data.map((item) => ({
    name: item.name,
    total: item.total,
  }));

  // Check if data is empty or all values are zero
  const hasData =
    chartData.length > 0 && chartData.some((item) => item.total > 0);

  return (
    <div className={`group ${className}`}>
      {hasData ? (
        <BarChartComponent
          data={chartData}
          title={title}
          description={description}
          dataKeys={["total"]}
          valueFormatter={formatCurrency}
          height={320}
          className="group-hover:scale-[1.01] transition-transform duration-300"
        />
      ) : (
        <div className="relative overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mb-4">
              <svg
                className="h-6 w-6 text-amber-600 dark:text-amber-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              {description}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Belum ada data pembelian untuk ditampilkan
            </p>
          </div>
        </div>
      )}

      {showFooter && (
        <div className="mt-4">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-full hover:bg-amber-50 hover:text-amber-700 hover:border-amber-300 transition-colors"
          >
            <Link href="/dashboard/purchases">
              <span className="flex items-center">
                Lihat Laporan Lengkap
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
