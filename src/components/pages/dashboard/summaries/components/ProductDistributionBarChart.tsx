"use client";

import React from "react";
import { BarChartComponent } from "./BarChartComponent";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export interface ProductDistributionDataPoint {
  name: string;
  value: number;
}

interface ProductDistributionBarChartProps {
  data: ProductDistributionDataPoint[];
  title?: string;
  description?: string;
  showFooter?: boolean;
  className?: string;
}

export function ProductDistributionBarChart({
  data,
  title = "Distribusi Produk",
  description = "Berdasarkan kategori",
  showFooter = false,
  className = "",
}: ProductDistributionBarChartProps) {
  // Transform data to work with BarChartComponent
  const chartData = data.map((item) => ({
    name: item.name,
    jumlah: item.value,
  }));

  // Custom formatter for product counts (no currency)
  const formatCount = (value: number) => value.toString();

  // Check if data is empty or all values are zero
  const hasData =
    chartData.length > 0 && chartData.some((item) => item.jumlah > 0);

  return (
    <div className={`group ${className}`}>
      {hasData ? (
        <BarChartComponent
          data={chartData}
          title={title}
          description={description}
          dataKeys={["jumlah"]}
          valueFormatter={formatCount}
          height={320}
          className="group-hover:scale-[1.01] transition-transform duration-300"
        />
      ) : (
        <div className="relative overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-full bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center mb-4">
              <svg
                className="h-6 w-6 text-emerald-600 dark:text-emerald-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              {description}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Belum ada data produk untuk ditampilkan
            </p>
          </div>
        </div>
      )}

      {showFooter && (
        <div className="mt-4">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-full hover:bg-emerald-50 hover:text-emerald-700 hover:border-emerald-300 transition-colors"
          >
            <Link href="/dashboard/products">
              <span className="flex items-center">
                Kelola Inventaris
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
