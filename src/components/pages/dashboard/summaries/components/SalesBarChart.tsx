"use client";

import React from "react";
import { BarChartComponent, formatCurrency } from "./BarChartComponent";
import { Button } from "@/components/ui/button";
import Link from "next/link";

export interface SalesChartDataPoint {
  name: string;
  total: number;
}

interface SalesBarChartProps {
  data: SalesChartDataPoint[];
  title?: string;
  description?: string;
  showFooter?: boolean;
  className?: string;
}

export function SalesBarChart({
  data,
  title = "Tren Penjualan",
  description = "Penjualan 6 bulan terakhir",
  showFooter = false,
  className = "",
}: SalesBarChartProps) {
  // Transform data to ensure it works with the BarChartComponent
  const chartData = data.map((item) => ({
    name: item.name,
    total: item.total,
  }));

  // Check if data is empty or all values are zero
  const hasData =
    chartData.length > 0 && chartData.some((item) => item.total > 0);

  return (
    <div className={`group ${className}`}>
      {hasData ? (
        <BarChartComponent
          data={chartData}
          title={title}
          description={description}
          dataKeys={["total"]}
          valueFormatter={formatCurrency}
          height={320}
          className="group-hover:scale-[1.01] transition-transform duration-300"
        />
      ) : (
        <div className="relative overflow-hidden border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 shadow-lg rounded-lg p-6">
          <div className="text-center">
            <div className="mx-auto h-12 w-12 rounded-full bg-indigo-100 dark:bg-indigo-900/30 flex items-center justify-center mb-4">
              <svg
                className="h-6 w-6 text-indigo-600 dark:text-indigo-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
              {title}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
              {description}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-300">
              Belum ada data penjualan untuk ditampilkan
            </p>
          </div>
        </div>
      )}

      {showFooter && (
        <div className="mt-4">
          <Button
            asChild
            variant="outline"
            size="sm"
            className="w-full hover:bg-indigo-50 hover:text-indigo-700 hover:border-indigo-300 transition-colors"
          >
            <Link href="/dashboard/sales">
              <span className="flex items-center">
                Lihat Laporan Lengkap
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </span>
            </Link>
          </Button>
        </div>
      )}
    </div>
  );
}
