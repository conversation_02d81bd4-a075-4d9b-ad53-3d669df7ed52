import { ColumnVisibility } from "../types";

export interface ColumnConfig {
  key: keyof ColumnVisibility;
  label: string;
  sortKey: string;
}

// Purchases column configuration with order
export const purchasesColumnConfig: ColumnConfig[] = [
  {
    key: "id",
    label: "ID Transaksi",
    sortKey: "transactionNumber",
  },
  {
    key: "date",
    label: "Tanggal",
    sortKey: "purchaseDate",
  },
  {
    key: "invoiceRef",
    label: "No. Invoice",
    sortKey: "invoiceRef",
  },
  {
    key: "paymentDueDate",
    label: "Tgl. Jatuh Tempo",
    sortKey: "paymentDueDate",
  },
  {
    key: "supplier",
    label: "Supplier",
    sortKey: "supplier.name",
  },
  {
    key: "totalAmount",
    label: "Total",
    sortKey: "totalAmount",
  },
  {
    key: "itemCount",
    label: "Jumlah Item",
    sortKey: "items.length",
  },
  {
    key: "quantity",
    label: "Qty",
    sortKey: "items.quantity",
  },
  {
    key: "tags",
    label: "Tags",
    sortKey: "tags",
  },
];
