import React, { useState } from "react";
import Link from "next/link";
import {
  MagnifyingGlassIcon,
  PlusIcon,
  AdjustmentsHorizontalIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { purchasesColumnConfig } from "../config/columnConfig";
import { ColumnVisibility } from "../types";
import { PurchaseImportExport } from "./PurchaseImportExport";
import { PurchaseFilter, PurchaseFilterState } from "./PurchaseFilter";

interface PurchaseActionsProps {
  columnVisibility: ColumnVisibility;
  setColumnVisibility: React.Dispatch<React.SetStateAction<ColumnVisibility>>;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  filters: PurchaseFilterState;
  onFilterChange: (filters: PurchaseFilterState) => void;
  suppliers?: Array<{ id: string; name: string }>;
  warehouses?: Array<{ id: string; name: string }>;
  selectedPurchases?: string[];
  onBatchDelete?: () => Promise<void>;
  onRefresh?: () => void; // Add refresh callback for after import
}

export const PurchaseActions: React.FC<PurchaseActionsProps> = ({
  columnVisibility,
  setColumnVisibility,
  searchTerm,
  setSearchTerm,
  filters,
  onFilterChange,
  suppliers,
  warehouses,
  selectedPurchases = [],
  onBatchDelete,
  onRefresh,
}) => {
  const [isDeleting, setIsDeleting] = useState(false);

  const handleBatchDelete = async () => {
    setIsDeleting(true);
    try {
      if (!onBatchDelete) return;
      await onBatchDelete();
    } finally {
      setIsDeleting(false);
    }
  };
  return (
    <div className="flex flex-col md:flex-row md:flex-wrap md:items-center justify-between gap-4">
      <div className="flex flex-wrap items-center gap-2">
        {/* Column Visibility Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger className="inline-flex cursor-pointer items-center justify-center rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 shadow-sm hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
            <AdjustmentsHorizontalIcon className="md:mr-2 h-5 w-5" />
            <span className="hidden md:inline">Kolom</span>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-56">
            <DropdownMenuLabel>Tampilkan Kolom</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {purchasesColumnConfig.map((column) => (
              <DropdownMenuCheckboxItem
                key={column.key}
                checked={columnVisibility[column.key]}
                onCheckedChange={(checked) =>
                  setColumnVisibility((prev) => ({
                    ...prev,
                    [column.key]: !!checked,
                  }))
                }
                onSelect={(e) => e.preventDefault()}
              >
                {column.label}
              </DropdownMenuCheckboxItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Filter Component */}
        <PurchaseFilter
          filters={filters}
          onFilterChange={onFilterChange}
          suppliers={suppliers}
          warehouses={warehouses}
        />

        {/* Import/Export Component */}
        <PurchaseImportExport onRefresh={onRefresh} />

        {/* Batch Delete Button - Only visible when items are selected */}
        {selectedPurchases.length > 0 && (
          <button
            type="button"
            onClick={handleBatchDelete}
            disabled={isDeleting}
            className="inline-flex items-center justify-center rounded-md border border-transparent bg-red-600 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer"
          >
            <TrashIcon className="mr-2 h-5 w-5" />
            {isDeleting ? (
              "Menghapus..."
            ) : (
              <span className="hidden md:inline">Hapus</span>
            )}
            ({selectedPurchases.length})
          </button>
        )}
      </div>

      <div className="flex items-center gap-2 w-full sm:w-auto">
        {/* Search Input */}
        <div className="relative">
          <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
            <MagnifyingGlassIcon
              className="h-5 w-5 text-gray-400"
              aria-hidden="true"
            />
          </div>
          <input
            type="text"
            placeholder="Cari pembelian..."
            className="block w-full rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 py-2 pl-10 pr-3 text-sm placeholder-gray-500 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 dark:text-gray-100"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        {/* Add Purchase Button */}
        <Link
          href="/dashboard/purchases/new"
          className="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
        >
          <PlusIcon className="mr-2 h-5 w-5" />
          <span className="">Tambah</span>
        </Link>
      </div>
    </div>
  );
};
