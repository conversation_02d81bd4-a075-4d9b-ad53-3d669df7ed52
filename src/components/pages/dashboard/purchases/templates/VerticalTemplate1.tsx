import type { Purchase } from "../types";

/**
 * Vertical Template 1 - Receipt style with vertical orientation
 * Optimized for both 58mm and 80mm thermal receipt printers
 */
export const renderVerticalTemplate1 = (purchase: Purchase): string => {
  // Calculate total quantity
  const totalQuantity = purchase.items.reduce(
    (sum, item) => sum + item.quantity,
    0
  );

  // Format date and time
  const purchaseDate = new Date(purchase.purchaseDate);
  const formattedDate = purchaseDate
    .toLocaleDateString("id-ID", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    })
    .replace(/\//g, "-");
  const formattedTime = purchaseDate.toLocaleTimeString("id-ID", {
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  // Generate unique receipt ID (used for reference)
  // const receiptId = `P-${Math.random().toString(36).substring(2, 8).toUpperCase()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

  return `
    <!DOCTYPE html>
    <html lang="id">
    <head>
      <meta charset="utf-8"/>
      <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
      <title>Receipt - ${purchase.transactionNumber || purchase.id.substring(0, 8)}</title>
      <style>
        /* Thermal printer optimized styles */
        @page {
          /* Size will be determined by @media queries */
          margin: 0;
        }

        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 0;
          color: black;
          background-color: white;
          font-size: 9pt;
          line-height: 1.2;
        }

        .receipt-container {
          width: 100%;
          padding: 3mm 2mm;
          box-sizing: border-box;
        }

        .text-center {
          text-align: center;
        }

        .text-right {
          text-align: right;
        }

        .header {
          margin-bottom: 3mm;
        }

        .shop-name {
          font-size: 12pt;
          font-weight: bold;
          margin-bottom: 1mm;
        }

        .shop-info {
          font-size: 8pt;
          margin-bottom: 1mm;
        }

        .divider {
          border-top: 1px dashed black;
          margin: 2mm 0;
        }

        .transaction-info {
          display: flex;
          justify-content: space-between;
          font-size: 8pt;
          margin-bottom: 2mm;
        }

        .item {
          margin-bottom: 2mm;
          border-bottom: 1px dashed black;
          padding-bottom: 2mm;
        }

        .item-name {
          font-weight: bold;
          font-size: 9pt;
          margin-bottom: 1mm;
        }

        .item-details {
          display: flex;
          justify-content: space-between;
          font-size: 8pt;
        }

        .qty-box {
          border: 1px solid black;
          display: inline-block;
          padding: 1mm 2mm;
          margin-bottom: 2mm;
          font-size: 8pt;
          font-weight: bold;
        }

        .totals {
          font-size: 8pt;
          margin-bottom: 3mm;
        }

        .total-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 1mm;
        }

        .grand-total {
          font-weight: bold;
          font-size: 10pt;
        }

        .footer {
          text-align: center;
          font-size: 8pt;
          margin-top: 3mm;
        }

        .feedback-link {
          font-size: 7pt;
          text-align: center;
          margin-top: 2mm;
          border-top: 1px dashed black;
          padding-top: 2mm;
        }

        /* 58mm thermal printer styles */
        @media print and (max-width: 58mm) {
          @page {
            size: 58mm auto;
          }

          body {
            width: 58mm;
          }

          .receipt-container {
            max-width: 58mm;
          }

          .shop-name {
            font-size: 11pt;
          }

          .shop-info {
            font-size: 7pt;
          }

          .item-name {
            font-size: 8pt;
          }

          .item-details {
            font-size: 7pt;
          }
        }

        /* 80mm thermal printer styles */
        @media print and (min-width: 59mm) {
          @page {
            size: 80mm auto;
          }

          body {
            width: 80mm;
          }

          .receipt-container {
            max-width: 80mm;
          }

          .shop-name {
            font-size: 14pt;
          }

          .shop-info {
            font-size: 9pt;
          }

          .item-name {
            font-size: 10pt;
          }

          .item-details {
            font-size: 9pt;
          }

          .grand-total {
            font-size: 12pt;
          }
        }
      </style>
    </head>
    <body>
      <div class="receipt-container">
        <!-- Header -->
        <div class="header text-center">
          <div class="shop-name">Kasir Online</div>
          <div class="shop-info">Jl. Sistem Informasi No. 123, Jakarta</div>
          <div class="shop-info">Telp: (*************</div>
          <div class="shop-info">${purchase.id}</div>
        </div>

        <div class="divider"></div>

        <!-- Transaction Info -->
        <div class="transaction-info">
          <div>
            <div>${formattedDate}</div>
            <div>${formattedTime}</div>
            <div>No.${purchase.transactionNumber || purchase.id.substring(0, 5)}</div>
          </div>
          <div class="text-right">
            <div>Supplier:</div>
            <div>${purchase.supplier ? purchase.supplier.name : "-"}</div>
          </div>
        </div>

        <div class="divider"></div>

        <!-- Items -->
        ${purchase.items
          .map(
            (item, index) => `
          <div class="item">
            <div class="item-name">${index + 1}. ${item.product.name}</div>
            <div class="item-details">
              <div>${item.quantity} x ${item.costAtPurchase.toLocaleString("id-ID")}</div>
              <div>Rp ${(item.quantity * item.costAtPurchase).toLocaleString("id-ID")}</div>
            </div>
          </div>
        `
          )
          .join("")}

        <div class="qty-box">Total QTY: ${totalQuantity}</div>

        <!-- Totals -->
        <div class="totals">
          <div class="total-row">
            <div>Sub Total</div>
            <div>Rp ${purchase.totalAmount.toLocaleString("id-ID")}</div>
          </div>
          <div class="total-row grand-total">
            <div>Total</div>
            <div>Rp ${purchase.totalAmount.toLocaleString("id-ID")}</div>
          </div>
        </div>

        <div class="footer">
          <div>Terimakasih Atas Kerjasamanya</div>
          <div>Faktur Pembelian</div>
        </div>

        <div class="feedback-link">
          <div>Kritik dan Saran:</div>
          <div>kivapos.com/kritik-saran</div>
        </div>

        <!-- Paper cut instruction (comment for reference) -->
        <!-- ESC/POS paper cut command would be sent by the printer driver -->
      </div>
    </body>
    </html>
  `;
};
