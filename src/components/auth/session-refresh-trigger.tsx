"use client";

import { useSession } from "next-auth/react";
import { useEffect } from "react";

/**
 * Component that triggers session refresh when mounted
 * Useful for forcing session updates after account switches
 */
export const SessionRefreshTrigger = () => {
  const { update } = useSession();

  useEffect(() => {
    const refreshSession = async () => {
      try {
        console.log("🔄 SessionRefreshTrigger: Forcing session refresh...");
        await update();
        console.log("✅ SessionRefreshTrigger: Session refreshed successfully");
      } catch (error) {
        console.error("❌ SessionRefreshTrigger: Failed to refresh session:", error);
      }
    };

    // Trigger refresh on mount
    refreshSession();
  }, [update]);

  return null; // This component doesn't render anything
};

/**
 * Hook that provides session refresh functionality
 */
export const useForceSessionRefresh = () => {
  const { update } = useSession();

  const forceRefresh = async () => {
    try {
      console.log("🔄 useForceSessionRefresh: Forcing session refresh...");
      await update();
      console.log("✅ useForceSessionRefresh: Session refreshed successfully");
    } catch (error) {
      console.error("❌ useForceSessionRefresh: Failed to refresh session:", error);
    }
  };

  return { forceRefresh };
};
