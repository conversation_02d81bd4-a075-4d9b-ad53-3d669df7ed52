import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import React from "react";
import { checkOnboardingStatus } from "@/actions/onboarding/onboarding";

interface OnboardingGuardProps {
  children: React.ReactNode;
  requireOnboarding?: boolean; // If true, requires onboarding to be completed
}

/**
 * A server component that protects routes based on onboarding completion status
 * @param children The content to render if the user meets the onboarding requirements
 * @param requireOnboarding If true, user must have completed onboarding to access the route
 */
export async function OnboardingGuard({
  children,
  requireOnboarding = true,
}: OnboardingGuardProps) {
  const session = await auth();
  
  // If not authenticated, redirect to login
  if (!session?.user) {
    redirect("/login");
  }

  // Check onboarding status
  const { hasCompleted } = await checkOnboardingStatus();

  // If onboarding is required but not completed, redirect to welcome page
  if (requireOnboarding && !hasCompleted) {
    redirect("/dashboard/welcome");
  }

  // If onboarding is not required but is completed, and this is the welcome page,
  // redirect to dashboard (prevent completed users from accessing welcome page)
  if (!requireOnboarding && hasCompleted) {
    redirect("/dashboard/summaries");
  }

  // User meets the requirements, render children
  return <>{children}</>;
}

/**
 * Higher-order component that wraps a page component with onboarding protection
 * @param Component The page component to wrap
 * @param requireOnboarding If true, user must have completed onboarding to access the page
 */
export function withOnboardingGuard(
  Component: React.ComponentType<any>,
  requireOnboarding = true
) {
  return async function OnboardingProtectedPage(props: any) {
    return (
      <OnboardingGuard requireOnboarding={requireOnboarding}>
        <Component {...props} />
      </OnboardingGuard>
    );
  };
}
