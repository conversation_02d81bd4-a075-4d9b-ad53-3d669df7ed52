"use client";

import { useSession } from "next-auth/react";
import { useEffect } from "react";

/**
 * Component that automatically refreshes session when account type changes
 * This ensures that role detection updates immediately when switching between accounts
 * Optimized to reduce interference with navigation
 */
export const SessionRefresh = () => {
  const { data: session, update } = useSession();

  useEffect(() => {
    let refreshTimeout: NodeJS.Timeout;

    // Listen for storage events that might indicate account switching
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === "account-switch" || e.key === "role-change") {
        console.log("🔄 Account switch detected, refreshing session...");
        // Debounce session refresh to prevent multiple rapid calls
        clearTimeout(refreshTimeout);
        refreshTimeout = setTimeout(() => {
          update();
        }, 500);
      }
    };

    // Listen for custom events that indicate account switching
    const handleAccountSwitch = () => {
      console.log(
        "🔄 Custom account switch event detected, refreshing session..."
      );
      // Debounce session refresh to prevent multiple rapid calls
      clearTimeout(refreshTimeout);
      refreshTimeout = setTimeout(() => {
        update();
      }, 500);
    };

    window.addEventListener("storage", handleStorageChange);
    window.addEventListener("account-switch", handleAccountSwitch);

    return () => {
      window.removeEventListener("storage", handleStorageChange);
      window.removeEventListener("account-switch", handleAccountSwitch);
      clearTimeout(refreshTimeout);
    };
  }, [update]);

  // Only refresh session on mount if explicitly needed (not on every mount)
  // This prevents excessive API calls

  return null; // This component doesn't render anything
};

/**
 * Utility function to trigger session refresh from anywhere in the app
 */
export const triggerSessionRefresh = () => {
  // Dispatch custom event
  window.dispatchEvent(new CustomEvent("account-switch"));

  // Also set localStorage flag
  localStorage.setItem("account-switch", Date.now().toString());
  localStorage.removeItem("account-switch");
};
