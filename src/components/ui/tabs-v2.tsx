"use client";

import * as React from "react";
import * as TabsPrimitive from "@radix-ui/react-tabs";

import { cn } from "@/lib/utils";

function Tabs({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Root>) {
  return (
    <TabsPrimitive.Root
      data-slot="tabs"
      className={cn("flex flex-col gap-3", className)}
      {...props}
    />
  );
}

function TabsList({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.List>) {
  return (
    <TabsPrimitive.List
      data-slot="tabs-list"
      className={cn(
        "bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 inline-flex h-12 w-fit items-center justify-center rounded-xl p-1 shadow-sm",
        "transition-all duration-300",
        className
      )}
      {...props}
    />
  );
}

function TabsTrigger({
  className,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {
  return (
    <TabsPrimitive.Trigger
      data-slot="tabs-trigger"
      className={cn(
        "data-[state=active]:bg-white data-[state=active]:text-gray-900 dark:data-[state=active]:bg-gray-700 dark:data-[state=active]:text-white",
        "data-[state=active]:scale-105 data-[state=active]:shadow-md",
        "focus-visible:ring-2 focus-visible:ring-blue-400 focus-visible:ring-offset-2 focus-visible:ring-offset-white dark:focus-visible:ring-offset-gray-900",
        "inline-flex h-full flex-1 items-center justify-center gap-2 rounded-lg border border-transparent px-4 py-2 text-sm font-semibold tracking-tight whitespace-nowrap",
        "hover:bg-gray-200 dark:hover:bg-gray-600 hover:text-gray-900 dark:hover:text-white",
        "transition-all duration-200 ease-in-out transform hover:scale-[1.02]",
        "disabled:opacity-50 disabled:cursor-not-allowed",
        "[&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-5",
        className
      )}
      {...props}
    />
  );
}

function TabsContent({
  className,
  value,
  ...props
}: React.ComponentProps<typeof TabsPrimitive.Content>) {
  // Calculate animation class based on tab value
  const animationClass = React.useMemo(() => {
    const index = parseInt(value, 10) || value.charCodeAt(0) || 0;
    const directions = [
      "slide-in-from-left-2",
      "slide-in-from-right-2",
      "slide-in-from-top-2",
      "slide-in-from-bottom-2",
    ];
    return directions[Math.abs(index) % directions.length];
  }, [value]);

  return (
    <TabsPrimitive.Content
      data-slot="tabs-content"
      className={cn(
        "flex-1 outline-none mt-4 p-6 bg-white dark:bg-gray-800 rounded-xl shadow-sm",
        "animate-in fade-in-10",
        animationClass,
        "duration-500 ease-[cubic-bezier(0.4,0,0.2,1)]",
        className
      )}
      value={value}
      {...props}
    />
  );
}

export { Tabs, TabsList, TabsTrigger, TabsContent };
