"use client";

import React from "react";
import { DatabaseErrorModal } from "./database-error-modal";

interface DatabaseErrorWrapperProps {
  children: React.ReactNode;
  hasError: boolean;
  errorMessage?: string;
  title?: string;
  description?: string;
}

export const DatabaseErrorWrapper: React.FC<DatabaseErrorWrapperProps> = ({
  children,
  hasError,
  errorMessage,
  title,
  description,
}) => {
  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <>
      {children}
      <DatabaseErrorModal
        isOpen={hasError}
        onRefresh={handleRefresh}
        title={title}
        description={description}
        errorMessage={errorMessage}
      />
    </>
  );
};
