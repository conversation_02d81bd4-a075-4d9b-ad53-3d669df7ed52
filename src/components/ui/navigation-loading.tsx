"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

/**
 * Navigation loading overlay to prevent multiple concurrent navigation requests
 * and provide visual feedback during page transitions
 */
export const NavigationLoading = () => {
  const [isNavigating, setIsNavigating] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    // Set loading state when navigation starts
    const handleNavigationStart = () => {
      setIsNavigating(true);
    };

    // Clear loading state when navigation completes
    const handleNavigationComplete = () => {
      setIsNavigating(false);
    };

    // Listen for navigation events
    window.addEventListener("beforeunload", handleNavigationStart);
    
    // Clear loading state when pathname changes (navigation complete)
    setIsNavigating(false);

    return () => {
      window.removeEventListener("beforeunload", handleNavigationStart);
    };
  }, [pathname]);

  // Add data attribute to body for CSS targeting
  useEffect(() => {
    if (isNavigating) {
      document.body.setAttribute("data-loading", "true");
    } else {
      document.body.removeAttribute("data-loading");
    }

    return () => {
      document.body.removeAttribute("data-loading");
    };
  }, [isNavigating]);

  if (!isNavigating) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm flex items-center justify-center">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg flex items-center space-x-3">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
        <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
          Memuat halaman...
        </span>
      </div>
    </div>
  );
};

/**
 * Hook to manage navigation loading state
 */
export const useNavigationLoading = () => {
  const [isLoading, setIsLoading] = useState(false);

  const startLoading = () => setIsLoading(true);
  const stopLoading = () => setIsLoading(false);

  return {
    isLoading,
    startLoading,
    stopLoading,
  };
};
