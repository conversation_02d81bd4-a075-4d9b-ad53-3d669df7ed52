import React from "react";
import ActivityPage from "@/components/pages/dashboard/activity/activity-page";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Role } from "@prisma/client";

const ActivityPageContainer = async () => {
  return (
    <ProtectedRoute allowedRoles={[Role.OWNER, Role.ADMIN, Role.CASHIER]}>
      <DashboardLayout>
        <div className="py-6 px-1.5 sm:px-2 md:px-4">
          <ActivityPage />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default ActivityPageContainer;
