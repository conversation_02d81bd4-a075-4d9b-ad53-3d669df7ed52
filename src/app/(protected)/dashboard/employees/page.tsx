import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import EmployeesPage from "@/components/pages/dashboard/employees/employees";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getEmployees } from "@/actions/entities/employee";

export const metadata: Metadata = {
  title: "<PERSON><PERSON><PERSON> | KivaPOS",
  description: "Kelola data karyawan dan hak akses sistem",
};

const Employees = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch employee data
  const employeesResult = await getEmployees();

  if (employeesResult.error) {
    console.error("Error fetching employees:", employeesResult.error);
  }

  return (
    <DashboardLayout>
      <div className="py-6 px-1.5 sm:px-2 md:px-4">
        <EmployeesPage employees={employeesResult.employees || []} />
      </div>
    </DashboardLayout>
  );
};

export default Employees;
