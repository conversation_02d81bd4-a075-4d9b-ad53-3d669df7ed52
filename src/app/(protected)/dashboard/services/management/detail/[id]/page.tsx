import { notFound } from "next/navigation";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { getServiceByServiceNumber } from "@/lib/get-services";
import ServiceDetailPage from "@/components/pages/dashboard/services/detail";

interface Props {
  params: Promise<{ id: string }>;
}

// This is an async Server Component
export default async function ServiceDetail(props: Props) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const serviceNumber = params.id;

  // Get the effective user ID (owner or employee)
  const effectiveUserId = await getEffectiveUserId();

  // If no user ID, return 404
  if (!effectiveUserId) {
    notFound();
  }

  // Fetch the service with the given service number
  const service = await getServiceByServiceNumber(serviceNumber, effectiveUserId);

  // If service not found, return 404
  if (!service) {
    notFound();
  }

  return <ServiceDetailPage service={service} />;
}
