import ServicesPage from "@/components/pages/dashboard/services/services";
import React from "react";

export const dynamic = 'force-dynamic'
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { getServices, calculateServiceCounts } from "@/lib/get-services";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Manajemen Layanan | KivaPOS",
  description: "Kelola layanan servis dan perbaikan",
};

// This is an async Server Component
const Services = async () => {
  try {
    // Get the effective user ID (owner or employee)
    // Get the effective user ID (owner or employee)
    const effectiveUserId = await getEffectiveUserId();

    // If no user ID, return empty data
    if (!effectiveUserId) {
      return (
        <ServicesPage
          services={[]}
          serviceCounts={{
            diterima: 0,
            prosesMenungguSparepart: 0,
            selesaiBelumDiambil: 0,
            selesaiSudahDiambil: 0,
            total: 0,
            drafts: 0,
          }}
        />
      );
    }

    // Fetch real service data from the database
    const services = await getServices(effectiveUserId);
    const serviceCounts = await calculateServiceCounts(services);

    // Return the ServicesPage component with data
    return <ServicesPage services={services} serviceCounts={serviceCounts} />;
  } catch (error) {
    console.error("Error fetching service data:", error);
    return (
      <p>Error: Terjadi kesalahan saat mengambil data. Silakan coba lagi.</p>
    );
  }
};

export default Services;
