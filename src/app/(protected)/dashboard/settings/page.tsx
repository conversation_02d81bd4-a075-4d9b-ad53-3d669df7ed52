import React from "react";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import ModernSettingsOverview from "@/components/pages/dashboard/settings/modern-settings-overview";

export default async function SettingsPage() {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <SettingsLayout>
        <ModernSettingsOverview />
      </SettingsLayout>
    </DashboardLayout>
  );
}