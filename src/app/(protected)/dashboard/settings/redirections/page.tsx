import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";
import RedirectionSettings from "@/components/pages/dashboard/settings/redirections/redirection-settings";

export default async function RedirectionSettingsPage() {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  return (
    <DashboardLayout>
      <SettingsLayout>
        <RedirectionSettings />
      </SettingsLayout>
    </DashboardLayout>
  );
}