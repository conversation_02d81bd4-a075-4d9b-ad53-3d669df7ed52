import { auth } from "@/lib/auth";
import { db } from "@/lib/prisma";
import React from "react";
import ProfileSettings from "@/components/pages/dashboard/settings/profile/profile-settings";
import DashboardLayout from "@/components/layout/dashboardlayout";
import SettingsLayout from "@/components/pages/dashboard/settings/settings-layout";

const ProfileSettingsPage = async () => {
  const session = await auth();

  // Fetch user data from database - we can assume session exists due to middleware protection
  const user = session?.user?.id
    ? await db.user.findUnique({
        where: {
          id: session.user.id,
        },
        select: {
          id: true,
          name: true,
          email: true,
          username: true,
          image: true,
          phone: true,
          bio: true,
          birthday: true,
          role: true,
          lastLogin: true,
          createdAt: true,
          currentPlan: true,
          subscriptionExpiry: true,
          businessInfo: {
            select: {
              companyId: true,
              companyName: true,
              companyUsername: true,
            },
          },
        },
      })
    : null;

  // Use a default user object with empty values if user is null
  const userData = user
    ? {
        id: user.id,
        name: user.name,
        email: user.email,
        username: user.username,
        image: user.image,
        phone: user.phone,
        bio: user.bio,
        birthday: user.birthday,
        role: user.role,
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        currentPlan: user.currentPlan,
        subscriptionExpiry: user.subscriptionExpiry,
        companyId: user.businessInfo?.companyId || null,
        companyName: user.businessInfo?.companyName || null,
        companyUsername: user.businessInfo?.companyUsername || null,
      }
    : {
        id: session?.user?.id || "",
        name: session?.user?.name || "",
        email: session?.user?.email || "",
        username: "",
        image: session?.user?.image || "",
        phone: null,
        bio: null,
        birthday: null,
        role: session?.user?.role || "OWNER",
        lastLogin: null,
        createdAt: new Date(),
        currentPlan: "FREE",
        subscriptionExpiry: null,
        companyId: null,
        companyName: null,
        companyUsername: null,
      };

  return (
    <DashboardLayout>
      <SettingsLayout>
        <ProfileSettings user={userData} />
      </SettingsLayout>
    </DashboardLayout>
  );
};

export default ProfileSettingsPage;
