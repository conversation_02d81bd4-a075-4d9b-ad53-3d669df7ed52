import ProductsPage from "@/components/pages/dashboard/products/products";
import React from "react";

export const dynamic = 'force-dynamic'
import { getProducts, getProductStockCounts } from "@/lib/get-products";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { OnboardingGuard } from "@/components/auth/onboarding-guard";
import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import {
  Category,
  Product,
  Tag,
  StockCounts,
} from "@/components/pages/dashboard/products/types";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Produk - KivaPOS",
  description: "Kelola produk dan inventaris Anda",
};

// This is an async Server Component
const Products = async () => {
  let hasError = false;
  let errorMessage = "";
  let formattedProducts: Product[] = [];
  let stockCounts: StockCounts = {
    available: 0,
    low: 0,
    outOfStock: 0,
    needsApproval: 0,
    drafts: 0,
  };
  let categories: Category[] = [];

  try {
    // Fetch products using our utility function that handles employee access
    const serializedProducts = await getProducts({
      orderBy: "createdAt",
      orderDirection: "desc",
      includeOutOfStock: true,
    });

    // Get product stock counts
    stockCounts = await getProductStockCounts();

    // Get categories for filtering
    const effectiveUserId = await getEffectiveUserId();

    if (effectiveUserId) {
      categories = await db.category.findMany({
        where: { userId: effectiveUserId },
        orderBy: { name: "asc" },
      });
    }

    // Convert serializedProducts to match the expected Product type
    // The main issue is converting string[] tags to Tag[] objects
    formattedProducts = serializedProducts.map((product) => {
      // Convert string tags to Tag objects if they exist
      const formattedTags: Tag[] = product.tags
        ? product.tags.map((tag: string) => ({
            id: tag, // Using the tag string as id
            name: tag,
          }))
        : [];

      return {
        ...product,
        tags: formattedTags,
      } as Product;
    });
  } catch (error) {
    console.error("Error fetching product data:", error);
    hasError = true;
    errorMessage =
      error instanceof Error
        ? error.message
        : "Terjadi kesalahan saat mengambil data produk";
  }

  return (
    <OnboardingGuard requireOnboarding={true}>
      <DatabaseErrorWrapper
        hasError={hasError}
        errorMessage={errorMessage}
        title="Gagal Memuat Data Produk"
        description="Terjadi masalah saat mengambil data produk dari database. Silakan refresh halaman untuk mencoba lagi."
      >
        <ProductsPage
          products={formattedProducts}
          stockCounts={stockCounts}
          categories={categories}
        />
      </DatabaseErrorWrapper>
    </OnboardingGuard>
  );
};

export default Products;
