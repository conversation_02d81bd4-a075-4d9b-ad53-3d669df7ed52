import React from "react";
import NotificationsPage from "@/components/pages/dashboard/notifications/notifications-page";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { ProtectedRoute } from "@/components/auth/protected-route";
import { Role } from "@prisma/client";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import { getUserSubscription } from "@/lib/subscription";
import { hasNotificationAccess } from "@/lib/subscription-limits";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Bell, ChevronsUp, Lock } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const NotificationsPageContainer = async () => {
  // Check authentication
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Check subscription access to notifications
  try {
    const userSubscription = await getUserSubscription(session.user.id);
    const hasAccess = hasNotificationAccess(userSubscription.plan);

    if (!hasAccess) {
      // Show access denied page for free plan users
      return (
        <ProtectedRoute allowedRoles={[Role.OWNER, Role.ADMIN, Role.CASHIER]}>
          <DashboardLayout>
            <div className="container mx-auto p-6 max-w-2xl">
              <Card className="border-amber-200 bg-amber-50 dark:bg-amber-900/20">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-amber-100 dark:bg-amber-900/40 flex items-center justify-center">
                    <Lock className="h-8 w-8 text-amber-600 dark:text-amber-400" />
                  </div>
                  <CardTitle className="text-xl text-amber-800 dark:text-amber-200">
                    Akses Notifikasi Terbatas
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <p className="text-amber-700 dark:text-amber-300">
                    Fitur notifikasi tidak tersedia untuk akun Anda. Upgrade ke
                    paket berbayar untuk mengakses notifikasi website dan email.
                  </p>
                  <div className="flex items-center justify-center gap-2 text-sm text-amber-600 dark:text-amber-400">
                    <Bell className="h-4 w-4" />
                    <span>Paket Dasar: Notifikasi Website</span>
                  </div>
                  <div className="flex items-center justify-center gap-2 text-sm text-amber-600 dark:text-amber-400">
                    <Bell className="h-4 w-4" />
                    <span>
                      Paket Pro & Enterprise: Notifikasi Website + Email
                    </span>
                  </div>
                  <div className="pt-4">
                    <Link href="/dashboard/settings/plans">
                      <Button className="bg-amber-600 hover:bg-amber-700 text-white">
                        <ChevronsUp className="h-4 w-4 mr-2" />
                        Upgrade Paket
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </DashboardLayout>
        </ProtectedRoute>
      );
    }
  } catch (error) {
    console.error("Error checking notification access:", error);
    redirect("/dashboard");
  }

  // User has access, show normal notifications page
  return (
    <ProtectedRoute allowedRoles={[Role.OWNER, Role.ADMIN, Role.CASHIER]}>
      <DashboardLayout>
        <div className="py-6 px-1.5 sm:px-2 md:px-4">
          <NotificationsPage />
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default NotificationsPageContainer;
