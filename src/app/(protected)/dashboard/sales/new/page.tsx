import React from "react";
import DashboardLayout from "@/components/layout/dashboardlayout";

export const dynamic = 'force-dynamic'
import EnhancedSalePage from "@/components/pages/dashboard/sales/new";
import { getProducts } from "@/lib/get-products";
import { Product } from "@/components/pages/dashboard/sales/new/types";
import { ErrorCard } from "@/components/ui/error-card";
import { Metadata } from "next";

// Metadata for the page
export const metadata: Metadata = {
  title: "Tambah Penjualan - KivaPOS",
  description: "Tambah transaksi penjualan baru",
};

// This is an async Server Component
export default async function NewSale() {
  try {
    // Fetch ALL products (including out of stock) to show comprehensive information
    const serializedProducts = await getProducts({
      includeOutOfStock: true, // Include all products to show stock status
      orderBy: "createdAt", // Order by creation date to get newest products
      orderDirection: "desc", // Newest first
      excludeDrafts: true, // Exclude draft products
    });

    // Separate products by stock status
    const availableProducts = serializedProducts.filter(
      (product) => (product.stock || 0) > 0
    );
    const outOfStockProducts = serializedProducts.filter(
      (product) => (product.stock || 0) === 0
    );

    // If no products exist at all
    if (serializedProducts.length === 0) {
      return (
        <DashboardLayout>
          <ErrorCard
            title="Tidak ada produk"
            description="Belum ada produk yang terdaftar dalam sistem. Tambahkan produk terlebih dahulu untuk memulai penjualan."
            showAddProductButton={true}
            showPurchaseButton={false}
          />
        </DashboardLayout>
      );
    }

    // If all products are out of stock
    if (availableProducts.length === 0 && outOfStockProducts.length > 0) {
      return (
        <DashboardLayout>
          <ErrorCard
            title="Semua produk habis"
            description={`Anda memiliki ${outOfStockProducts.length} produk, tetapi semuanya sedang habis stok. Buat pembelian untuk menambah stok produk yang ada, atau tambah produk baru untuk melanjutkan penjualan.`}
            retryLink="/dashboard/products"
            retryLabel="Kelola Produk"
            showPurchaseButton={true}
            showAddProductButton={true}
          />
        </DashboardLayout>
      );
    }

    // Adapt ALL products to match the expected Product interface (including out of stock)
    const adaptedProducts: Product[] = serializedProducts.map((product) => ({
      id: product.id,
      name: product.name,
      price: product.price,
      wholesalePrice: product.wholesalePrice || undefined, // Include wholesale price
      stock: product.stock || 0,
      image: product.image || undefined,
      createdAt: product.createdAt || undefined,
    }));

    return (
      <DashboardLayout>
        <EnhancedSalePage
          products={adaptedProducts}
          availableCount={availableProducts.length}
          outOfStockCount={outOfStockProducts.length}
        />
      </DashboardLayout>
    );
  } catch (error) {
    console.error("Error loading products for sale page:", error);
    return (
      <DashboardLayout>
        <ErrorCard
          title="Error"
          description="Terjadi kesalahan saat memuat data produk. Silakan coba lagi nanti."
          retryLink="/dashboard/sales/new"
          retryLabel="Coba Lagi"
        />
      </DashboardLayout>
    );
  }
}
