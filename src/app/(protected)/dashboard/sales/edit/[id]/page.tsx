import React from "react";
import { notFound } from "next/navigation";
import EnhancedSaleEditPage from "@/components/pages/dashboard/sales/edit";
import { getSaleById } from "@/actions/entities/sales";
import { getProducts } from "@/lib/get-products";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { Metadata } from "next";
import { Product } from "@/components/pages/dashboard/sales/new/types";

export const metadata: Metadata = {
  title: "Edit Penjualan - KivaPOS",
  description: "Edit transaksi penjualan",
};

type PageProps = {
  params: Promise<{ id: string }>;
  searchParams?: Promise<Record<string, string | string[]>>;
};

export default async function SaleEdit(props: PageProps) {
  // Get the id from params (which is now a Promise)
  const params = await props.params;
  const id = params.id;

  // Fetch the sale with the given ID
  const saleResult = await getSaleById(id);

  // If sale not found, return 404
  if (!saleResult.sale) {
    notFound();
  }

  // Fetch products for the form
  const fetchedProducts = await getProducts({
    includeOutOfStock: true, // Show all products for editing
    orderBy: "createdAt", // Order by creation date to get newest products
    orderDirection: "desc", // Newest first
    excludeDrafts: true, // Exclude draft products
  });

  // Adapt the products to match the expected Product interface
  const adaptedProducts: Product[] = fetchedProducts.map((product) => ({
    id: product.id,
    name: product.name,
    price: product.price,
    wholesalePrice: product.wholesalePrice || undefined, // Include wholesale price
    stock: product.stock || 0,
    image: product.image || undefined,
    createdAt: product.createdAt || undefined,
  }));

  // Create a complete serialized sale object with all required fields for the edit form
  const serializedSale = {
    id: saleResult.sale.id,
    totalAmount: saleResult.sale.totalAmount,
    saleDate: saleResult.sale.saleDate.toISOString(),
    createdAt: saleResult.sale.createdAt.toISOString(),
    // Customer relationship
    customerId: saleResult.sale.customerId || undefined,
    customer: saleResult.sale.customer
      ? {
          id: saleResult.sale.customer.id,
          name: saleResult.sale.customer.name,
          email: saleResult.sale.customer.email || undefined,
          phone: saleResult.sale.customer.phone || undefined,
          NIK: saleResult.sale.customer.NIK || undefined,
          NPWP: saleResult.sale.customer.NPWP || undefined,
        }
      : undefined,
    // Transaction details
    transactionNumber: saleResult.sale.transactionNumber || undefined,
    invoiceRef: saleResult.sale.invoiceRef || undefined,
    // Additional fields from the sale schema
    customerRefNumber: saleResult.sale.customerRefNumber || undefined,
    shippingAddress: saleResult.sale.shippingAddress || undefined,
    paymentDueDate: saleResult.sale.paymentDueDate
      ? saleResult.sale.paymentDueDate.toISOString()
      : undefined,
    paymentTerms: saleResult.sale.paymentTerms || undefined,
    warehouseId: saleResult.sale.warehouseId || undefined,
    tags: saleResult.sale.tags || [],
    memo: saleResult.sale.memo || undefined,
    lampiran: (saleResult.sale.lampiran || []).map((attachment: any) => ({
      url: attachment?.url || "",
      filename: attachment?.filename || "",
    })),
    priceIncludesTax: saleResult.sale.priceIncludesTax || false,
    // Items with complete product information
    items: saleResult.sale.items.map((item) => ({
      id: item.id,
      quantity: item.quantity,
      priceAtSale: item.priceAtSale,
      productId: item.productId,
      // Include discount and metadata fields from database
      discountPercentage: item.discountPercentage || undefined,
      discountAmount: item.discountAmount || undefined,
      eventDiscountId: item.eventDiscountId || undefined,
      eventDiscountName: item.eventDiscountName || undefined,
      isWholesale: item.isWholesale || false,
      unit: item.unit || "Buah", // Use stored unit or default
      tax: item.tax || "", // Use stored tax or default
      product: {
        id: item.productId,
        name: item.product.name,
        price: item.priceAtSale,
        stock: 0, // Required by the Product type
      },
    })),
  };

  return (
    <DashboardLayout>
      <EnhancedSaleEditPage sale={serializedSale} products={adaptedProducts} />
    </DashboardLayout>
  );
}
