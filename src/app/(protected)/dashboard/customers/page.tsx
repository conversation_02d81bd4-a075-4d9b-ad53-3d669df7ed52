import React from "react";
import { Metadata } from "next";
import DashboardLayout from "@/components/layout/dashboardlayout";
import CustomersPage from "@/components/pages/dashboard/customers/customers";
import { DatabaseErrorWrapper } from "@/components/ui/database-error-wrapper";
import { auth } from "@/lib/auth";
import { getCustomers } from "@/actions/entities/customers";
import { redirect } from "next/navigation";

export const metadata: Metadata = {
  title: "Pelanggan | KivaPOS",
  description: "Kelola data pelanggan Anda",
};

const Customers = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Fetch customers for the current user
  const { customers, error } = await getCustomers();

  return (
    <DashboardLayout>
      <div className="py-6 px-1.5 sm:px-2 md:px-4">
        <DatabaseErrorWrapper
          hasError={!!error}
          errorMessage={error}
          title="Gagal Memuat Data Pelanggan"
          description="Terjadi masalah saat mengambil data pelanggan dari database. Silakan refresh halaman untuk mencoba lagi."
        >
          {/* Pass the fetched customers to the client component */}
          <CustomersPage customers={customers || []} />
        </DatabaseErrorWrapper>
      </div>
    </DashboardLayout>
  );
};

export default Customers;
