import React from "react";
import { auth } from "@/lib/auth";
import { redirect } from "next/navigation";
import DashboardLayout from "@/components/layout/dashboardlayout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { testSubscriptionLimits, testPlanLimits, generateTestReport } from "@/lib/test-subscription-limits";
import UsageSummary from "@/components/subscription/usage-summary";

const TestSubscriptionPage = async () => {
  const session = await auth();
  if (!session?.user?.id) {
    redirect("/login");
  }

  // Run tests
  const userTests = await testSubscriptionLimits(session.user.id);
  const planTests = testPlanLimits();
  const allTests = [...userTests, ...planTests];
  
  const passed = allTests.filter(t => t.passed).length;
  const total = allTests.length;
  const successRate = ((passed / total) * 100).toFixed(1);

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold">Test Subscription System</h1>
          <Badge variant={passed === total ? "default" : "destructive"}>
            {passed}/{total} Tests Passed ({successRate}%)
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Usage Summary */}
          <UsageSummary />

          {/* Test Results Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Test Results Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Total Tests:</span>
                  <span className="font-medium">{total}</span>
                </div>
                <div className="flex justify-between">
                  <span>Passed:</span>
                  <span className="font-medium text-green-600">{passed}</span>
                </div>
                <div className="flex justify-between">
                  <span>Failed:</span>
                  <span className="font-medium text-red-600">{total - passed}</span>
                </div>
                <div className="flex justify-between">
                  <span>Success Rate:</span>
                  <span className="font-medium">{successRate}%</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Detailed Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {allTests.map((test, index) => (
                <div 
                  key={index}
                  className={`p-4 rounded-lg border ${
                    test.passed 
                      ? "border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20" 
                      : "border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20"
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-medium">{test.test}</h3>
                    <Badge variant={test.passed ? "default" : "destructive"}>
                      {test.passed ? "PASS" : "FAIL"}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-2">
                    {test.message}
                  </p>
                  {test.details && (
                    <details className="text-xs">
                      <summary className="cursor-pointer text-muted-foreground">
                        Show Details
                      </summary>
                      <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                        {JSON.stringify(test.details, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Console Report */}
        <Card>
          <CardHeader>
            <CardTitle>Console Report</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-xs bg-muted p-4 rounded overflow-auto">
              {generateTestReport(allTests)}
            </pre>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default TestSubscriptionPage;
