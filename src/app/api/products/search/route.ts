import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

export async function GET(request: NextRequest) {
  try {
    // Get the search query from URL params
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get("q") || "";
    const limit = parseInt(searchParams.get("limit") || "10");

    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return NextResponse.json(
        { error: "Tidak terautentikasi!" },
        { status: 401 }
      );
    }

    // Search products in database
    const products = await db.product.findMany({
      where: {
        userId: effectiveUserId,
        isDraft: false, // Exclude draft products
        OR: [
          { name: { contains: query, mode: "insensitive" } },
          { sku: { contains: query, mode: "insensitive" } },
        ],
      },
      orderBy: {
        createdAt: "desc", // Order by creation date (newest first)
      },
      take: limit,
      include: {
        category: true,
      },
    });

    // Format products for response
    const formattedProducts = products.map((product) => ({
      id: product.id,
      name: product.name,
      cost: product.cost ? product.cost.toNumber() : null,
      image: product.image,
      createdAt: product.createdAt.toISOString(), // Include creation date for sorting
    }));

    return NextResponse.json({ products: formattedProducts });
  } catch (error) {
    console.error("Error searching products:", error);
    return NextResponse.json(
      { error: "Gagal mencari produk" },
      { status: 500 }
    );
  }
}
