import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ productId: string }> }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: "Unauthorized", sales: [] },
        { status: 401 }
      );
    }

    const { productId } = await params;

    // Get pagination parameters from query string
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get("page") || "1");
    const limit = parseInt(url.searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;

    // TODO: Replace this with your actual database query
    // Example using Prisma:
    /*
    const sales = await db.sale.findMany({
      where: {
        items: {
          some: {
            productId: productId
          }
        }
      },
      include: {
        customer: true,
        items: {
          where: {
            productId: productId
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Transform the data to match the expected format
    const transformedSales = sales.map(sale => ({
      id: sale.id,
      date: sale.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
      customer: sale.customer.name,
      quantity: sale.items[0]?.quantity || 0,
      unitPrice: sale.items[0]?.unitPrice || 0,
      totalAmount: sale.items[0]?.totalAmount || 0,
      invoiceNumber: sale.invoiceNumber
    }));
    */

    // Get total count for pagination
    const totalCount = await db.saleItem.count({
      where: {
        productId: productId,
        sale: {
          userId: session.user.id,
        },
      },
    });

    // Fetch sale items for the specific product (only for the authenticated user)
    const saleItems = await db.saleItem.findMany({
      where: {
        productId: productId,
        sale: {
          userId: session.user.id, // Only get sales for the authenticated user
        },
      },
      include: {
        sale: {
          include: {
            customer: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip: skip,
      take: limit,
    });

    // Transform the data to match the expected format
    const transformedSales = saleItems.map((item) => ({
      id: item.id,
      date: item.sale.saleDate.toISOString().split("T")[0], // Format as YYYY-MM-DD
      customer: item.sale.customer?.name || "Customer Tidak Diketahui",
      quantity: item.quantity,
      unitPrice: Number(item.priceAtSale), // Convert Decimal to number
      totalAmount: Number(item.priceAtSale) * item.quantity, // Calculate total
      invoiceNumber:
        item.sale.invoiceRef || item.sale.transactionNumber || item.sale.id,
    }));

    // Calculate pagination metadata
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      sales: transformedSales,
      pagination: {
        currentPage: page,
        totalPages: totalPages,
        totalCount: totalCount,
        limit: limit,
        hasNextPage: hasNextPage,
        hasPrevPage: hasPrevPage,
      },
    });
  } catch (error) {
    console.error("Error fetching sales history:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch sales history",
        sales: [],
      },
      { status: 500 }
    );
  }
}

/*
Example database schema for reference:

Table: sales
- id (string/uuid)
- date (datetime)
- customerId (string)
- invoiceNumber (string)
- totalAmount (decimal)
- createdAt (datetime)
- updatedAt (datetime)

Table: sale_items
- id (string/uuid)
- saleId (string)
- productId (string)
- quantity (integer)
- unitPrice (decimal)
- totalAmount (decimal)

Table: customers
- id (string/uuid)
- name (string)
- email (string)
- phone (string)
- address (text)

To implement with your database:
1. Replace the mock data with actual database queries
2. Adjust the field names to match your schema
3. Add proper error handling and validation
4. Consider adding pagination for large datasets
5. Add authentication/authorization if needed
*/
