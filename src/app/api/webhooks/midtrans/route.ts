import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { PaymentStatus } from "@prisma/client";
import { handleNotification, mapMidtransStatus } from "@/lib/midtrans";

// POST /api/webhooks/midtrans - Handle Midtrans webhook notifications
export async function POST(request: NextRequest) {
  console.log('📨 [MIDTRANS WEBHOOK] Received webhook request');

  try {
    // Get the raw request body as a string
    const rawBody = await request.text();
    
    console.log('📝 [MIDTRANS WEBHOOK] Raw body received:', {
      bodyLength: rawBody.length,
      hasContent: rawBody.length > 0,
    });

    if (!rawBody) {
      console.error('❌ [MIDTRANS WEBHOOK] Empty request body');
      return NextResponse.json(
        { error: "Empty request body" },
        { status: 400 }
      );
    }

    let body;
    try {
      body = JSON.parse(rawBody);
    } catch (parseError) {
      console.error('❌ [MIDTRANS WEBHOOK] Failed to parse JSON:', {
        error: parseError instanceof Error ? parseError.message : 'Unknown parse error',
        rawBody: rawBody.substring(0, 200) + '...',
      });
      return NextResponse.json(
        { error: "Invalid JSON format" },
        { status: 400 }
      );
    }

    console.log('📋 [MIDTRANS WEBHOOK] Parsed notification data:', {
      order_id: body.order_id,
      transaction_status: body.transaction_status,
      payment_type: body.payment_type,
      fraud_status: body.fraud_status,
      status_code: body.status_code,
      gross_amount: body.gross_amount,
      signature_key: body.signature_key ? 'provided' : 'missing',
    });

    // Validate required fields
    if (!body.order_id || !body.transaction_status || !body.signature_key) {
      console.error('❌ [MIDTRANS WEBHOOK] Missing required fields:', {
        order_id: !!body.order_id,
        transaction_status: !!body.transaction_status,
        signature_key: !!body.signature_key,
      });
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Handle notification using Midtrans library
    const notificationResult = await handleNotification(body);

    if (!notificationResult.success) {
      console.error('❌ [MIDTRANS WEBHOOK] Notification handling failed:', {
        order_id: body.order_id,
        error: notificationResult.error,
      });
      return NextResponse.json(
        { error: notificationResult.error },
        { status: 400 }
      );
    }

    const transactionData = notificationResult.data;

    // Find payment by order ID (which should be our external ID)
    console.log('🔍 [MIDTRANS WEBHOOK] Looking for payment record:', {
      order_id: body.order_id,
    });

    const payment = await db.payment.findFirst({
      where: {
        OR: [
          { externalId: body.order_id },
          { invoiceId: body.order_id },
        ],
      },
      include: { subscription: true },
    });

    if (!payment) {
      console.error('❌ [MIDTRANS WEBHOOK] Payment not found:', {
        order_id: body.order_id,
        searched_fields: ['externalId', 'invoiceId'],
      });
      return NextResponse.json(
        { error: "Payment not found" },
        { status: 404 }
      );
    }

    console.log('✅ [MIDTRANS WEBHOOK] Payment record found:', {
      payment_id: payment.id,
      current_status: payment.status,
      user_id: payment.userId,
      subscription_id: payment.subscriptionId,
    });

    // Map Midtrans status to our PaymentStatus enum
    const newStatus = mapMidtransStatus(
      transactionData.transaction_status,
      transactionData.fraud_status
    );

    console.log('🔄 [MIDTRANS WEBHOOK] Status mapping:', {
      midtrans_status: transactionData.transaction_status,
      fraud_status: transactionData.fraud_status,
      mapped_status: newStatus,
      current_status: payment.status,
    });

    // Prepare metadata with Midtrans callback data
    let newMetadata: any = { midtransCallback: transactionData };
    if (payment.metadata) {
      try {
        const existingMetadata = JSON.parse(JSON.stringify(payment.metadata));
        newMetadata = {
          ...existingMetadata,
          midtransCallback: transactionData,
        };
      } catch (metadataError) {
        console.warn('⚠️ [MIDTRANS WEBHOOK] Failed to parse existing metadata:', {
          payment_id: payment.id,
          error: metadataError instanceof Error ? metadataError.message : 'Unknown error',
        });
      }
    }

    // Handle successful payments
    if (newStatus === 'COMPLETED' && payment.status !== PaymentStatus.COMPLETED) {
      console.log('💰 [MIDTRANS WEBHOOK] Processing successful payment:', {
        payment_id: payment.id,
        order_id: body.order_id,
      });

      // Update payment status to COMPLETED
      await db.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.COMPLETED,
          paymentDate: new Date(),
          metadata: newMetadata,
        },
      });

      console.log('✅ [MIDTRANS WEBHOOK] Payment status updated to COMPLETED:', {
        payment_id: payment.id,
      });

      // If payment is for a subscription, update user's subscription
      if (payment.subscriptionId && payment.subscription) {
        console.log('🔄 [MIDTRANS WEBHOOK] Updating user subscription:', {
          user_id: payment.userId,
          subscription_id: payment.subscriptionId,
          plan: payment.subscription.plan,
          end_date: payment.subscription.endDate,
        });

        await db.user.update({
          where: { id: payment.userId },
          data: {
            currentPlan: payment.subscription.plan,
            subscriptionExpiry: payment.subscription.endDate,
          },
        });

        // Update subscription status to active
        await db.subscription.update({
          where: { id: payment.subscriptionId },
          data: { status: "active" },
        });

        console.log('✅ [MIDTRANS WEBHOOK] Subscription activated:', {
          user_id: payment.userId,
          plan: payment.subscription.plan,
        });
      }
    }
    // Handle failed/expired payments
    else if (['FAILED', 'EXPIRED'].includes(newStatus) && payment.status !== PaymentStatus.EXPIRED) {
      console.log('❌ [MIDTRANS WEBHOOK] Processing failed/expired payment:', {
        payment_id: payment.id,
        order_id: body.order_id,
        new_status: newStatus,
      });

      // Update payment status to EXPIRED (we use EXPIRED for both failed and expired)
      await db.payment.update({
        where: { id: payment.id },
        data: {
          status: PaymentStatus.EXPIRED,
          metadata: newMetadata,
        },
      });

      console.log('✅ [MIDTRANS WEBHOOK] Payment status updated to EXPIRED:', {
        payment_id: payment.id,
      });
    }
    // Handle pending payments
    else if (newStatus === 'PENDING' && payment.status !== PaymentStatus.PENDING) {
      console.log('⏳ [MIDTRANS WEBHOOK] Processing pending payment:', {
        payment_id: payment.id,
        order_id: body.order_id,
      });

      // Update payment metadata but keep status as PENDING
      await db.payment.update({
        where: { id: payment.id },
        data: {
          metadata: newMetadata,
        },
      });

      console.log('✅ [MIDTRANS WEBHOOK] Payment metadata updated (PENDING):', {
        payment_id: payment.id,
      });
    }
    // Status unchanged
    else {
      console.log('ℹ️ [MIDTRANS WEBHOOK] No status change needed:', {
        payment_id: payment.id,
        current_status: payment.status,
        new_status: newStatus,
      });

      // Still update metadata with latest callback data
      await db.payment.update({
        where: { id: payment.id },
        data: {
          metadata: newMetadata,
        },
      });
    }

    console.log('✅ [MIDTRANS WEBHOOK] Webhook processed successfully:', {
      order_id: body.order_id,
      payment_id: payment.id,
      final_status: newStatus,
    });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('❌ [MIDTRANS WEBHOOK] Unexpected error processing webhook:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
    });
    
    return NextResponse.json(
      { error: "Failed to process webhook" },
      { status: 500 }
    );
  }
}
