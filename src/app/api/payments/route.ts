import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getUserPaymentHistory } from "@/lib/subscription";
import { db } from "@/lib/prisma";
import { PaymentStatus } from "@prisma/client";
import { getTransactionStatus } from "@/lib/midtrans";

// GET /api/payments - Get user's payment history
export async function GET(_request: NextRequest) {
  try {
    console.log("📋 [PAYMENTS API] Fetching payment history");
    const session = await auth();

    if (!session?.user?.id) {
      console.log("❌ [PAYMENTS API] Unauthorized - no session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log("🔍 [PAYMENTS API] Getting payments for user:", {
      userId: session.user.id,
    });
    const payments = await getUserPaymentHistory(session.user.id);

    console.log("✅ [PAYMENTS API] Payment history retrieved:", {
      count: payments.length,
    });
    return NextResponse.json({ payments });
  } catch (error) {
    console.error("❌ [PAYMENTS API] Error getting payments:", error);
    return NextResponse.json(
      { error: "Failed to get payments" },
      { status: 500 }
    );
  }
}

// DELETE /api/payments - Delete a payment record
export async function DELETE(request: NextRequest) {
  try {
    console.log("🗑️ [PAYMENTS API] Processing payment deletion request");
    const session = await auth();

    if (!session?.user?.id) {
      console.log("❌ [PAYMENTS API] Unauthorized - no session");
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const paymentId = searchParams.get("id");

    if (!paymentId) {
      console.log("❌ [PAYMENTS API] Missing payment ID");
      return NextResponse.json(
        { error: "Payment ID is required" },
        { status: 400 }
      );
    }

    console.log("🔍 [PAYMENTS API] Looking for payment to delete:", {
      paymentId,
      userId: session.user.id,
    });

    // Find the payment and verify ownership
    const payment = await db.payment.findFirst({
      where: {
        id: paymentId,
        userId: session.user.id, // Ensure user owns this payment
      },
      include: {
        subscription: true,
      },
    });

    if (!payment) {
      console.log("❌ [PAYMENTS API] Payment not found or unauthorized:", {
        paymentId,
        userId: session.user.id,
      });
      return NextResponse.json(
        { error: "Payment not found or unauthorized" },
        { status: 404 }
      );
    }

    console.log("✅ [PAYMENTS API] Payment found:", {
      paymentId: payment.id,
      status: payment.status,
      amount: payment.amount,
      externalId: payment.externalId,
      subscriptionId: payment.subscriptionId,
    });

    // Check if payment is in a state that allows deletion
    if (payment.status === PaymentStatus.COMPLETED) {
      console.log("❌ [PAYMENTS API] Cannot delete completed payment:", {
        paymentId,
        status: payment.status,
      });
      return NextResponse.json(
        { error: "Cannot delete completed payments" },
        { status: 400 }
      );
    }

    // If payment has external ID, check status with Midtrans for consistency
    if (payment.externalId) {
      console.log(
        "🔍 [PAYMENTS API] Checking Midtrans status before deletion:",
        {
          paymentId,
          externalId: payment.externalId,
        }
      );

      try {
        const midtransStatus = await getTransactionStatus(payment.externalId);

        if (midtransStatus.success) {
          const transactionStatus = midtransStatus.data.transaction_status;
          console.log("📊 [PAYMENTS API] Midtrans transaction status:", {
            paymentId,
            externalId: payment.externalId,
            transactionStatus,
          });

          // Prevent deletion if Midtrans shows payment as successful
          if (
            transactionStatus === "settlement" ||
            transactionStatus === "capture"
          ) {
            console.log(
              "❌ [PAYMENTS API] Cannot delete - Midtrans shows payment as successful:",
              {
                paymentId,
                transactionStatus,
              }
            );
            return NextResponse.json(
              {
                error:
                  "Cannot delete payment - transaction is successful in Midtrans",
              },
              { status: 400 }
            );
          }
        } else {
          console.log(
            "⚠️ [PAYMENTS API] Could not verify Midtrans status, proceeding with deletion:",
            {
              paymentId,
              error: midtransStatus.error,
            }
          );
        }
      } catch (error) {
        console.log(
          "⚠️ [PAYMENTS API] Error checking Midtrans status, proceeding with deletion:",
          {
            paymentId,
            error: error instanceof Error ? error.message : "Unknown error",
          }
        );
      }
    }

    // Delete the payment record
    console.log("🗑️ [PAYMENTS API] Deleting payment record:", { paymentId });
    await db.payment.delete({
      where: { id: paymentId },
    });

    console.log("✅ [PAYMENTS API] Payment deleted successfully:", {
      paymentId,
    });
    return NextResponse.json({
      success: true,
      message: "Payment deleted successfully",
      deletedPaymentId: paymentId,
    });
  } catch (error) {
    console.error("❌ [PAYMENTS API] Error deleting payment:", error);
    return NextResponse.json(
      { error: "Failed to delete payment" },
      { status: 500 }
    );
  }
}
