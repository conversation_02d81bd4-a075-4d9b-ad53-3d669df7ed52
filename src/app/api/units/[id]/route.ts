import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the unit ID from the URL params (await the params Promise in Next.js 15)
    const resolvedParams = await params;
    const unitId = resolvedParams.id;

    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return NextResponse.json(
        { error: "Tidak terautentikasi!" },
        { status: 401 }
      );
    }

    // Fetch the unit from the database
    const unit = await db.unit.findFirst({
      where: {
        id: unitId,
        userId: effectiveUserId,
      },
    });

    if (!unit) {
      return NextResponse.json(
        { error: "Unit tidak ditemukan!" },
        { status: 404 }
      );
    }

    // Return the unit data
    return NextResponse.json({ unit });
  } catch (error) {
    console.error("Error fetching unit:", error);
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengambil data unit." },
      { status: 500 }
    );
  }
}
