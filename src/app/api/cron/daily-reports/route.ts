import { NextRequest, NextResponse } from "next/server";
import { db } from "@/lib/prisma";
import { sendDailyReportEmail } from "@/lib/daily-reports";
import { format } from "date-fns";

// This endpoint should be called by a cron service (like Vercel Cron or external cron)
// to send daily reports to users who have enabled them
export async function POST(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional)
    const authHeader = request.headers.get("authorization");
    const expectedToken = process.env.CRON_SECRET_TOKEN;
    
    if (expectedToken && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    console.log("🕐 Starting daily reports cron job...");

    // Get current time in UTC+7 (WIB)
    const now = new Date();
    const currentHour = format(now, "HH:mm");
    
    console.log(`Current time: ${currentHour}`);

    // Get all users who have daily reports enabled and should receive reports at this time
    const usersWithDailyReports = await db.user.findMany({
      where: {
        notificationSettings: {
          dailyReports: true,
          emailEnabled: true,
          dailyReportsTime: currentHour,
        },
      },
      include: {
        notificationSettings: true,
      },
    });

    console.log(`Found ${usersWithDailyReports.length} users to send daily reports to`);

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[],
    };

    // Send daily reports to each user
    for (const user of usersWithDailyReports) {
      if (!user.email) {
        console.log(`Skipping user ${user.id} - no email address`);
        results.failed++;
        results.errors.push(`User ${user.id} has no email address`);
        continue;
      }

      try {
        console.log(`Sending daily report to ${user.email}...`);
        
        const result = await sendDailyReportEmail(
          user.id,
          user.email,
          user.name || user.username || "Pengguna",
          now
        );

        if (result.success) {
          console.log(`✅ Daily report sent successfully to ${user.email}`);
          results.success++;
        } else {
          console.error(`❌ Failed to send daily report to ${user.email}:`, result.error);
          results.failed++;
          results.errors.push(`${user.email}: ${result.error}`);
        }
      } catch (error) {
        console.error(`❌ Error sending daily report to ${user.email}:`, error);
        results.failed++;
        results.errors.push(`${user.email}: ${error instanceof Error ? error.message : "Unknown error"}`);
      }

      // Add a small delay between emails to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`🏁 Daily reports cron job completed. Success: ${results.success}, Failed: ${results.failed}`);

    return NextResponse.json({
      success: true,
      message: `Daily reports sent successfully`,
      results: {
        totalUsers: usersWithDailyReports.length,
        successCount: results.success,
        failedCount: results.failed,
        errors: results.errors,
      },
    });

  } catch (error) {
    console.error("❌ Error in daily reports cron job:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        message: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}

// GET endpoint for testing
export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: "Daily reports cron endpoint is working",
    timestamp: new Date().toISOString(),
    note: "Use POST method to trigger daily reports sending",
  });
}
