import { Customer } from "@/components/pages/dashboard/customers/types";
import { Supplier } from "@/components/pages/dashboard/suppliers/types";
import * as XLSX from "xlsx-js-style";
import {
  createCustomersExcelReport,
  createSuppliersExcelReport,
} from "./exports";

// Convert data to CSV format
export const convertToCSV = (data: any[], headers: string[]): string => {
  const csvHeaders = headers.join(",");
  const csvRows = data.map((row) =>
    headers
      .map((header) => {
        const value = row[header];
        // Handle null/undefined values
        if (value === null || value === undefined) return "";
        // Handle dates
        if (value instanceof Date) return value.toISOString().split("T")[0];
        // Escape commas and quotes in strings
        if (typeof value === "string") {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value.toString();
      })
      .join(",")
  );

  return [csvHeaders, ...csvRows].join("\n");
};

// Download CSV file
export const downloadCSV = (csvContent: string, filename: string): void => {
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// Export customers to CSV
export const exportCustomersToCSV = (customers: Customer[]): void => {
  const headers = [
    "name",
    "contactName",
    "email",
    "phone",
    "address",
    "NIK",
    "NPWP",
    "notes",
    "createdAt",
    "updatedAt",
  ];

  const csvContent = convertToCSV(customers, headers);
  const filename = `customers_${new Date().toISOString().split("T")[0]}.csv`;
  downloadCSV(csvContent, filename);
};

// Export suppliers to CSV
export const exportSuppliersToCSV = (suppliers: Supplier[]): void => {
  const headers = [
    "name",
    "contactName",
    "email",
    "phone",
    "address",
    "notes",
    "createdAt",
    "updatedAt",
  ];

  const csvContent = convertToCSV(suppliers, headers);
  const filename = `suppliers_${new Date().toISOString().split("T")[0]}.csv`;
  downloadCSV(csvContent, filename);
};

// Export customers to professional Excel format
export const exportCustomersToExcel = (customers: Customer[]): void => {
  const customersData = customers.map((customer) => ({
    name: customer.name,
    phone: customer.phone || "-",
    email: customer.email || "-",
    address: customer.address || "-",
    totalTransactions: 0, // This would need to be calculated from actual transaction data
    totalSpent: 0, // This would need to be calculated from actual transaction data
  }));

  const workbook = createCustomersExcelReport(customersData, "Semua Data", {
    companyName: "Kasir Online",
    reportTitle: "Data Pelanggan",
  });

  const filename = `data-pelanggan-${new Date().toISOString().split("T")[0]}.xlsx`;
  XLSX.writeFile(workbook, filename);
};

// Export suppliers to professional Excel format
export const exportSuppliersToExcel = (suppliers: Supplier[]): void => {
  const suppliersData = suppliers.map((supplier) => ({
    name: supplier.name,
    phone: supplier.phone || "-",
    email: supplier.email || "-",
    address: supplier.address || "-",
    totalTransactions: 0, // This would need to be calculated from actual transaction data
    totalPurchases: 0, // This would need to be calculated from actual transaction data
  }));

  const workbook = createSuppliersExcelReport(suppliersData, "Semua Data", {
    companyName: "Kasir Online",
    reportTitle: "Data Supplier",
  });

  const filename = `data-supplier-${new Date().toISOString().split("T")[0]}.xlsx`;
  XLSX.writeFile(workbook, filename);
};

// Parse CSV content
export const parseCSV = (csvContent: string): any[] => {
  const lines = csvContent.split("\n").filter((line) => line.trim());
  if (lines.length === 0) return [];

  const headers = lines[0]
    .split(",")
    .map((header) => header.trim().replace(/"/g, ""));
  const data = [];

  for (let i = 1; i < lines.length; i++) {
    const values = lines[i]
      .split(",")
      .map((value) => value.trim().replace(/"/g, ""));
    const row: any = {};

    headers.forEach((header, index) => {
      const value = values[index] || "";
      row[header] = value === "" ? null : value;
    });

    data.push(row);
  }

  return data;
};

// Handle file upload
export const handleFileUpload = (
  file: File,
  onSuccess: (data: any[]) => void,
  onError: (error: string) => void
): void => {
  if (!file) {
    onError("No file selected");
    return;
  }

  if (!file.name.endsWith(".csv")) {
    onError("Please select a CSV file");
    return;
  }

  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const csvContent = e.target?.result as string;
      const data = parseCSV(csvContent);
      onSuccess(data);
    } catch (error) {
      onError("Error parsing CSV file");
    }
  };

  reader.onerror = () => {
    onError("Error reading file");
  };

  reader.readAsText(file);
};
