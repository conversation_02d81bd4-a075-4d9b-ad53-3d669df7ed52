// Professional Excel export functionality for Sales
// Extracted from consolidated excelTemplate.ts for better maintainability

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
  BRAND_COLORS,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const formatDate = (date: string | Date): string => {
  try {
    return new Date(date).toLocaleDateString("id-ID");
  } catch (error) {
    return String(date);
  }
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- SALES SPECIFIC FUNCTIONS ---

/**
 * Creates a well-formatted sales data sheet
 */
export const createSalesDataSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Penjualan";
  const columns = [
    { key: "id", label: "No. Transaksi", type: "text" as const },
    { key: "saleDate", label: "Tanggal", type: "date" as const },
    { key: "customer.name", label: "Pelanggan", type: "text" as const },
    { key: "totalAmount", label: "Total", type: "currency" as const },
    { key: "items.length", label: "Jumlah Item", type: "number" as const },
    { key: "tags", label: "Tag", type: "text" as const },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = SHEET_HEADER_STYLES.sales;
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (col.key === "items.length" && Array.isArray(item.items))
        value = item.items.length;
      if (col.key === "customer.name") value = item.customer?.name || "Umum";
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "date":
          let dateValue: Date | null = null;
          if (value instanceof Date) {
            dateValue = value;
          } else if (typeof value === "string") {
            try {
              dateValue = new Date(value);
              if (isNaN(dateValue.getTime())) {
                dateValue = null;
              }
            } catch (e) {
              dateValue = null;
            }
          }
          return dateValue || "";
        case "number":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.type === "date") {
        style.numFmt = NUMBER_FORMATS.date;
        if (style.alignment) style.alignment.horizontal = "center";
      } else if (col.type === "number") {
        style.numFmt = NUMBER_FORMATS.integer;
        if (style.alignment) style.alignment.horizontal = "right";
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  // 6. Set Row Heights
  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  setRowHeights(worksheet, rowHeights);

  // 7. Add Interactive Features
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Adds a total row to the sales data sheet
 */
export const addSalesTotalRow = (
  worksheet: XLSX.WorkSheet,
  data: any[]
) => {
  if (!data || data.length === 0) return;

  const headerRowCount = 4;
  const totalRowIndex = data.length + headerRowCount;
  const columns = [
    { key: "id", label: "No. Transaksi", type: "text" },
    { key: "saleDate", label: "Tanggal", type: "date" },
    { key: "customer.name", label: "Pelanggan", type: "text" },
    { key: "totalAmount", label: "Total", type: "currency" },
    { key: "items.length", label: "Jumlah Item", type: "number" },
    { key: "tags", label: "Tag", type: "text" },
  ];

  const totalRow = columns.map((col) => {
    if (col.type === "currency" || col.type === "number") {
      return data.reduce(
        (sum, item) => sum + (getNestedValue(item, col.key) || 0),
        0
      );
    }
    return null;
  });
  totalRow[0] = "TOTAL";

  XLSX.utils.sheet_add_aoa(worksheet, [totalRow], {
    origin: { r: totalRowIndex, c: 0 },
  });

  // Style the total row
  columns.forEach((col, colIndex) => {
    const cellRef = XLSX.utils.encode_cell({ r: totalRowIndex, c: colIndex });
    let style: ExcelCellStyle = JSON.parse(
      JSON.stringify(CELL_STYLES.tableTotalRow)
    );

    if (col.type === "currency") {
      style.numFmt = NUMBER_FORMATS.currency;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (col.type === "number") {
      style.numFmt = NUMBER_FORMATS.integer;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (colIndex === 0) {
      if (style.alignment) style.alignment.horizontal = "left";
    }
    if (style.alignment) style.alignment.vertical = "center";

    applyCellStyle(worksheet, cellRef, style);
  });
};

/**
 * Creates a complete sales Excel workbook
 */
export const createSalesExcelReport = (
  salesData: any[],
  reportPeriod: string,
  options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create sales sheet
  const salesSheet = createSalesDataSheet(salesData, reportPeriod);
  addSalesTotalRow(salesSheet, salesData);
  XLSX.utils.book_append_sheet(workbook, salesSheet, "💰 Penjualan");

  return workbook;
};
