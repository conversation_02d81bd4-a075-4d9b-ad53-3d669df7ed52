// Professional Excel export functionality for Services
// Extracted from consolidated excelTemplate.ts for better maintainability

import * as XLSX from "xlsx-js-style";
import { CELL_STYLES, NUMBER_FORMATS, ExcelCellStyle } from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- SERVICES SPECIFIC FUNCTIONS ---

/**
 * Creates a well-formatted services data sheet
 */
export const createServicesDataSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Data Servis";
  const columns = [
    { key: "serviceNumber", label: "No. Servis", type: "text" as const },
    { key: "receivedDate", label: "Tanggal Masuk", type: "date" as const },
    { key: "customerName", label: "Nama Pelanggan", type: "text" as const },
    { key: "customerPhone", label: "Telepon", type: "text" as const },
    { key: "deviceType", label: "Jenis Perangkat", type: "text" as const },
    { key: "deviceBrand", label: "Merek", type: "text" as const },
    { key: "deviceModel", label: "Model", type: "text" as const },
    { key: "problemDescription", label: "Keluhan", type: "text" as const },
    { key: "status", label: "Status", type: "text" as const },
    {
      key: "estimatedCost",
      label: "Estimasi Biaya",
      type: "currency" as const,
    },
    { key: "finalCost", label: "Biaya Final", type: "currency" as const },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = CELL_STYLES.tableHeader; // Use default header style for services
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "date":
          let dateValue: Date | null = null;
          if (value instanceof Date) {
            dateValue = value;
          } else if (typeof value === "string") {
            try {
              dateValue = new Date(value);
              if (isNaN(dateValue.getTime())) {
                dateValue = null;
              }
            } catch (e) {
              dateValue = null;
            }
          }
          return dateValue || "";
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.type === "date") {
        style.numFmt = NUMBER_FORMATS.date;
        if (style.alignment) style.alignment.horizontal = "center";
      } else if (col.key === "problemDescription") {
        if (style.alignment) {
          style.alignment.vertical = "top";
          style.alignment.wrapText = true;
        }
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (col.key === "problemDescription") width = 50;
    else if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  // 6. Set Row Heights
  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  if (columns.some((c) => c.key === "problemDescription")) {
    rows.forEach((_, i) => {
      rowHeights[headerRowCount + i + 1] = 40;
    });
  }
  setRowHeights(worksheet, rowHeights);

  // 7. Add Interactive Features
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Creates a spare parts data sheet for services
 */
export const createSparePartsDataSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Data Sparepart";
  const columns = [
    { key: "serviceNumber", label: "No. Servis", type: "text" as const },
    { key: "entryDate", label: "Tanggal Masuk", type: "date" as const },
    { key: "sparePartName", label: "Nama Sparepart", type: "text" as const },
    { key: "barcode", label: "Barcode", type: "text" as const },
    { key: "quantity", label: "Jumlah", type: "number" as const },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = CELL_STYLES.tableHeader;
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "date":
          let dateValue: Date | null = null;
          if (value instanceof Date) {
            dateValue = value;
          } else if (typeof value === "string") {
            try {
              dateValue = new Date(value);
              if (isNaN(dateValue.getTime())) {
                dateValue = null;
              }
            } catch (e) {
              dateValue = null;
            }
          }
          return dateValue || "";
        case "number":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "date") {
        style.numFmt = NUMBER_FORMATS.date;
        if (style.alignment) style.alignment.horizontal = "center";
      } else if (col.type === "number") {
        style.numFmt = NUMBER_FORMATS.integer;
        if (style.alignment) style.alignment.horizontal = "right";
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths and other formatting
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  setRowHeights(worksheet, rowHeights);

  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Creates a complete services Excel workbook
 */
export const createServicesExcelReport = (
  servicesData: any[],
  sparePartsData: any[] = [],
  reportPeriod: string,
  _options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create services sheet
  const servicesSheet = createServicesDataSheet(servicesData, reportPeriod);
  XLSX.utils.book_append_sheet(workbook, servicesSheet, "🔧 Servis");

  // Create spare parts sheet if data exists
  if (sparePartsData && sparePartsData.length > 0) {
    const sparePartsSheet = createSparePartsDataSheet(
      sparePartsData,
      reportPeriod
    );
    XLSX.utils.book_append_sheet(workbook, sparePartsSheet, "⚙️ Sparepart");
  }

  return workbook;
};
