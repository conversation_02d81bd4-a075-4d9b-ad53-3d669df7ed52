// Centralized exports for all Excel export functionality
// This file provides easy access to all individual export modules

// Purchases Export
export {
  createPurchasesDataSheet,
  addPurchasesTotalRow,
  createPurchasesExcelReport,
} from "./purchasesExport";

// Sales Export
export {
  createSalesDataSheet,
  addSalesTotalRow,
  createSalesExcelReport,
} from "./salesExport";

// Products Export
export {
  createProductsDataSheet,
  addProductsTotalRow,
  createProductsExcelReport,
} from "./productsExport";

// Services Export
export {
  createServicesDataSheet,
  createSparePartsDataSheet,
  createServicesExcelReport,
} from "./servicesExport";

// Customers Export
export {
  createCustomersDataSheet,
  addCustomersTotalRow,
  createCustomersExcelReport,
} from "./customersExport";

// Suppliers Export
export {
  createSuppliersDataSheet,
  addSuppliersTotalRow,
  createSuppliersExcelReport,
} from "./suppliersExport";

// Reports Export (Summary and Income Statement)
export {
  createSummarySheet,
  applyIncomeStatementFormatting,
  createIncomeStatementSheet,
  createReportsExcelWorkbook,
  type ReportData,
  type ExcelTemplateOptions,
} from "./reportsExport";

// Re-export common types and utilities from excelStyles
export type { ExcelCellStyle } from "../excelStyles";
export {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  BRAND_COLORS,
  FONTS,
} from "../excelStyles";
