// Professional Excel export functionality for Products
// Extracted from consolidated excelTemplate.ts for better maintainability

import * as XLSX from "xlsx-js-style";
import {
  CELL_STYLES,
  NUMBER_FORMATS,
  SHEET_HEADER_STYLES,
  ExcelCellStyle,
} from "../excelStyles";

// --- UTILITY FUNCTIONS ---

const applyCellStyle = (ws: XLSX.WorkSheet, ref: string, style: any) => {
  if (!ws[ref]) ws[ref] = { t: "s", v: "" };
  ws[ref].s = style;
};

const mergeCells = (ws: XLSX.WorkSheet, range: string) => {
  if (!ws["!merges"]) ws["!merges"] = [];
  ws["!merges"].push(XLSX.utils.decode_range(range));
};

const setColumnWidths = (ws: XLSX.WorkSheet, widths: { wch: number }[]) => {
  ws["!cols"] = widths;
};

const setRowHeights = (
  ws: XLSX.WorkSheet,
  heights: { [row: number]: number }
) => {
  if (!ws["!rows"]) ws["!rows"] = [];
  Object.entries(heights).forEach(([row, hpt]) => {
    const rowIndex = parseInt(row) - 1;
    if (!ws["!rows"]![rowIndex]) ws["!rows"]![rowIndex] = {};
    ws["!rows"]![rowIndex].hpt = hpt;
  });
};

const addAutoFilter = (ws: XLSX.WorkSheet, range: string) => {
  ws["!autofilter"] = { ref: range };
};

const freezePanes = (ws: XLSX.WorkSheet, cell: string) => {
  ws["!view"] = {
    state: "frozen",
    xSplit: 0,
    ySplit: XLSX.utils.decode_cell(cell).r,
    topLeftCell: cell,
  };
};

const getNestedValue = (obj: any, path: string): any => {
  return path.split(".").reduce((acc, part) => acc && acc[part], obj);
};

// --- PRODUCTS SPECIFIC FUNCTIONS ---

/**
 * Creates a well-formatted products data sheet
 */
export const createProductsDataSheet = (
  data: any[],
  reportPeriod: string
): XLSX.WorkSheet => {
  const sheetTitle = "Laporan Produk";
  const columns = [
    { key: "name", label: "Nama Produk", type: "text" as const },
    { key: "description", label: "Deskripsi", type: "text" as const },
    { key: "sku", label: "Kode Produk", type: "text" as const },
    { key: "category.name", label: "Kategori", type: "text" as const },
    { key: "stock", label: "Stok", type: "number" as const },
    { key: "cost", label: "Harga Beli", type: "currency" as const },
    { key: "price", label: "Harga Jual", type: "currency" as const },
  ];

  const headerRowCount = 4;
  const headers = columns.map((col) => col.label);
  const worksheet = XLSX.utils.aoa_to_sheet([[]]);

  // 1. Add Sheet Title & Subtitle
  XLSX.utils.sheet_add_aoa(
    worksheet,
    [[sheetTitle], [`Periode: ${reportPeriod}`]],
    { origin: "A1" }
  );
  mergeCells(worksheet, `A1:${XLSX.utils.encode_col(columns.length - 1)}1`);
  applyCellStyle(worksheet, "A1", CELL_STYLES.sheetTitle);
  mergeCells(worksheet, `A2:${XLSX.utils.encode_col(columns.length - 1)}2`);
  applyCellStyle(worksheet, "A2", CELL_STYLES.sheetSubtitle);

  // 2. Add Table Headers
  XLSX.utils.sheet_add_aoa(worksheet, [headers], {
    origin: { r: headerRowCount - 1, c: 0 },
  });
  const headerStyle = SHEET_HEADER_STYLES.products;
  columns.forEach((_, index) => {
    applyCellStyle(
      worksheet,
      XLSX.utils.encode_cell({ r: headerRowCount - 1, c: index }),
      headerStyle
    );
  });

  // 3. Process and Add Data Rows
  const rows = data.map((item) =>
    columns.map((col) => {
      let value = getNestedValue(item, col.key);
      if (col.key === "category.name")
        value = item.category?.name || "Tanpa Kategori";
      if (value === null || value === undefined) return "";

      switch (col.type) {
        case "currency":
          return typeof value === "number" ? value : 0;
        case "number":
          return typeof value === "number" ? value : 0;
        default:
          return String(value);
      }
    })
  );

  XLSX.utils.sheet_add_aoa(worksheet, rows, {
    origin: { r: headerRowCount, c: 0 },
  });

  // 4. Style Data Rows
  rows.forEach((_, rowIndex) => {
    const actualRow = rowIndex + headerRowCount;
    const isEven = rowIndex % 2 === 0;
    columns.forEach((col, colIndex) => {
      const cellRef = XLSX.utils.encode_cell({ r: actualRow, c: colIndex });
      let style: ExcelCellStyle = JSON.parse(
        JSON.stringify(
          isEven ? CELL_STYLES.tableDataEven : CELL_STYLES.tableDataOdd
        )
      );

      if (col.type === "currency") {
        style.numFmt = NUMBER_FORMATS.currency;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.type === "number") {
        style.numFmt = NUMBER_FORMATS.integer;
        if (style.alignment) style.alignment.horizontal = "right";
      } else if (col.key === "description") {
        if (style.alignment) {
          style.alignment.vertical = "top";
          style.alignment.wrapText = true;
        }
      }
      applyCellStyle(worksheet, cellRef, style);
    });
  });

  // 5. Calculate Column Widths
  const colWidths = columns.map((col, index) => {
    const headerLength = col.label.length;
    const maxDataLength =
      data.length > 0
        ? Math.max(...rows.map((row) => String(row[index] || "").length))
        : 0;
    let width = Math.max(headerLength + 5, maxDataLength + 3, 15);
    if (col.key === "description") width = 50;
    else if (width > 60) width = 60;
    return { wch: width };
  });
  setColumnWidths(worksheet, colWidths);

  // 6. Set Row Heights
  const rowHeights: { [key: number]: number } = {
    1: 22,
    2: 18,
    [headerRowCount]: 30,
  };
  if (columns.some((c) => c.key === "description")) {
    rows.forEach((_, i) => {
      rowHeights[headerRowCount + i + 1] = 40;
    });
  }
  setRowHeights(worksheet, rowHeights);

  // 7. Add Interactive Features
  freezePanes(worksheet, `A${headerRowCount + 1}`);
  if (data.length > 0) {
    addAutoFilter(
      worksheet,
      `A${headerRowCount}:${XLSX.utils.encode_col(columns.length - 1)}${data.length + headerRowCount}`
    );
  }

  return worksheet;
};

/**
 * Adds a total row to the products data sheet
 */
export const addProductsTotalRow = (worksheet: XLSX.WorkSheet, data: any[]) => {
  if (!data || data.length === 0) return;

  const headerRowCount = 4;
  const totalRowIndex = data.length + headerRowCount;
  const columns = [
    { key: "name", label: "Nama Produk", type: "text" },
    { key: "description", label: "Deskripsi", type: "text" },
    { key: "sku", label: "Kode Produk", type: "text" },
    { key: "category.name", label: "Kategori", type: "text" },
    { key: "stock", label: "Stok", type: "number" },
    { key: "cost", label: "Harga Beli", type: "currency" },
    { key: "price", label: "Harga Jual", type: "currency" },
  ];

  const totalRow = columns.map((col) => {
    if (col.type === "currency" || col.type === "number") {
      return data.reduce(
        (sum, item) => sum + (getNestedValue(item, col.key) || 0),
        0
      );
    }
    return null;
  });
  totalRow[0] = "TOTAL";

  XLSX.utils.sheet_add_aoa(worksheet, [totalRow], {
    origin: { r: totalRowIndex, c: 0 },
  });

  // Style the total row
  columns.forEach((col, colIndex) => {
    const cellRef = XLSX.utils.encode_cell({ r: totalRowIndex, c: colIndex });
    let style: ExcelCellStyle = JSON.parse(
      JSON.stringify(CELL_STYLES.tableTotalRow)
    );

    if (col.type === "currency") {
      style.numFmt = NUMBER_FORMATS.currency;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (col.type === "number") {
      style.numFmt = NUMBER_FORMATS.integer;
      if (style.alignment) style.alignment.horizontal = "right";
    } else if (colIndex === 0) {
      if (style.alignment) style.alignment.horizontal = "left";
    }
    if (style.alignment) style.alignment.vertical = "center";

    applyCellStyle(worksheet, cellRef, style);
  });
};

/**
 * Creates a complete products Excel workbook
 */
export const createProductsExcelReport = (
  productsData: any[],
  reportPeriod: string,
  _options: { companyName?: string; reportTitle?: string } = {}
): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create products sheet
  const productsSheet = createProductsDataSheet(productsData, reportPeriod);
  addProductsTotalRow(productsSheet, productsData);
  XLSX.utils.book_append_sheet(workbook, productsSheet, "📦 Produk");

  return workbook;
};
