// Service import template generation utilities
// Creates standardized Excel import templates for service management

import * as XLSX from "xlsx-js-style";
import {
  applyCellStyle,
  mergeCells,
  setColumnWidths,
  setRowHeights,
  applyHeaderStyling,
  applyColumnHeaderStyling,
  setStandardRowHeights,
  createInstructionsSheet,
  getCommonInstructions,
  getDateFormatInstructions,
  CELL_STYLES,
  BRAND_COLORS,
} from "./shared";

/**
 * Creates a professional import template for services
 */
export const createServiceImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    ["TEMPLATE IMPORT SERVIS", "", "", "", "", "", "", "", "", "", "", ""],
    ["Kasir Online - Sistem Manajemen Servis", "", "", "", "", "", "", "", "", "", "", ""],
    ["", "", "", "", "", "", "", "", "", "", "", ""],
    // Column headers
    [
      "Tanggal Diterima*",
      "Nama Pelanggan*",
      "Telepon Pelanggan",
      "Email Pelanggan",
      "Jenis Perangkat*",
      "Merek Perangkat",
      "Model Perangkat",
      "Keluhan*",
      "Estimasi Biaya",
      "Periode Garansi",
      "Status",
      "Catatan"
    ],
    // Sample data rows
    [
      "2024-01-15",
      "John Doe",
      "081234567890",
      "<EMAIL>",
      "Laptop",
      "ASUS",
      "VivoBook X441",
      "Laptop tidak bisa menyala",
      "250000",
      "30",
      "Diterima",
      "Perlu pengecekan power supply"
    ],
    [
      "2024-01-15",
      "Jane Smith",
      "081234567891",
      "<EMAIL>",
      "Smartphone",
      "Samsung",
      "Galaxy A52",
      "Layar pecah",
      "150000",
      "14",
      "Dalam Proses",
      "Ganti layar LCD"
    ]
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(templateSheet, "TEMPLATE IMPORT SERVIS", "Kasir Online - Sistem Manajemen Servis", 12);
  
  // Style column headers
  applyColumnHeaderStyling(templateSheet, 12);

  // Set column widths
  setColumnWidths(templateSheet, [
    { wch: 15 }, // Tanggal Diterima
    { wch: 20 }, // Nama Pelanggan
    { wch: 15 }, // Telepon Pelanggan
    { wch: 25 }, // Email Pelanggan
    { wch: 15 }, // Jenis Perangkat
    { wch: 15 }, // Merek Perangkat
    { wch: 20 }, // Model Perangkat
    { wch: 30 }, // Keluhan
    { wch: 12 }, // Estimasi Biaya
    { wch: 12 }, // Periode Garansi
    { wch: 15 }, // Status
    { wch: 25 }  // Catatan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Servis");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Tanggal Diterima: Format YYYY-MM-DD (contoh: 2024-01-15)",
    "   • Nama Pelanggan: Nama pemilik perangkat",
    "   • Jenis Perangkat: Jenis perangkat yang akan diperbaiki",
    "   • Keluhan: Deskripsi masalah atau keluhan",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Telepon Pelanggan: Nomor telepon pelanggan",
    "   • Email Pelanggan: Email pelanggan",
    "   • Merek Perangkat: Merek perangkat",
    "   • Model Perangkat: Model/tipe perangkat",
    "   • Estimasi Biaya: Perkiraan biaya perbaikan (dalam Rupiah)",
    "   • Periode Garansi: Periode garansi dalam hari",
    "   • Status: Status servis (Diterima/Dalam Proses/Selesai/Diambil)",
    "   • Catatan: Catatan tambahan",
    "",
    "3. FORMAT DATA:",
    ...getDateFormatInstructions(),
    "   • Garansi: Masukkan angka hari (contoh: 30)",
    "   • Status: Diterima, Dalam Proses, Selesai, atau Diambil",
    "",
    "4. TIPS IMPORT:",
    "   • Pelanggan akan dibuat otomatis jika belum ada",
    "   • Hapus baris contoh sebelum import",
    "   • Pastikan tidak ada baris kosong di tengah data",
    "   • Simpan file dalam format .xlsx atau .xls",
    "",
    "5. TROUBLESHOOTING:",
    "   • Jika import gagal, periksa format tanggal",
    "   • Pastikan status sesuai dengan pilihan yang tersedia",
    "   • Periksa format angka (tanpa titik/koma)"
  ];

  createInstructionsSheet(workbook, "PETUNJUK PENGGUNAAN TEMPLATE IMPORT SERVIS", instructions);

  return workbook;
};
