// Sales import template generation utilities
// Creates standardized Excel import templates for sales transactions

import * as XLSX from "xlsx-js-style";
import {
  applyCellStyle,
  mergeCells,
  setColumnWidths,
  setRowHeights,
  applyHeaderStyling,
  applyColumnHeaderStyling,
  setStandardRowHeights,
  createInstructionsSheet,
  getCommonInstructions,
  getDateFormatInstructions,
  CELL_STYLES,
  BRAND_COLORS,
} from "./shared";

/**
 * Creates a professional import template for sales
 */
export const createSalesImportTemplate = (): XLSX.WorkBook => {
  const workbook = XLSX.utils.book_new();

  // Create main template sheet
  const templateData = [
    // Header rows
    ["TEMPLATE IMPORT PENJUALAN", "", "", "", "", "", "", "", "", ""],
    ["Kasir Online - Sistem Manajemen Penjualan", "", "", "", "", "", "", "", "", ""],
    ["", "", "", "", "", "", "", "", "", ""],
    // Column headers
    [
      "Tanggal Penjualan*",
      "Nama Pelanggan",
      "Telepon Pelanggan",
      "Email Pelanggan",
      "Nama Produk*",
      "Quantity*",
      "Harga Satuan*",
      "Diskon",
      "PPN (%)",
      "Catatan"
    ],
    // Sample data rows
    [
      "2024-01-15",
      "<PERSON> Doe",
      "081234567890",
      "<EMAIL>",
      "Produk A",
      "2",
      "50000",
      "5000",
      "11",
      "Penjualan contoh"
    ],
    [
      "2024-01-15",
      "Jane Smith",
      "081234567891",
      "<EMAIL>",
      "Produk B",
      "1",
      "75000",
      "0",
      "11",
      ""
    ]
  ];

  const templateSheet = XLSX.utils.aoa_to_sheet(templateData);

  // Apply header styling
  applyHeaderStyling(templateSheet, "TEMPLATE IMPORT PENJUALAN", "Kasir Online - Sistem Manajemen Penjualan", 10);
  
  // Style column headers
  applyColumnHeaderStyling(templateSheet, 10);

  // Set column widths
  setColumnWidths(templateSheet, [
    { wch: 15 }, // Tanggal Penjualan
    { wch: 20 }, // Nama Pelanggan
    { wch: 15 }, // Telepon Pelanggan
    { wch: 25 }, // Email Pelanggan
    { wch: 25 }, // Nama Produk
    { wch: 10 }, // Quantity
    { wch: 12 }, // Harga Satuan
    { wch: 10 }, // Diskon
    { wch: 10 }, // PPN
    { wch: 20 }  // Catatan
  ]);

  // Set row heights
  setStandardRowHeights(templateSheet);

  XLSX.utils.book_append_sheet(workbook, templateSheet, "Template Penjualan");

  // Create instructions sheet
  const instructions = [
    "1. KOLOM WAJIB (Harus diisi):",
    "   • Tanggal Penjualan: Format YYYY-MM-DD (contoh: 2024-01-15)",
    "   • Nama Produk: Nama produk yang dijual (harus sudah ada di sistem)",
    "   • Quantity: Jumlah produk yang dijual",
    "   • Harga Satuan: Harga per unit (dalam Rupiah, tanpa titik/koma)",
    "",
    "2. KOLOM OPSIONAL:",
    "   • Nama Pelanggan: Nama pembeli",
    "   • Telepon Pelanggan: Nomor telepon pembeli",
    "   • Email Pelanggan: Email pembeli",
    "   • Diskon: Jumlah diskon dalam Rupiah",
    "   • PPN (%): Persentase PPN (contoh: 11 untuk 11%)",
    "   • Catatan: Catatan tambahan untuk penjualan",
    "",
    "3. FORMAT DATA:",
    ...getDateFormatInstructions(),
    "   • PPN: Masukkan persentase (contoh: 11)",
    "",
    "4. TIPS IMPORT:",
    "   • Produk harus sudah ada di sistem sebelum import",
    "   • Hapus baris contoh sebelum import",
    "   • Pastikan tidak ada baris kosong di tengah data",
    "   • Simpan file dalam format .xlsx atau .xls",
    "",
    "5. TROUBLESHOOTING:",
    "   • Jika import gagal, periksa format tanggal",
    "   • Pastikan produk sudah ada di sistem",
    "   • Periksa format angka (tanpa titik/koma)"
  ];

  createInstructionsSheet(workbook, "PETUNJUK PENGGUNAAN TEMPLATE IMPORT PENJUALAN", instructions);

  return workbook;
};
