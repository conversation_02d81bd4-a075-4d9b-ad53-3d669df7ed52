"use client";

import { useSession } from "next-auth/react";
import { useCallback } from "react";

/**
 * Custom hook to handle session refresh
 * This ensures that session data is updated immediately when switching between accounts
 */
export const useSessionRefresh = () => {
  const { data: session, status, update } = useSession();

  const refreshSession = useCallback(async () => {
    try {
      console.log("🔄 Refreshing session...");
      await update();
      console.log("✅ Session refreshed successfully");
    } catch (error) {
      console.error("❌ Failed to refresh session:", error);
    }
  }, [update]);

  const forceRefresh = useCallback(async () => {
    try {
      console.log("🔄 Force refreshing session...");
      // Force a complete session refresh by updating with current data
      await update();
      // Additional refresh to ensure data is current
      setTimeout(async () => {
        await update();
      }, 100);
      console.log("✅ Session force refreshed successfully");
    } catch (error) {
      console.error("❌ Failed to force refresh session:", error);
    }
  }, [update]);

  return {
    session,
    status,
    refreshSession,
    forceRefresh,
    isLoading: status === "loading",
    isAuthenticated: status === "authenticated",
  };
};
