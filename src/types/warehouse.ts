// Warehouse Management Types

export interface Warehouse {
  id: string;
  name: string;
  description?: string | null;
  address?: string | null;
  phone?: string | null;
  email?: string | null;
  contactName?: string | null;
  isActive: boolean;
  isDefault: boolean;
  createdAt: Date | string;
  updatedAt: Date | string;
  userId: string;
}

export interface WarehouseStock {
  id: string;
  quantity: number;
  minLevel?: number | null;
  maxLevel?: number | null;
  createdAt: Date | string;
  updatedAt: Date | string;
  productId: string;
  warehouseId: string;
  product?: {
    id: string;
    name: string;
    sku?: string | null;
    image?: string | null;
    price: number;
    unit: string;
  };
  warehouse?: {
    id: string;
    name: string;
  };
}

export interface StockMovement {
  id: string;
  type: StockMovementType;
  quantity: number;
  previousStock: number;
  newStock: number;
  reference?: string | null;
  notes?: string | null;
  createdAt: Date | string;
  productId: string;
  warehouseId: string;
  userId: string;
  product?: {
    id: string;
    name: string;
    sku?: string | null;
  };
  warehouse?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name?: string | null;
    username?: string | null;
  };
}

export enum StockMovementType {
  PURCHASE = "PURCHASE",
  SALE = "SALE", 
  TRANSFER_IN = "TRANSFER_IN",
  TRANSFER_OUT = "TRANSFER_OUT",
  ADJUSTMENT = "ADJUSTMENT",
  RETURN = "RETURN",
  DAMAGE = "DAMAGE"
}

export interface WarehouseFormData {
  name: string;
  description?: string;
  address?: string;
  phone?: string;
  email?: string;
  contactName?: string;
  isActive: boolean;
  isDefault: boolean;
}

export interface StockTransferData {
  productId: string;
  fromWarehouseId: string;
  toWarehouseId: string;
  quantity: number;
  notes?: string;
}

export interface StockAdjustmentData {
  productId: string;
  warehouseId: string;
  newQuantity: number;
  notes?: string;
}

export interface WarehouseSummary {
  totalWarehouses: number;
  activeWarehouses: number;
  totalProducts: number;
  totalStock: number;
  lowStockItems: number;
}

export interface WarehouseStockSummary {
  warehouseId: string;
  warehouseName: string;
  totalProducts: number;
  totalStock: number;
  lowStockItems: number;
  outOfStockItems: number;
}

// Column visibility for warehouse tables
export interface WarehouseColumnVisibility {
  name: boolean;
  description: boolean;
  address: boolean;
  phone: boolean;
  email: boolean;
  contactName: boolean;
  isActive: boolean;
  isDefault: boolean;
  createdAt: boolean;
  actions: boolean;
}

export interface WarehouseStockColumnVisibility {
  productName: boolean;
  sku: boolean;
  quantity: boolean;
  minLevel: boolean;
  maxLevel: boolean;
  status: boolean;
  lastUpdated: boolean;
  actions: boolean;
}

export interface StockMovementColumnVisibility {
  type: boolean;
  productName: boolean;
  quantity: boolean;
  previousStock: boolean;
  newStock: boolean;
  reference: boolean;
  notes: boolean;
  createdAt: boolean;
  user: boolean;
}

// Filter and search interfaces
export interface WarehouseFilters {
  isActive?: boolean;
  isDefault?: boolean;
  search?: string;
}

export interface WarehouseStockFilters {
  warehouseId?: string;
  lowStock?: boolean;
  outOfStock?: boolean;
  search?: string;
}

export interface StockMovementFilters {
  warehouseId?: string;
  productId?: string;
  type?: StockMovementType;
  dateFrom?: Date;
  dateTo?: Date;
  search?: string;
}
