import { Resend } from "resend";
import * as XLSX from "xlsx-js-style";
import { db } from "@/lib/prisma";
import { startOfDay, endOfDay, format } from "date-fns";
import { id } from "date-fns/locale";
import { CELL_STYLES, NUMBER_FORMATS } from "@/utils/excelStyles";

const resend = new Resend(process.env.RESEND_API_KEY || "re_12345678-1234-1234-1234-1234567890ab");

interface DailyReportData {
  date: Date;
  income: {
    sales: number;
    services: number;
    total: number;
  };
  expenses: {
    purchases: number;
    total: number;
  };
  transactions: {
    salesCount: number;
    purchasesCount: number;
    servicesCount: number;
    total: number;
  };
  loginHistory: Array<{
    userId: string;
    userName: string;
    loginTime: Date;
    userAgent?: string;
  }>;
}

// Generate daily report data for a specific user
export async function generateDailyReportData(
  userId: string,
  date: Date = new Date()
): Promise<DailyReportData> {
  const startDate = startOfDay(date);
  const endDate = endOfDay(date);

  // Get sales data
  const sales = await db.sale.findMany({
    where: {
      userId,
      saleDate: {
        gte: startDate,
        lte: endDate,
      },
      isDraft: false,
    },
  });

  // Get purchase data
  const purchases = await db.purchase.findMany({
    where: {
      userId,
      purchaseDate: {
        gte: startDate,
        lte: endDate,
      },
    },
  });

  // Get service data
  const services = await db.service.findMany({
    where: {
      userId,
      receivedDate: {
        gte: startDate,
        lte: endDate,
      },
      isDraft: false,
    },
  });

  // Get login history (simplified - you might need to implement actual login tracking)
  const loginHistory = await db.user.findMany({
    where: {
      id: userId,
      updatedAt: {
        gte: startDate,
        lte: endDate,
      },
    },
    select: {
      id: true,
      name: true,
      username: true,
      updatedAt: true,
    },
  });

  // Calculate totals
  const salesTotal = sales.reduce((sum, sale) => sum + sale.totalAmount.toNumber(), 0);
  const servicesTotal = services.reduce((sum, service) => {
    return sum + (service.finalCost ? service.finalCost.toNumber() : service.estimatedCost?.toNumber() || 0);
  }, 0);
  const purchasesTotal = purchases.reduce((sum, purchase) => sum + purchase.totalAmount.toNumber(), 0);

  return {
    date,
    income: {
      sales: salesTotal,
      services: servicesTotal,
      total: salesTotal + servicesTotal,
    },
    expenses: {
      purchases: purchasesTotal,
      total: purchasesTotal,
    },
    transactions: {
      salesCount: sales.length,
      purchasesCount: purchases.length,
      servicesCount: services.length,
      total: sales.length + purchases.length + services.length,
    },
    loginHistory: loginHistory.map(user => ({
      userId: user.id,
      userName: user.name || user.username || 'Unknown',
      loginTime: user.updatedAt,
    })),
  };
}

// Create Excel report from daily data
export function createDailyReportExcel(data: DailyReportData): Buffer {
  const workbook = XLSX.utils.book_new();
  const dateStr = format(data.date, "dd MMMM yyyy", { locale: id });

  // Summary Sheet
  const summaryData = [
    [`Laporan Harian - ${dateStr}`],
    [`Diekspor pada: ${new Date().toLocaleString("id-ID")}`],
    [],
    ["RINGKASAN PENDAPATAN"],
    ["Penjualan", data.income.sales],
    ["Jasa/Servis", data.income.services],
    ["Total Pendapatan", data.income.total],
    [],
    ["RINGKASAN PENGELUARAN"],
    ["Pembelian", data.expenses.purchases],
    ["Total Pengeluaran", data.expenses.total],
    [],
    ["LABA BERSIH"],
    ["Laba Hari Ini", data.income.total - data.expenses.total],
    [],
    ["RINGKASAN TRANSAKSI"],
    ["Jumlah Penjualan", data.transactions.salesCount],
    ["Jumlah Pembelian", data.transactions.purchasesCount],
    ["Jumlah Servis", data.transactions.servicesCount],
    ["Total Transaksi", data.transactions.total],
  ];

  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  
  // Apply styling
  summarySheet["A1"].s = CELL_STYLES.reportTitle;
  summarySheet["A2"].s = CELL_STYLES.info;
  
  // Set column widths
  summarySheet["!cols"] = [{ wch: 25 }, { wch: 15 }];
  
  XLSX.utils.book_append_sheet(workbook, summarySheet, "📊 Ringkasan");

  // Login History Sheet
  if (data.loginHistory.length > 0) {
    const loginData = [
      [`Riwayat Login - ${dateStr}`],
      [],
      ["Nama Pengguna", "Waktu Login"],
      ...data.loginHistory.map(login => [
        login.userName,
        login.loginTime.toLocaleString("id-ID"),
      ]),
    ];

    const loginSheet = XLSX.utils.aoa_to_sheet(loginData);
    loginSheet["A1"].s = CELL_STYLES.sheetTitle;
    loginSheet["A3"].s = CELL_STYLES.tableHeader;
    loginSheet["B3"].s = CELL_STYLES.tableHeader;
    
    loginSheet["!cols"] = [{ wch: 20 }, { wch: 20 }];
    
    XLSX.utils.book_append_sheet(workbook, loginSheet, "👤 Login History");
  }

  // Convert to buffer
  const excelBuffer = XLSX.write(workbook, { type: "buffer", bookType: "xlsx" });
  return excelBuffer as Buffer;
}

// Send daily report email
export async function sendDailyReportEmail(
  userId: string,
  userEmail: string,
  userName: string,
  date: Date = new Date()
): Promise<{ success: boolean; error?: string }> {
  try {
    // Generate report data
    const reportData = await generateDailyReportData(userId, date);
    
    // Create Excel attachment
    const excelBuffer = createDailyReportExcel(reportData);
    
    const dateStr = format(date, "dd MMMM yyyy", { locale: id });
    const fileName = `laporan-harian-${format(date, "yyyy-MM-dd")}.xlsx`;

    // Prepare email content
    const emailHtml = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4F46E5;">Laporan Harian - ${dateStr}</h2>
        
        <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1e293b;">Ringkasan Hari Ini</h3>
          
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 15px 0;">
            <div>
              <h4 style="color: #059669; margin: 5px 0;">💰 Pendapatan</h4>
              <p style="margin: 5px 0;">Penjualan: Rp ${reportData.income.sales.toLocaleString("id-ID")}</p>
              <p style="margin: 5px 0;">Jasa/Servis: Rp ${reportData.income.services.toLocaleString("id-ID")}</p>
              <p style="margin: 5px 0; font-weight: bold;">Total: Rp ${reportData.income.total.toLocaleString("id-ID")}</p>
            </div>
            
            <div>
              <h4 style="color: #DC2626; margin: 5px 0;">💸 Pengeluaran</h4>
              <p style="margin: 5px 0;">Pembelian: Rp ${reportData.expenses.purchases.toLocaleString("id-ID")}</p>
              <p style="margin: 5px 0; font-weight: bold;">Total: Rp ${reportData.expenses.total.toLocaleString("id-ID")}</p>
            </div>
          </div>
          
          <div style="border-top: 2px solid #e2e8f0; padding-top: 15px; margin-top: 15px;">
            <h4 style="color: ${reportData.income.total - reportData.expenses.total >= 0 ? '#059669' : '#DC2626'}; margin: 5px 0;">
              📈 Laba Bersih: Rp ${(reportData.income.total - reportData.expenses.total).toLocaleString("id-ID")}
            </h4>
          </div>
          
          <div style="margin-top: 15px;">
            <h4 style="color: #7C3AED; margin: 5px 0;">📊 Transaksi</h4>
            <p style="margin: 5px 0;">Penjualan: ${reportData.transactions.salesCount} transaksi</p>
            <p style="margin: 5px 0;">Pembelian: ${reportData.transactions.purchasesCount} transaksi</p>
            <p style="margin: 5px 0;">Servis: ${reportData.transactions.servicesCount} transaksi</p>
            <p style="margin: 5px 0; font-weight: bold;">Total: ${reportData.transactions.total} transaksi</p>
          </div>
        </div>
        
        <p style="color: #64748b;">Laporan lengkap tersedia dalam file Excel terlampir.</p>
        
        <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">
        <p style="color: #94a3b8; font-size: 12px;">
          Email ini dikirim secara otomatis dari sistem Kasir Online.<br>
          Jika Anda tidak ingin menerima laporan harian, silakan ubah pengaturan notifikasi di dashboard.
        </p>
      </div>
    `;

    // Send email with attachment
    const { data, error } = await resend.emails.send({
      from: "Laporan Harian <<EMAIL>>",
      to: userEmail,
      subject: `Laporan Harian - ${dateStr}`,
      html: emailHtml,
      attachments: [
        {
          filename: fileName,
          content: excelBuffer,
        },
      ],
    });

    if (error) {
      console.error("Error sending daily report email:", error);
      return { success: false, error: "Gagal mengirim email laporan harian" };
    }

    return { success: true };
  } catch (error) {
    console.error("Error in sendDailyReportEmail:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
