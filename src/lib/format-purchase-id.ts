/**
 * Formats a purchase ID into a more readable format
 * 
 * @param id The original purchase ID (CUID)
 * @param createdAt Optional creation date to include in the formatted ID
 * @returns A formatted, more readable ID
 */
export function formatPurchaseId(id: string, createdAt?: string | Date): string {
  // Extract the first 8 characters of the ID
  const shortId = id.substring(0, 8);
  
  // If createdAt is provided, include a date component
  if (createdAt) {
    const date = new Date(createdAt);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    // Format: PUR-YYYYMMDD-ABCD123
    return `PUR-${year}${month}${day}-${shortId}`;
  }
  
  // If no date, just use a simple prefix
  return `PUR-${shortId}`;
}
