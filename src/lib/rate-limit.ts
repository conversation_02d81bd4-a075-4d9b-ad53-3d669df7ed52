// Simple in-memory rate limiting for login attempts
// In production, consider using Redis or a database for persistence

interface RateLimitEntry {
  attempts: number;
  lastAttempt: number;
  blockedUntil?: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

// Configuration
const MAX_ATTEMPTS = 5; // Maximum login attempts
const WINDOW_MS = 15 * 60 * 1000; // 15 minutes window
const BLOCK_DURATION_MS = 30 * 60 * 1000; // 30 minutes block duration

/**
 * Check if an IP/email is rate limited
 * @param identifier - IP address or email
 * @returns boolean indicating if the request should be blocked
 */
export function isRateLimited(identifier: string): boolean {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);

  if (!entry) {
    return false;
  }

  // Check if still blocked
  if (entry.blockedUntil && now < entry.blockedUntil) {
    return true;
  }

  // Reset if window has passed
  if (now - entry.lastAttempt > WINDOW_MS) {
    rateLimitStore.delete(identifier);
    return false;
  }

  return false;
}

/**
 * Record a failed login attempt
 * @param identifier - IP address or email
 * @returns boolean indicating if the user is now blocked
 */
export function recordFailedAttempt(identifier: string): boolean {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);

  if (!entry) {
    rateLimitStore.set(identifier, {
      attempts: 1,
      lastAttempt: now,
    });
    return false;
  }

  // Reset if window has passed
  if (now - entry.lastAttempt > WINDOW_MS) {
    rateLimitStore.set(identifier, {
      attempts: 1,
      lastAttempt: now,
    });
    return false;
  }

  // Increment attempts
  entry.attempts += 1;
  entry.lastAttempt = now;

  // Block if max attempts reached
  if (entry.attempts >= MAX_ATTEMPTS) {
    entry.blockedUntil = now + BLOCK_DURATION_MS;
    return true;
  }

  return false;
}

/**
 * Record a successful login (clears the rate limit)
 * @param identifier - IP address or email
 */
export function recordSuccessfulAttempt(identifier: string): void {
  rateLimitStore.delete(identifier);
}

/**
 * Get remaining attempts before rate limit
 * @param identifier - IP address or email
 * @returns number of remaining attempts
 */
export function getRemainingAttempts(identifier: string): number {
  const entry = rateLimitStore.get(identifier);
  if (!entry) {
    return MAX_ATTEMPTS;
  }

  const now = Date.now();
  
  // Reset if window has passed
  if (now - entry.lastAttempt > WINDOW_MS) {
    return MAX_ATTEMPTS;
  }

  return Math.max(0, MAX_ATTEMPTS - entry.attempts);
}

/**
 * Get time until unblocked (in milliseconds)
 * @param identifier - IP address or email
 * @returns milliseconds until unblocked, or 0 if not blocked
 */
export function getTimeUntilUnblocked(identifier: string): number {
  const entry = rateLimitStore.get(identifier);
  if (!entry || !entry.blockedUntil) {
    return 0;
  }

  const now = Date.now();
  return Math.max(0, entry.blockedUntil - now);
}

// Cleanup function to remove old entries (should be called periodically)
export function cleanupRateLimitStore(): void {
  const now = Date.now();
  const cutoff = now - WINDOW_MS;

  for (const [identifier, entry] of rateLimitStore.entries()) {
    // Remove entries that are old and not blocked
    if (entry.lastAttempt < cutoff && (!entry.blockedUntil || entry.blockedUntil < now)) {
      rateLimitStore.delete(identifier);
    }
  }
}

// Auto-cleanup every 30 minutes
setInterval(cleanupRateLimitStore, 30 * 60 * 1000);
