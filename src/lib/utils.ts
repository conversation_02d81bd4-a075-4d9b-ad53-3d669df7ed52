import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Format a date string to a localized format
 * @param dateString ISO date string
 * @returns Formatted date string
 */
export function formatDate(dateString: string): string {
  if (!dateString) return "-";

  try {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat("id-ID", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(date);
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
}

/**
 * Format a date string to a detailed format with time and timezone
 * @param dateString ISO date string
 * @returns Formatted date string with full timestamp
 */
export function formatDetailedDate(dateString: string): string {
  if (!dateString) return "-";

  try {
    const date = new Date(dateString);

    // Format date and time
    const dateTimeFormat = new Intl.DateTimeFormat("id-ID", {
      weekday: "long",
      day: "2-digit",
      month: "long",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      timeZoneName: "short",
    });

    return dateTimeFormat.format(date);
  } catch (error) {
    console.error("Error formatting detailed date:", error);
    return dateString;
  }
}

/**
 * Format a date string to show relative time (e.g., "2 hours ago")
 * @param dateString ISO date string
 * @returns Relative time string
 */
export function formatRelativeTime(dateString: string): string {
  if (!dateString) return "-";

  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return "Baru saja";
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} menit yang lalu`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} jam yang lalu`;
    } else if (diffInSeconds < 2592000) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} hari yang lalu`;
    } else {
      return formatDate(dateString);
    }
  } catch (error) {
    console.error("Error formatting relative time:", error);
    return dateString;
  }
}

/**
 * Format a number as Indonesian currency with dot separators
 * @param value Number to format
 * @param withSymbol Whether to include the 'Rp' symbol
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number,
  withSymbol: boolean = true
): string {
  if (isNaN(value)) return withSymbol ? "Rp 0" : "0";

  // Format with dots as thousand separators
  const formatted = value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");

  return withSymbol ? `Rp ${formatted}` : formatted;
}
