import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { getUserSubscription } from "@/lib/subscription";
import {
  canCreateProduct,
  canCreateTransaction,
  canCreateEmployee,
  canCreateContact,
  canCreateSupplier,
  canCreateCustomer,
  hasNotificationAccess,
} from "@/lib/subscription-limits";

/**
 * Middleware to check subscription limits for API routes
 */
export async function checkSubscriptionLimits(
  request: NextRequest,
  limitType:
    | "product"
    | "transaction"
    | "employee"
    | "contact"
    | "supplier"
    | "customer"
    | "notification"
): Promise<{ allowed: boolean; response?: NextResponse; userId?: string }> {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user?.id) {
      return {
        allowed: false,
        response: NextResponse.json({ error: "Unauthorized" }, { status: 401 }),
      };
    }

    const userId = session.user.id;

    // Get user subscription
    const userSubscription = await getUserSubscription(userId);

    // Check specific limit based on type
    let limitCheck;
    switch (limitType) {
      case "product":
        limitCheck = await canCreateProduct(userId, userSubscription.plan);
        break;
      case "transaction":
        limitCheck = await canCreateTransaction(userId, userSubscription.plan);
        break;
      case "employee":
        limitCheck = await canCreateEmployee(userId, userSubscription.plan);
        break;
      case "contact":
        limitCheck = await canCreateContact(userId, userSubscription.plan);
        break;
      case "supplier":
        limitCheck = await canCreateSupplier(userId, userSubscription.plan);
        break;
      case "customer":
        limitCheck = await canCreateCustomer(userId, userSubscription.plan);
        break;
      case "notification":
        const hasAccess = hasNotificationAccess(userSubscription.plan);
        limitCheck = {
          allowed: hasAccess,
          message: hasAccess
            ? undefined
            : "Akses notifikasi tidak tersedia untuk paket Gratis.",
        };
        break;
      default:
        return {
          allowed: false,
          response: NextResponse.json(
            { error: "Invalid limit type" },
            { status: 400 }
          ),
        };
    }

    if (!limitCheck.allowed) {
      return {
        allowed: false,
        response: NextResponse.json(
          {
            error: limitCheck.message || "Subscription limit exceeded",
            limitType,
            currentUsage: limitCheck.currentUsage,
            limit: limitCheck.limit,
          },
          { status: 403 }
        ),
      };
    }

    return { allowed: true, userId };
  } catch (error) {
    console.error("Error checking subscription limits:", error);
    return {
      allowed: false,
      response: NextResponse.json(
        { error: "Failed to check subscription limits" },
        { status: 500 }
      ),
    };
  }
}

/**
 * Higher-order function to wrap API routes with subscription limit checking
 */
export function withSubscriptionLimit(
  limitType: "product" | "transaction" | "employee" | "contact" | "notification"
) {
  return function <T extends any[]>(
    handler: (request: NextRequest, ...args: T) => Promise<NextResponse>
  ) {
    return async function (
      request: NextRequest,
      ...args: T
    ): Promise<NextResponse> {
      const limitCheck = await checkSubscriptionLimits(request, limitType);

      if (!limitCheck.allowed) {
        return limitCheck.response!;
      }

      // Add userId to request for convenience
      (request as any).userId = limitCheck.userId;

      return handler(request, ...args);
    };
  };
}

/**
 * Utility function to get usage summary for API responses
 */
export async function getUsageSummaryForAPI(userId: string) {
  try {
    const userSubscription = await getUserSubscription(userId);
    const { getUsageSummary } = await import("@/lib/subscription-limits");
    return await getUsageSummary(userId, userSubscription.plan);
  } catch (error) {
    console.error("Error getting usage summary:", error);
    return null;
  }
}
