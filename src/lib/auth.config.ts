// this file for edge browser compatible
import Google from "next-auth/providers/google";
import type { NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import { db as database } from "./prisma";
import bcrypt from "bcryptjs";
import { LoginSchema, EmployeeLoginSchema } from "../schemas/zod";
import { Role } from "@prisma/client";

// Notice this is only an object, not a full Auth.js instance
export default {
  providers: [
    // Provider untuk login dengan email dan password
    Credentials({
      credentials: {
        email: { type: "email" },
        password: { type: "password" },
      },
      async authorize(credentials) {
        console.log("🔐 CREDENTIALS AUTHORIZE CALLED");
        console.log("📧 Credentials received:", {
          email: credentials?.email,
          passwordLength:
            typeof credentials?.password === "string"
              ? credentials.password.length
              : 0,
        });

        // Validasi input menggunakan schema Zod
        const validatedFields = LoginSchema.safeParse(credentials);
        if (!validatedFields.success) {
          console.log(
            "❌ Credentials validation failed:",
            validatedFields.error
          );
          return null;
        }

        // Ambil email dan password dari data yang sudah divalidasi
        const { email, password } = validatedFields.data;

        // Find user by email
        const user = await database.user.findUnique({
          where: { email },
        });

        // Check if user exists and has password
        if (!user || !user.password) {
          return null;
        }

        // Check email verification status
        if (!user.emailVerified) {
          return null;
        }

        // Verify password
        const isPasswordValid = await bcrypt.compare(password, user.password);

        if (!isPasswordValid) {
          return null;
        }
        return {
          ...user,
          role: user.role ?? Role.CASHIER,
        };
      },
    }),

    // Provider untuk login sebagai karyawan
    Credentials({
      id: "employee-credentials",
      name: "Employee Login",
      credentials: {
        companyUsername: { type: "text", label: "Username Perusahaan" },
        employeeId: { type: "text", label: "ID Karyawan" },
        password: { type: "password", label: "Password" },
      },
      async authorize(credentials) {
        console.log("🔐 AUTH CONFIG: Employee authorization started");

        // Validasi input menggunakan schema Zod
        const validatedFields = EmployeeLoginSchema.safeParse(credentials);
        if (!validatedFields.success) {
          console.log(
            "❌ AUTH CONFIG: Validation failed:",
            validatedFields.error
          );
          return null;
        }

        // Get validated input data
        const { companyUsername, employeeId, password } = validatedFields.data;

        // Find owner by companyUsername
        const owner = await database.user.findFirst({
          where: {
            businessInfo: {
              companyUsername: companyUsername,
            },
          },
          include: {
            businessInfo: {
              select: {
                companyUsername: true,
              },
            },
          },
        });

        // Check if owner exists and has correct role
        if (!owner || owner.role !== Role.OWNER) {
          throw new Error("Perusahaan tidak ditemukan!");
        }

        // Find employee by employeeId and ownerId
        const employee = await database.employee.findFirst({
          where: {
            employeeId,
            ownerId: owner.id,
          },
        });

        // Check if employee exists
        if (!employee) {
          throw new Error("Karyawan tidak ditemukan!");
        }

        // Verify employee password
        const isPasswordValid = await bcrypt.compare(
          password,
          employee.password
        );

        if (!isPasswordValid) {
          throw new Error("Password salah!");
        }

        // Return data karyawan dengan format yang sesuai dengan User
        return {
          id: employee.id,
          name: employee.name,
          role: employee.role,
          isEmployee: true,
          ownerId: employee.ownerId,
          employeeId: employee.employeeId,
        };
      },
    }),

    // Provider untuk login dengan Google
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
  ],
} satisfies NextAuthConfig;
