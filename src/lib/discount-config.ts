// Discount configuration for subscription plans
export interface DiscountConfig {
  annual: {
    percentage: number;
    description: string;
  };
  // Future discount types can be added here
  promotional?: {
    percentage: number;
    description: string;
    validUntil?: Date;
  };
}

// Centralized discount configuration
export const DISCOUNT_CONFIG: DiscountConfig = {
  annual: {
    percentage: 10, // 10% discount for annual payments (configurable)
    description: "Hemat 10% dengan berlangganan tahunan",
  },
  // Example promotional discount (commented out)
  // promotional: {
  //   percentage: 15,
  //   description: "Diskon spesial 15% untuk pelanggan baru",
  //   validUntil: new Date('2024-12-31'),
  // },
};

// Helper function to calculate discounted price
export function calculateDiscountedPrice(
  originalPrice: number,
  discountPercentage: number
): number {
  return Math.round(originalPrice * (1 - discountPercentage / 100));
}

// Helper function to calculate annual price with discount
export function calculateAnnualPrice(monthlyPrice: number): {
  originalAnnualPrice: number;
  discountedAnnualPrice: number;
  savings: number;
  discountPercentage: number;
} {
  const originalAnnualPrice = monthlyPrice * 12;
  const discountPercentage = DISCOUNT_CONFIG.annual.percentage;
  const discountedAnnualPrice = calculateDiscountedPrice(
    originalAnnualPrice,
    discountPercentage
  );
  const savings = originalAnnualPrice - discountedAnnualPrice;

  return {
    originalAnnualPrice,
    discountedAnnualPrice,
    savings,
    discountPercentage,
  };
}

// Helper function to format currency for display
export function formatCurrency(amount: number): string {
  return `Rp ${amount.toLocaleString("id-ID")}`;
}

// Helper function to get discount badge text
export function getDiscountBadgeText(discountPercentage: number): string {
  return `Hemat ${discountPercentage}%`;
}
