/**
 * Test utilities for subscription limits
 * This file contains functions to test and validate the subscription system
 */

import {
  canCreateProduct,
  canCreateTransaction,
  canCreateEmployee,
  canCreateContact,
  hasNotificationAccess,
  getCurrentUsage,
  getUsageSummary,
} from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";
import { SubscriptionPlan } from "@prisma/client";

export interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  details?: any;
}

/**
 * Test subscription limits for a specific user and plan
 */
export async function testSubscriptionLimits(
  userId: string
): Promise<TestResult[]> {
  const results: TestResult[] = [];

  try {
    // Get user subscription
    const userSubscription = await getUserSubscription(userId);
    const plan = userSubscription.plan;

    // Test 1: Get current usage
    try {
      const usage = await getCurrentUsage(userId);
      results.push({
        test: "Get Current Usage",
        passed: true,
        message: "Successfully retrieved current usage",
        details: usage,
      });
    } catch (error) {
      results.push({
        test: "Get Current Usage",
        passed: false,
        message: `Failed to get current usage: ${error}`,
      });
    }

    // Test 2: Product creation limits
    try {
      const productCheck = await canCreateProduct(userId, plan);
      results.push({
        test: "Product Creation Limit Check",
        passed: true,
        message: `Product creation ${productCheck.allowed ? "allowed" : "blocked"}`,
        details: productCheck,
      });
    } catch (error) {
      results.push({
        test: "Product Creation Limit Check",
        passed: false,
        message: `Failed to check product limits: ${error}`,
      });
    }

    // Test 3: Transaction limits
    try {
      const transactionCheck = await canCreateTransaction(userId, plan);
      results.push({
        test: "Transaction Creation Limit Check",
        passed: true,
        message: `Transaction creation ${transactionCheck.allowed ? "allowed" : "blocked"}`,
        details: transactionCheck,
      });
    } catch (error) {
      results.push({
        test: "Transaction Creation Limit Check",
        passed: false,
        message: `Failed to check transaction limits: ${error}`,
      });
    }

    // Test 4: Employee creation limits
    try {
      const employeeCheck = await canCreateEmployee(userId, plan);
      results.push({
        test: "Employee Creation Limit Check",
        passed: true,
        message: `Employee creation ${employeeCheck.allowed ? "allowed" : "blocked"}`,
        details: employeeCheck,
      });
    } catch (error) {
      results.push({
        test: "Employee Creation Limit Check",
        passed: false,
        message: `Failed to check employee limits: ${error}`,
      });
    }

    // Test 5: Contact creation limits
    try {
      const contactCheck = await canCreateContact(userId, plan);
      results.push({
        test: "Contact Creation Limit Check",
        passed: true,
        message: `Contact creation ${contactCheck.allowed ? "allowed" : "blocked"}`,
        details: contactCheck,
      });
    } catch (error) {
      results.push({
        test: "Contact Creation Limit Check",
        passed: false,
        message: `Failed to check contact limits: ${error}`,
      });
    }

    // Test 6: Notification access
    try {
      const notificationAccess = hasNotificationAccess(plan);
      results.push({
        test: "Notification Access Check",
        passed: true,
        message: `Notification access ${notificationAccess ? "granted" : "denied"}`,
        details: { hasAccess: notificationAccess, plan },
      });
    } catch (error) {
      results.push({
        test: "Notification Access Check",
        passed: false,
        message: `Failed to check notification access: ${error}`,
      });
    }

    // Test 7: Usage summary
    try {
      const summary = await getUsageSummary(userId, plan);
      results.push({
        test: "Usage Summary Generation",
        passed: true,
        message: "Successfully generated usage summary",
        details: summary,
      });
    } catch (error) {
      results.push({
        test: "Usage Summary Generation",
        passed: false,
        message: `Failed to generate usage summary: ${error}`,
      });
    }
  } catch (error) {
    results.push({
      test: "Overall Subscription Test",
      passed: false,
      message: `Failed to run subscription tests: ${error}`,
    });
  }

  return results;
}

/**
 * Test plan-specific limits
 */
export function testPlanLimits(): TestResult[] {
  const results: TestResult[] = [];

  // Test BASIC plan limits
  const basicPlan = "BASIC" as SubscriptionPlan;
  const basicExpected = {
    maxProducts: 200,
    maxTransactionsPerMonth: 1000,
    maxUsers: 10,
    maxContacts: 100,
    notifications: { website: true, email: false },
  };

  // Test PRO plan limits
  const proPlan = "PRO" as SubscriptionPlan;
  const proExpected = {
    maxProducts: 500,
    maxTransactionsPerMonth: 10000,
    maxUsers: 100,
    maxContacts: 1000,
    notifications: { website: true, email: true },
  };

  // Test ENTERPRISE plan limits
  const enterprisePlan = "ENTERPRISE" as SubscriptionPlan;
  const enterpriseExpected = {
    maxProducts: null,
    maxTransactionsPerMonth: null,
    maxUsers: null,
    maxContacts: null,
    notifications: { website: true, email: true },
  };

  const testCases = [
    { plan: basicPlan, expected: basicExpected, name: "BASIC" },
    { plan: proPlan, expected: proExpected, name: "PRO" },
    { plan: enterprisePlan, expected: enterpriseExpected, name: "ENTERPRISE" },
  ];

  testCases.forEach(({ plan, expected, name }) => {
    try {
      const { getSubscriptionLimits } = require("@/lib/subscription-limits");
      const limits = getSubscriptionLimits(plan);

      const passed =
        limits.maxProducts === expected.maxProducts &&
        limits.maxTransactionsPerMonth === expected.maxTransactionsPerMonth &&
        limits.maxUsers === expected.maxUsers &&
        limits.maxContacts === expected.maxContacts &&
        limits.notifications.website === expected.notifications.website &&
        limits.notifications.email === expected.notifications.email;

      results.push({
        test: `${name} Plan Limits`,
        passed,
        message: passed
          ? `${name} plan limits are correct`
          : `${name} plan limits are incorrect`,
        details: { expected, actual: limits },
      });
    } catch (error) {
      results.push({
        test: `${name} Plan Limits`,
        passed: false,
        message: `Failed to test ${name} plan limits: ${error}`,
      });
    }
  });

  return results;
}

/**
 * Generate a test report
 */
export function generateTestReport(results: TestResult[]): string {
  const passed = results.filter((r) => r.passed).length;
  const total = results.length;
  const failed = total - passed;

  let report = `\n=== SUBSCRIPTION SYSTEM TEST REPORT ===\n`;
  report += `Total Tests: ${total}\n`;
  report += `Passed: ${passed}\n`;
  report += `Failed: ${failed}\n`;
  report += `Success Rate: ${((passed / total) * 100).toFixed(1)}%\n\n`;

  report += `=== TEST RESULTS ===\n`;
  results.forEach((result, index) => {
    const status = result.passed ? "✅ PASS" : "❌ FAIL";
    report += `${index + 1}. ${status} - ${result.test}\n`;
    report += `   ${result.message}\n`;
    if (!result.passed && result.details) {
      report += `   Details: ${JSON.stringify(result.details, null, 2)}\n`;
    }
    report += `\n`;
  });

  return report;
}
