"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { revalidatePath } from "next/cache";
import { 
  Warehouse, 
  WarehouseFormData, 
  StockTransferData, 
  StockAdjustmentData,
  StockMovementType 
} from "@/types/warehouse";

// Get all warehouses for the current user
export async function getWarehouses(): Promise<Warehouse[]> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const warehouses = await db.warehouse.findMany({
      where: { userId },
      orderBy: [
        { isDefault: "desc" },
        { name: "asc" }
      ],
    });

    return warehouses;
  } catch (error) {
    console.error("Error fetching warehouses:", error);
    throw new Error("Failed to fetch warehouses");
  }
}

// Get a single warehouse by ID
export async function getWarehouse(id: string): Promise<Warehouse | null> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const warehouse = await db.warehouse.findFirst({
      where: { 
        id,
        userId 
      },
    });

    return warehouse;
  } catch (error) {
    console.error("Error fetching warehouse:", error);
    throw new Error("Failed to fetch warehouse");
  }
}

// Create a new warehouse
export async function createWarehouse(data: WarehouseFormData): Promise<Warehouse> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    // Check if this is the first warehouse (make it default)
    const existingWarehouses = await db.warehouse.count({
      where: { userId }
    });

    const isFirstWarehouse = existingWarehouses === 0;

    // If setting as default, unset other defaults
    if (data.isDefault || isFirstWarehouse) {
      await db.warehouse.updateMany({
        where: { 
          userId,
          isDefault: true 
        },
        data: { isDefault: false }
      });
    }

    const warehouse = await db.warehouse.create({
      data: {
        ...data,
        isDefault: data.isDefault || isFirstWarehouse,
        userId,
      },
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");

    return warehouse;
  } catch (error) {
    console.error("Error creating warehouse:", error);
    throw new Error("Failed to create warehouse");
  }
}

// Update an existing warehouse
export async function updateWarehouse(id: string, data: WarehouseFormData): Promise<Warehouse> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    // If setting as default, unset other defaults
    if (data.isDefault) {
      await db.warehouse.updateMany({
        where: { 
          userId,
          isDefault: true,
          id: { not: id }
        },
        data: { isDefault: false }
      });
    }

    const warehouse = await db.warehouse.update({
      where: { 
        id,
        userId 
      },
      data,
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");

    return warehouse;
  } catch (error) {
    console.error("Error updating warehouse:", error);
    throw new Error("Failed to update warehouse");
  }
}

// Delete a warehouse
export async function deleteWarehouse(id: string): Promise<void> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    // Check if warehouse has stock
    const stockCount = await db.warehouseStock.count({
      where: { 
        warehouseId: id,
        quantity: { gt: 0 }
      }
    });

    if (stockCount > 0) {
      throw new Error("Cannot delete warehouse with existing stock. Please transfer stock to another warehouse first.");
    }

    // Check if warehouse is default
    const warehouse = await db.warehouse.findFirst({
      where: { id, userId }
    });

    if (warehouse?.isDefault) {
      throw new Error("Cannot delete the default warehouse. Please set another warehouse as default first.");
    }

    await db.warehouse.delete({
      where: { 
        id,
        userId 
      },
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");
  } catch (error) {
    console.error("Error deleting warehouse:", error);
    throw error;
  }
}

// Set warehouse as default
export async function setDefaultWarehouse(id: string): Promise<void> {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    // Unset all defaults first
    await db.warehouse.updateMany({
      where: { 
        userId,
        isDefault: true 
      },
      data: { isDefault: false }
    });

    // Set the new default
    await db.warehouse.update({
      where: { 
        id,
        userId 
      },
      data: { isDefault: true }
    });

    revalidatePath("/dashboard/products");
    revalidatePath("/dashboard/purchases");
    revalidatePath("/dashboard/sales");
  } catch (error) {
    console.error("Error setting default warehouse:", error);
    throw new Error("Failed to set default warehouse");
  }
}

// Get warehouse summary statistics
export async function getWarehouseSummary() {
  try {
    const userId = await getEffectiveUserId();
    if (!userId) {
      throw new Error("User not authenticated");
    }

    const [totalWarehouses, activeWarehouses, stockData] = await Promise.all([
      db.warehouse.count({ where: { userId } }),
      db.warehouse.count({ where: { userId, isActive: true } }),
      db.warehouseStock.aggregate({
        where: { 
          warehouse: { userId },
          quantity: { gt: 0 }
        },
        _sum: { quantity: true },
        _count: { id: true }
      })
    ]);

    const lowStockItems = await db.warehouseStock.count({
      where: {
        warehouse: { userId },
        quantity: { lte: db.warehouseStock.fields.minLevel }
      }
    });

    return {
      totalWarehouses,
      activeWarehouses,
      totalProducts: stockData._count.id || 0,
      totalStock: stockData._sum.quantity || 0,
      lowStockItems
    };
  } catch (error) {
    console.error("Error fetching warehouse summary:", error);
    throw new Error("Failed to fetch warehouse summary");
  }
}
