"use server";

import { z } from "zod";
import { LoginSchema } from "@/schemas/zod";
import { signIn } from "@/lib/auth";
import { AuthError } from "next-auth";
import { getUserByEmail } from "@/lib/user";
import {
  isRateLimited,
  recordFailedAttempt,
  recordSuccessfulAttempt,
  getTimeUntilUnblocked,
} from "@/lib/rate-limit";

export const login = async (values: z.infer<typeof LoginSchema>) => {
  // Validasi input menggunakan Zod schema
  const validatedFields = LoginSchema.safeParse(values);
  // Return error if validation fails
  if (!validatedFields.success) {
    return { error: "Input tidak valid!" };
  }

  // Get validated data
  const { email, password } = validatedFields.data;

  // Check rate limiting
  if (isRateLimited(email)) {
    const timeUntilUnblocked = getTimeUntilUnblocked(email);
    const minutesRemaining = Math.ceil(timeUntilUnblocked / (60 * 1000));
    return {
      error: `Terlalu banyak percobaan login. Coba lagi dalam ${minutesRemaining} menit.`,
    };
  }

  // Check if user exists
  const existingUser = await getUserByEmail(email);

  if (!existingUser) {
    recordFailedAttempt(email);
    return {
      error: "User tidak ditemukan!",
    };
  }

  // Check email verification
  if (!existingUser.emailVerified) {
    recordFailedAttempt(email);
    return {
      error: "Akun belum terverifikasi!\nSilakan cek email untuk verifikasi.",
    };
  }

  // Check if user has password
  if (!existingUser.password) {
    recordFailedAttempt(email);
    return { error: "User tidak ditemukan atau password salah!" };
  }

  try {
    // Attempt sign in with credentials
    await signIn("credentials", {
      redirect: false,
      email,
      password,
    });

    // Record successful login
    recordSuccessfulAttempt(email);

    return {
      success: "Login berhasil!",
      redirectTo: "/dashboard",
      refreshSession: true,
    };
  } catch (error) {
    // Record failed attempt for any authentication error
    recordFailedAttempt(email);

    if (error instanceof AuthError) {
      switch (error.type) {
        case "CredentialsSignin":
          return { error: "Email atau password salah!" };
        default:
          return { error: "Ada yang salah!" };
      }
    }

    // Check if this is a redirect error (which is actually a success)
    if (error instanceof Error && error.message?.includes("NEXT_REDIRECT")) {
      // This is actually a success, so record it as such
      recordSuccessfulAttempt(email);
      return {
        success: "Login berhasil!",
        redirectTo: "/dashboard",
        refreshSession: true,
      };
    }

    return { error: "Terjadi kesalahan saat login!" };
  }
};
