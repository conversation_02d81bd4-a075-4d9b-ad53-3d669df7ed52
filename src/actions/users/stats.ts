"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

export interface UserStats {
  totalLogins: number;
  lastWeekActivity: number;
  securityScore: number;
  featuresUsed: number;
  totalFeatures: number;
  accountAge: number;
  totalSales: number;
  totalPurchases: number;
  totalProducts: number;
  totalCustomers: number;
  totalSuppliers: number;
}

export interface ProfileCompletion {
  percentage: number;
  completedFields: string[];
  missingFields: string[];
}

/**
 * Get comprehensive user statistics from database
 */
export async function getUserStats(): Promise<{
  success: boolean;
  data?: UserStats;
  error?: string;
}> {
  try {
    const session = await auth();
    const effectiveUserId = await getEffectiveUserId();

    if (!session?.user?.id || !effectiveUserId) {
      return { success: false, error: "Tidak terautentikasi" };
    }

    // Get user data
    const user = await db.user.findUnique({
      where: { id: session.user.id },
      select: {
        createdAt: true,
        lastLogin: true,
        image: true,
        bio: true,
        phone: true,
        name: true,
        email: true,
        username: true,
      },
    });

    if (!user) {
      return { success: false, error: "User tidak ditemukan" };
    }

    // Calculate account age
    const accountAge = Math.ceil(
      (new Date().getTime() - user.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Get session count (approximate login count)
    const totalSessions = await db.session.count({
      where: { userId: session.user.id },
    });

    // Get business statistics
    const [
      totalSales,
      totalPurchases,
      totalProducts,
      totalCustomers,
      totalSuppliers,
    ] = await Promise.all([
      db.sale.count({ where: { userId: effectiveUserId } }),
      db.purchase.count({ where: { userId: effectiveUserId } }),
      db.product.count({ where: { userId: effectiveUserId } }),
      db.customer.count({ where: { userId: effectiveUserId } }),
      db.supplier.count({ where: { userId: effectiveUserId } }),
    ]);

    // Get recent activity (last 7 days)
    const lastWeek = new Date();
    lastWeek.setDate(lastWeek.getDate() - 7);

    const recentActivity = await Promise.all([
      db.sale.count({
        where: {
          userId: effectiveUserId,
          saleDate: { gte: lastWeek },
        },
      }),
      db.purchase.count({
        where: {
          userId: effectiveUserId,
          purchaseDate: { gte: lastWeek },
        },
      }),
      db.product.count({
        where: {
          userId: effectiveUserId,
          updatedAt: { gte: lastWeek },
        },
      }),
    ]);

    const lastWeekActivity = recentActivity.reduce((sum, count) => sum + count, 0);

    // Calculate security score based on profile completeness and activity
    let securityScore = 0;
    
    // Base score for having an account
    securityScore += 20;
    
    // Profile completeness (40 points max)
    if (user.image) securityScore += 10;
    if (user.bio) securityScore += 10;
    if (user.phone) securityScore += 10;
    if (user.name && user.email) securityScore += 10;
    
    // Activity score (20 points max)
    if (totalSales > 0) securityScore += 5;
    if (totalPurchases > 0) securityScore += 5;
    if (totalProducts > 0) securityScore += 5;
    if (lastWeekActivity > 0) securityScore += 5;
    
    // Recent login (20 points max)
    if (user.lastLogin) {
      const daysSinceLastLogin = Math.ceil(
        (new Date().getTime() - user.lastLogin.getTime()) / (1000 * 60 * 60 * 24)
      );
      if (daysSinceLastLogin <= 1) securityScore += 20;
      else if (daysSinceLastLogin <= 7) securityScore += 15;
      else if (daysSinceLastLogin <= 30) securityScore += 10;
      else securityScore += 5;
    }

    // Calculate features used
    const featuresUsed = [
      totalSales > 0, // Sales feature
      totalPurchases > 0, // Purchase feature
      totalProducts > 0, // Product management
      totalCustomers > 0, // Customer management
      totalSuppliers > 0, // Supplier management
      user.image !== null, // Profile image
      user.bio !== null, // Bio
      user.phone !== null, // Phone
    ].filter(Boolean).length;

    const totalFeatures = 8; // Total available features

    const stats: UserStats = {
      totalLogins: totalSessions,
      lastWeekActivity,
      securityScore: Math.min(securityScore, 100), // Cap at 100%
      featuresUsed,
      totalFeatures,
      accountAge,
      totalSales,
      totalPurchases,
      totalProducts,
      totalCustomers,
      totalSuppliers,
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error("Error fetching user stats:", error);
    return { success: false, error: "Gagal mengambil statistik pengguna" };
  }
}

/**
 * Calculate profile completion percentage and details
 */
export async function getProfileCompletion(): Promise<{
  success: boolean;
  data?: ProfileCompletion;
  error?: string;
}> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { success: false, error: "Tidak terautentikasi" };
    }

    // Get user and business info
    const [user, businessInfo] = await Promise.all([
      db.user.findUnique({
        where: { id: session.user.id },
        select: {
          name: true,
          email: true,
          username: true,
          image: true,
          phone: true,
          bio: true,
          birthday: true,
        },
      }),
      db.businessInfo.findUnique({
        where: { userId: session.user.id },
        select: {
          companyName: true,
          companyUsername: true,
          companyPhone: true,
          companyEmail: true,
          companyAddress: true,
          industry: true,
          position: true,
          employeeCount: true,
          city: true,
          postalCode: true,
        },
      }),
    ]);

    if (!user) {
      return { success: false, error: "User tidak ditemukan" };
    }

    const completedFields: string[] = [];
    const missingFields: string[] = [];

    // Check user fields
    const userFields = [
      { key: "name", value: user.name, label: "Nama" },
      { key: "email", value: user.email, label: "Email" },
      { key: "username", value: user.username, label: "Username" },
      { key: "image", value: user.image, label: "Foto Profil" },
      { key: "phone", value: user.phone, label: "Telepon" },
      { key: "bio", value: user.bio, label: "Bio" },
      { key: "birthday", value: user.birthday, label: "Tanggal Lahir" },
    ];

    // Check business info fields
    const businessFields = businessInfo ? [
      { key: "companyName", value: businessInfo.companyName, label: "Nama Perusahaan" },
      { key: "companyUsername", value: businessInfo.companyUsername, label: "Username Perusahaan" },
      { key: "companyPhone", value: businessInfo.companyPhone, label: "Telepon Perusahaan" },
      { key: "companyEmail", value: businessInfo.companyEmail, label: "Email Perusahaan" },
      { key: "companyAddress", value: businessInfo.companyAddress, label: "Alamat Perusahaan" },
      { key: "industry", value: businessInfo.industry, label: "Industri" },
      { key: "position", value: businessInfo.position, label: "Posisi" },
      { key: "employeeCount", value: businessInfo.employeeCount, label: "Jumlah Karyawan" },
    ] : [];

    const allFields = [...userFields, ...businessFields];

    allFields.forEach(field => {
      if (field.value && field.value.toString().trim() !== "") {
        completedFields.push(field.label);
      } else {
        missingFields.push(field.label);
      }
    });

    const percentage = Math.round((completedFields.length / allFields.length) * 100);

    return {
      success: true,
      data: {
        percentage,
        completedFields,
        missingFields,
      },
    };
  } catch (error) {
    console.error("Error calculating profile completion:", error);
    return { success: false, error: "Gagal menghitung kelengkapan profil" };
  }
}
