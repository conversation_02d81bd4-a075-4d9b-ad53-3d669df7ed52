"use server";

import { z } from "zod";
import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { revalidatePath } from "next/cache";

// Schema for additional user info
const BusinessInfoSchema = z.object({
  position: z.string().optional(),
  employeeCount: z.string().optional(),
  occupation: z.string().optional(),
  industry: z.string().optional(),
  subscriptionPackage: z.string().optional(),
  referralCode: z.string().optional(),
  companyName: z.string().optional(),
  companyUsername: z.string().optional(),
  companyAddress: z.string().optional(),
  companyLogo: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  billingAddress: z.string().optional(), // Legacy field for backward compatibility
  shippingAddress: z.string().optional(), // Legacy field for backward compatibility
  billingAddresses: z.array(z.string()).optional(), // New array field for multiple addresses
  shippingAddresses: z.array(z.string()).optional(), // New array field for multiple addresses
  faxNumber: z.string().optional(),
  website: z.string().optional(),
  companyEmail: z.string().optional(),
  companyPhone: z.string().optional(),
  // Operating hours and notification settings
  operatingHoursStart: z.string().optional(),
  operatingHoursEnd: z.string().optional(),
  notificationTime: z.string().optional(),
});

export type BusinessInfoData = z.infer<typeof BusinessInfoSchema>;

/**
 * Get additional user info for the current user
 */
export const getBusinessInfo = async () => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Get additional info
    const businessInfo = await db.businessInfo.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    return {
      success: true,
      data: {
        position: businessInfo?.position || null,
        employeeCount: businessInfo?.employeeCount || null,
        occupation: businessInfo?.occupation || null,
        industry: businessInfo?.industry || null,
        subscriptionPackage: businessInfo?.subscriptionPackage || null,
        referralCode: businessInfo?.referralCode || null,
        companyName: businessInfo?.companyName || null,
        companyUsername: businessInfo?.companyUsername || null,
        companyAddress: businessInfo?.companyAddress || null,
        companyLogo: businessInfo?.companyLogo || null,
        postalCode: businessInfo?.postalCode || null,
        city: businessInfo?.city || null,
        billingAddress: businessInfo?.billingAddress || null, // Legacy field
        shippingAddress: businessInfo?.shippingAddress || null, // Legacy field
        billingAddresses: businessInfo?.billingAddresses || [], // New array field
        shippingAddresses: businessInfo?.shippingAddresses || [], // New array field
        faxNumber: businessInfo?.companyFaxNumber || null,
        website: businessInfo?.companyWebsite || null,
        companyEmail: businessInfo?.companyEmail || null,
        companyPhone: businessInfo?.companyPhone || null,
        // Operating hours and notification settings
        operatingHoursStart: businessInfo?.operatingHoursStart || null,
        operatingHoursEnd: businessInfo?.operatingHoursEnd || null,
        notificationTime: businessInfo?.notificationTime || null,
      },
    };
  } catch (error) {
    console.error("Error fetching additional user info:", error);
    return { error: "Gagal mengambil informasi tambahan" };
  }
};

/**
 * Update additional user info for the current user
 */
export const updateBusinessInfo = async (values: BusinessInfoData) => {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { error: "Tidak terautentikasi!" };
    }

    // Validate input
    const validatedFields = BusinessInfoSchema.safeParse(values);
    if (!validatedFields.success) {
      return { error: "Data tidak valid!" };
    }

    const data = validatedFields.data;

    // Upsert additional user info
    await db.businessInfo.upsert({
      where: {
        userId: session.user.id,
      },
      update: {
        position: data.position || null,
        employeeCount: data.employeeCount || null,
        occupation: data.occupation || null,
        industry: data.industry || null,
        subscriptionPackage: data.subscriptionPackage || null,
        referralCode: data.referralCode || null,
        companyName: data.companyName || null,
        companyUsername: data.companyUsername || null,
        companyAddress: data.companyAddress || null,
        companyLogo: data.companyLogo || null,
        postalCode: data.postalCode || null,
        city: data.city || null,
        billingAddress: data.billingAddress || null, // Legacy field
        shippingAddress: data.shippingAddress || null, // Legacy field
        billingAddresses: data.billingAddresses || [], // New array field
        shippingAddresses: data.shippingAddresses || [], // New array field
        companyFaxNumber: data.faxNumber || null,
        companyWebsite: data.website || null,
        companyEmail: data.companyEmail || null,
        companyPhone: data.companyPhone || null,
        // Operating hours and notification settings
        operatingHoursStart: data.operatingHoursStart || null,
        operatingHoursEnd: data.operatingHoursEnd || null,
        notificationTime: data.notificationTime || null,
        updatedAt: new Date(),
      },
      create: {
        userId: session.user.id,
        position: data.position || null,
        employeeCount: data.employeeCount || null,
        occupation: data.occupation || null,
        industry: data.industry || null,
        subscriptionPackage: data.subscriptionPackage || null,
        referralCode: data.referralCode || null,
        companyName: data.companyName || null,
        companyUsername: data.companyUsername || null,
        companyAddress: data.companyAddress || null,
        companyLogo: data.companyLogo || null,
        postalCode: data.postalCode || null,
        city: data.city || null,
        billingAddress: data.billingAddress || null, // Legacy field
        shippingAddress: data.shippingAddress || null, // Legacy field
        billingAddresses: data.billingAddresses || [], // New array field
        shippingAddresses: data.shippingAddresses || [], // New array field
        companyFaxNumber: data.faxNumber || null,
        companyWebsite: data.website || null,
        companyEmail: data.companyEmail || null,
        companyPhone: data.companyPhone || null,
        // Operating hours and notification settings
        operatingHoursStart: data.operatingHoursStart || null,
        operatingHoursEnd: data.operatingHoursEnd || null,
        notificationTime: data.notificationTime || null,
      },
    });

    // Revalidate the profile page
    revalidatePath("/dashboard/settings/profile");

    return { success: "Informasi tambahan berhasil diperbarui!" };
  } catch (error) {
    console.error("Error updating additional user info:", error);
    return { error: "Gagal memperbarui informasi tambahan" };
  }
};
