"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { z } from "zod";
import { revalidatePath } from "next/cache";

// Define schema for session settings
const SessionSettingsSchema = z.object({
  sessionTimeout: z.string(),
  loginNotifications: z.boolean(),
});

export type SessionSettings = z.infer<typeof SessionSettingsSchema>;

/**
 * Update user session settings
 */
export async function updateSessionSettings(
  settings: SessionSettings
): Promise<{ success: boolean; message: string }> {
  try {
    // Validate input
    const validatedSettings = SessionSettingsSchema.safeParse(settings);
    if (!validatedSettings.success) {
      return {
        success: false,
        message: "Data pengaturan tidak valid",
      };
    }

    // Get current user
    const session = await auth();
    if (!session?.user?.id) {
      return {
        success: false,
        message: "Tidak terautentikasi",
      };
    }

    // Create or update notification settings
    // We'll use the NotificationSettings model to store our session settings
    await db.notificationSettings.upsert({
      where: {
        userId: session.user.id,
      },
      create: {
        userId: session.user.id,
        // Map session timeout to notification settings fields
        // This is a simple mapping for demonstration purposes
        dailySummary: validatedSettings.data.sessionTimeout === "30",
        weeklySummary: validatedSettings.data.loginNotifications,
        // Set other fields based on session timeout for storage
        emailEnabled:
          validatedSettings.data.sessionTimeout === "60" ||
          validatedSettings.data.sessionTimeout === "15",
        emailInfoEnabled:
          validatedSettings.data.sessionTimeout !== "480" &&
          validatedSettings.data.sessionTimeout !== "1440",
        emailWarningEnabled: validatedSettings.data.sessionTimeout === "1440",
        emailSuccessEnabled: true,
        emailErrorEnabled: true,
      },
      update: {
        // Map session timeout to notification settings fields
        dailySummary: validatedSettings.data.sessionTimeout === "30",
        weeklySummary: validatedSettings.data.loginNotifications,
        // Update other fields based on session timeout for storage
        emailEnabled:
          validatedSettings.data.sessionTimeout === "60" ||
          validatedSettings.data.sessionTimeout === "15",
        emailInfoEnabled:
          validatedSettings.data.sessionTimeout !== "480" &&
          validatedSettings.data.sessionTimeout !== "1440",
        emailWarningEnabled: validatedSettings.data.sessionTimeout === "1440",
      },
    });

    // Revalidate paths that might display this data
    revalidatePath("/dashboard/settings/security");

    return {
      success: true,
      message: "Pengaturan sesi berhasil disimpan",
    };
  } catch (error) {
    console.error("Error updating session settings:", error);
    return {
      success: false,
      message: "Terjadi kesalahan saat menyimpan pengaturan",
    };
  }
}

/**
 * Helper function to map notification settings to session timeout
 */
function getSessionTimeoutFromSettings(settings: any): string {
  // In a real implementation, you would store this mapping in a more appropriate way
  // For now, we're using the dailySummary field to determine the timeout
  if (settings.dailySummary) {
    return "30"; // Default timeout
  } else if (settings.weeklySummary && !settings.emailEnabled) {
    return "60"; // 1 hour
  } else if (!settings.weeklySummary && settings.emailEnabled) {
    return "120"; // 2 hours
  } else if (!settings.weeklySummary && !settings.emailEnabled) {
    return "240"; // 4 hours
  } else if (settings.emailInfoEnabled && !settings.emailWarningEnabled) {
    return "480"; // 8 hours
  } else if (!settings.emailInfoEnabled && settings.emailWarningEnabled) {
    return "1440"; // 24 hours
  } else {
    return "30"; // Default fallback
  }
}

export async function getSessionSettings(): Promise<{
  success: boolean;
  data?: SessionSettings;
  message?: string;
}> {
  try {
    // Get current user
    const session = await auth();
    if (!session?.user?.id) {
      return {
        success: false,
        message: "Tidak terautentikasi",
      };
    }

    // Get notification settings from database
    const settings = await db.notificationSettings.findUnique({
      where: {
        userId: session.user.id,
      },
    });

    // If no settings found, return defaults
    if (!settings) {
      return {
        success: true,
        data: {
          sessionTimeout: "30", // Default
          loginNotifications: true, // Default
        },
      };
    }

    // Extract session settings from notification settings
    // We're using dailySummary field to store session timeout (repurposed)
    // We're using weeklySummary field to store login notifications (repurposed)
    return {
      success: true,
      data: {
        // Map the dailySummary field to session timeout values
        // This is a simple mapping for demonstration purposes
        sessionTimeout: getSessionTimeoutFromSettings(settings),
        loginNotifications: settings.weeklySummary,
      },
    };
  } catch (error) {
    console.error("Error getting session settings:", error);
    return {
      success: false,
      message: "Terjadi kesalahan saat mengambil pengaturan",
    };
  }
}
