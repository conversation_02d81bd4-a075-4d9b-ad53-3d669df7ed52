"use server";

import { db } from "@/lib/prisma";
import { auth } from "@/lib/auth";
import { revalidatePath } from "next/cache";
import { cookies } from "next/headers";

export interface DeviceSession {
  id: string;
  name: string;
  type: "desktop" | "mobile" | "tablet";
  lastLogin: string;
  isCurrent: boolean;
}

/**
 * Get all active sessions for the current user
 */
export async function getDeviceSessions(): Promise<DeviceSession[]> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return [];
    }

    // Get the current session token
    const cookieStore = await cookies();
    const currentSessionToken = cookieStore.get(
      "next-auth.session-token"
    )?.value;

    // Get all sessions for the user from the database
    const userSessions = await db.session.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        expires: "desc",
      },
    });

    if (!userSessions.length) {
      return [];
    }

    // Format the sessions for display
    return userSessions.map((dbSession) => {
      // Parse user agent from session data if available
      // In a real app, you would store user agent info in the session
      // For this demo, we'll use some basic detection
      const isMobile = dbSession.sessionToken.includes("mobile");
      const isTablet = dbSession.sessionToken.includes("tablet");

      // Format the date
      const lastLoginDate = new Date(dbSession.expires);
      lastLoginDate.setHours(lastLoginDate.getHours() - 6); // Assuming 6-hour sessions

      // Format relative time
      const now = new Date();
      const diffInDays = Math.floor(
        (now.getTime() - lastLoginDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      let lastLoginFormatted;
      if (diffInDays === 0) {
        lastLoginFormatted = `Hari ini, ${lastLoginDate.getHours()}:${String(lastLoginDate.getMinutes()).padStart(2, "0")}`;
      } else if (diffInDays === 1) {
        lastLoginFormatted = "Kemarin";
      } else {
        lastLoginFormatted = `${diffInDays} hari yang lalu`;
      }

      // Determine device type and name
      let type: "desktop" | "mobile" | "tablet" = "desktop";
      let name = "Browser";

      if (isMobile) {
        type = "mobile";
        name = "Mobile Browser";
      } else if (isTablet) {
        type = "tablet";
        name = "Tablet Browser";
      }

      // For demo purposes, add some variety to device names
      if (dbSession.sessionToken.includes("Chrome")) {
        name = `Chrome - ${type === "desktop" ? "Windows" : "Android"}`;
      } else if (dbSession.sessionToken.includes("Safari")) {
        name = `Safari - ${type === "desktop" ? "macOS" : "iOS"}`;
      } else if (dbSession.sessionToken.includes("Firefox")) {
        name = `Firefox - ${type === "desktop" ? "Linux" : "Mobile"}`;
      }

      return {
        id: dbSession.id,
        name,
        type,
        lastLogin: lastLoginFormatted,
        isCurrent: dbSession.sessionToken === currentSessionToken,
      };
    });
  } catch (error) {
    console.error("Error fetching device sessions:", error);
    return [];
  }
}

/**
 * Revoke a specific session
 */
export async function revokeSession(
  sessionId: string
): Promise<{ success: boolean; message: string }> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { success: false, message: "Tidak terautentikasi" };
    }

    // Get the session to check if it belongs to the user
    const dbSession = await db.session.findUnique({
      where: { id: sessionId },
    });

    if (!dbSession) {
      return { success: false, message: "Sesi tidak ditemukan" };
    }

    // Verify the session belongs to the current user
    if (dbSession.userId !== session.user.id) {
      return { success: false, message: "Tidak diizinkan" };
    }

    // Check if this is the current session
    const cookieStore = await cookies();
    const currentSessionToken = cookieStore.get(
      "next-auth.session-token"
    )?.value;
    const isCurrentSession = dbSession.sessionToken === currentSessionToken;

    // Delete the session
    await db.session.delete({
      where: { id: sessionId },
    });

    revalidatePath("/dashboard/settings/security");

    if (isCurrentSession) {
      return {
        success: true,
        message: "current_session",
      };
    }

    return {
      success: true,
      message: "Sesi berhasil dihapus",
    };
  } catch (error) {
    console.error("Error revoking session:", error);
    return { success: false, message: "Terjadi kesalahan saat menghapus sesi" };
  }
}

/**
 * Revoke all sessions except the current one
 */
export async function revokeAllSessions(): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { success: false, message: "Tidak terautentikasi" };
    }

    // Get the current session token
    const cookieStore = await cookies();
    const currentSessionToken = cookieStore.get(
      "next-auth.session-token"
    )?.value;

    if (!currentSessionToken) {
      return { success: false, message: "Sesi saat ini tidak ditemukan" };
    }

    // Delete all sessions except the current one
    await db.session.deleteMany({
      where: {
        userId: session.user.id,
        NOT: {
          sessionToken: currentSessionToken,
        },
      },
    });

    revalidatePath("/dashboard/settings/security");

    return {
      success: true,
      message: "Semua sesi lain berhasil dihapus",
    };
  } catch (error) {
    console.error("Error revoking all sessions:", error);
    return {
      success: false,
      message: "Terjadi kesalahan saat menghapus semua sesi",
    };
  }
}

/**
 * Revoke all sessions including the current one (full logout)
 */
export async function revokeAllSessionsIncludingCurrent(): Promise<{
  success: boolean;
  message: string;
}> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return { success: false, message: "Tidak terautentikasi" };
    }

    // Delete all sessions for the user
    await db.session.deleteMany({
      where: {
        userId: session.user.id,
      },
    });

    // This will trigger a logout and redirect
    return {
      success: true,
      message: "all_sessions",
    };
  } catch (error) {
    console.error("Error revoking all sessions:", error);
    return {
      success: false,
      message: "Terjadi kesalahan saat menghapus semua sesi",
    };
  }
}
