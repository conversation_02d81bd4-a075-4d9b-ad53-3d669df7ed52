"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { AdvancedFilters, getDateRange } from "./_utils";
import { startOfDay, endOfDay } from "date-fns";

// Function to get sales report data
export const getSalesReportData = async (dateRange: string) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    const sales = await db.sale.findMany({
      where: {
        userId: effectiveUserId,
        saleDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        items: true, // Include the full items relation
        customer: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        saleDate: "desc",
      },
    });

    const salesData = sales.map((sale) => {
      const totalItems = sale.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      return {
        id: sale.transactionNumber || sale.id,
        saleDate: sale.saleDate.toISOString(),
        invoiceRef: sale.invoiceRef || "-",
        paymentDueDate: sale.paymentDueDate
          ? sale.paymentDueDate.toISOString()
          : "-",
        customer: {
          name: sale.customer?.name || "Pelanggan Umum",
        },
        totalAmount: sale.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: sale.saleDate.toISOString(),
        total: sale.totalAmount.toNumber(),
        transactionNumber: sale.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: salesData,
    };
  } catch (error) {
    console.error("Error fetching sales report data:", error);
    return {
      error: "Gagal mengambil data laporan penjualan.",
    };
  }
};

// Enhanced function to get sales report data with advanced filters
export const getSalesReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause with advanced filters
    const whereClause: any = {
      userId: effectiveUserId,
      saleDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    // Add customer filter if provided
    if (filters.customer) {
      whereClause.customer = {
        name: {
          contains: filters.customer,
          mode: "insensitive",
        },
      };
    }

    // Add status filter if provided
    if (filters.status) {
      whereClause.status = filters.status;
    }

    const sales = await db.sale.findMany({
      where: whereClause,
      include: {
        customer: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
                category: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        saleDate: "desc",
      },
    });

    // Filter by category if provided (filter at application level since it's nested)
    let filteredSales = sales;
    if (filters.category) {
      filteredSales = sales.filter((sale) =>
        sale.items.some((item) =>
          item.product.category?.name
            ?.toLowerCase()
            .includes(filters.category!.toLowerCase())
        )
      );
    }

    const salesData = filteredSales.map((sale) => {
      const totalItems = sale.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );

      return {
        id: sale.transactionNumber || sale.id,
        saleDate: sale.saleDate.toISOString(),
        invoiceRef: sale.invoiceRef || "-",
        paymentDueDate: sale.paymentDueDate
          ? sale.paymentDueDate.toISOString()
          : "-",
        customer: {
          name: sale.customer?.name || "Pelanggan Umum",
        },
        totalAmount: sale.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: sale.saleDate.toISOString(),
        total: sale.totalAmount.toNumber(),
        transactionNumber: sale.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: salesData,
    };
  } catch (error) {
    console.error("Error fetching sales report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan penjualan.",
    };
  }
};
