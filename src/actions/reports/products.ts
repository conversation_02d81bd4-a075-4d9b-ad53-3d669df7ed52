"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { AdvancedFilters, getDateRange } from "./_utils";
import { startOfDay, endOfDay } from "date-fns";

// Function to get product report data
export const getProductReportData = async (dateRange: string) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch all products for the user
    const products = await db.product.findMany({
      where: {
        userId: effectiveUserId,
      },
      include: {
        category: {
          select: { name: true },
        },
        variants: {
          select: {
            colorName: true,
          },
        },
        saleItems: {
          where: {
            sale: {
              saleDate: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          select: {
            quantity: true,
            priceAtSale: true,
          },
        },
        // We might need purchaseItems if we want to calculate profit based on costAtPurchase
        // purchaseItems: {
        //   where: { purchase: { purchaseDate: { gte: startDate, lte: endDate } } },
        //   select: { quantity: true, costAtPurchase: true }
        // }
      },
      orderBy: {
        id: "asc", // Sort by product ID in ascending order (000001, 000002, etc.)
      },
    });

    const productData = products.map((product) => {
      const totalSold = product.saleItems.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      const totalRevenue = product.saleItems.reduce(
        (
          sum: number,
          item: { quantity: number; priceAtSale: { toNumber: () => number } }
        ) => sum + item.quantity * item.priceAtSale.toNumber(),
        0
      );
      // Basic profit calculation (Revenue - Cost * Sold). Requires cost on Product model.
      const totalCost = product.cost ? product.cost.toNumber() * totalSold : 0;
      const totalProfit = totalRevenue - totalCost;

      // Determine stock status
      const stockStatus =
        product.stock <= 0
          ? "Habis"
          : product.stock <= 10
            ? "Stok Rendah"
            : "Tersedia";

      return {
        id: product.id,
        name: product.name,
        description: product.description || "", // Add description field
        sku: product.sku || "-",
        barcode: product.barcode || "-", // Use actual barcode from database
        unit: product.unit || "pcs",
        stock: product.stock,
        cost: product.cost ? product.cost.toNumber() : 0,
        price: product.price ? product.price.toNumber() : 0,
        wholesalePrice: product.wholesalePrice
          ? product.wholesalePrice.toNumber()
          : 0,
        category: {
          name: product.category?.name || "Tidak ada kategori",
        },
        tags: product.tags || [], // Use actual tags from database
        variants: product.variants || [],
        stockStatus: stockStatus,
        // Keep legacy fields for backward compatibility
        sold: totalSold,
        revenue: totalRevenue,
        profit: totalProfit, // This depends on having a 'cost' field on the Product model
      };
    });

    return {
      success: true,
      data: productData,
    };
  } catch (error) {
    console.error("Error fetching product report data:", error);
    return {
      error: "Gagal mengambil data laporan produk.",
    };
  }
};

// Enhanced function to get product report data with advanced filters
export const getProductReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause for products
    const whereClause: any = {
      userId: effectiveUserId,
    };

    // Add category filter if provided
    if (filters.category) {
      whereClause.category = {
        name: {
          contains: filters.category,
          mode: "insensitive",
        },
      };
    }

    const products = await db.product.findMany({
      where: whereClause,
      include: {
        category: {
          select: { name: true },
        },
        variants: {
          select: {
            colorName: true,
          },
        },
        saleItems: {
          where: {
            sale: {
              saleDate: {
                gte: startDate,
                lte: endDate,
              },
            },
          },
          select: {
            quantity: true,
            priceAtSale: true,
          },
        },
      },
      orderBy: {
        id: "asc", // Sort by product ID in ascending order (000001, 000002, etc.)
      },
    });

    const productData = products.map((product) => {
      const totalSold = product.saleItems.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );
      const totalRevenue = product.saleItems.reduce(
        (sum: number, item: { quantity: number; priceAtSale: any }) =>
          sum + item.quantity * item.priceAtSale.toNumber(),
        0
      );
      const totalCost = product.cost ? product.cost.toNumber() * totalSold : 0;
      const totalProfit = totalRevenue - totalCost;

      // Determine stock status
      const stockStatus =
        product.stock <= 0
          ? "Habis"
          : product.stock <= 10
            ? "Stok Rendah"
            : "Tersedia";

      return {
        id: product.id,
        name: product.name,
        description: product.description || "", // Add description field
        sku: product.sku || "-",
        barcode: product.barcode || "-", // Use actual barcode from database
        unit: product.unit || "pcs",
        stock: product.stock,
        cost: product.cost ? product.cost.toNumber() : 0,
        price: product.price ? product.price.toNumber() : 0,
        wholesalePrice: product.wholesalePrice
          ? product.wholesalePrice.toNumber()
          : 0,
        category: {
          name: product.category?.name || "Tidak ada kategori",
        },
        tags: product.tags || [], // Use actual tags from database
        variants: product.variants || [],
        stockStatus: stockStatus,
        // Keep legacy fields for backward compatibility
        sold: totalSold,
        revenue: totalRevenue,
        profit: totalProfit,
      };
    });

    return {
      success: true,
      data: productData,
    };
  } catch (error) {
    console.error("Error fetching product report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan produk.",
    };
  }
};
