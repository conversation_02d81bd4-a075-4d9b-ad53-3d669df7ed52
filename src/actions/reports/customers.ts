"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";

// Function to get customer report data
export const getCustomerReportData = async () => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const customers = await db.customer.findMany({
      where: {
        userId: effectiveUserId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const customerData = customers.map((customer) => ({
      id: customer.id,
      name: customer.name,
      contactName: customer.contactName || "-",
      email: customer.email || "-",
      phone: customer.phone || "-",
      address: customer.address || "-",
      NIK: customer.NIK || "-",
      NPWP: customer.NPWP || "-",
      notes: customer.notes || "-",
      createdAt: customer.createdAt.toISOString(),
      updatedAt: customer.updatedAt.toISOString(),
    }));

    return {
      success: true,
      data: customerData,
    };
  } catch (error) {
    console.error("Error fetching customer report data:", error);
    return {
      error: "Gagal mengambil data laporan pelanggan.",
    };
  }
};
