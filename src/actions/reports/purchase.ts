"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { format, startOfDay, endOfDay } from "date-fns";
import { AdvancedFilters, getDateRange } from "./_utils";

// Function to get purchase report data
export const getPurchaseReportData = async (dateRange: string) => {
  try {
    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch purchases within the date range
    const purchases = await db.purchase.findMany({
      where: {
        userId: effectiveUserId,
        purchaseDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
          },
        },
      },
      orderBy: {
        purchaseDate: "desc",
      },
    });

    // Transform the data to match the expected format
    const purchaseData = purchases.map((purchase) => {
      // Count total items
      const totalItems = purchase.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );

      return {
        id: purchase.transactionNumber || purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(),
        invoiceRef: purchase.invoiceRef || "-",
        paymentDueDate: purchase.paymentDueDate
          ? purchase.paymentDueDate.toISOString()
          : "-",
        supplier: {
          name: purchase.supplier?.name || "Tidak ada supplier",
        },
        totalAmount: purchase.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: purchase.purchaseDate.toISOString(),
        total: purchase.totalAmount.toNumber(),
        transactionNumber: purchase.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: purchaseData,
    };
  } catch (error) {
    console.error("Error fetching purchase report data:", error);
    return {
      error: "Gagal mengambil data laporan pembelian.",
    };
  }
};

// Function to get purchase chart data
export const getPurchaseChartData = async (dateRange: string) => {
  try {
    // Get effective user ID (owner ID if employee, user's own ID otherwise)
    const effectiveUserId = await getEffectiveUserId();

    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    const { startDate, endDate } = getDateRange(dateRange);

    // Fetch purchases within the date range
    const purchases = await db.purchase.findMany({
      where: {
        userId: effectiveUserId,
        purchaseDate: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
      },
      orderBy: {
        purchaseDate: "asc",
      },
    });

    // Group purchases by month for trend chart
    const monthlyData = new Map<string, number>();
    purchases.forEach((purchase) => {
      const month = format(purchase.purchaseDate, "MMM");
      const currentTotal = monthlyData.get(month) || 0;
      monthlyData.set(month, currentTotal + purchase.totalAmount.toNumber());
    });

    const trendData = Array.from(monthlyData.entries()).map(
      ([name, total]) => ({
        name,
        total,
      })
    );

    // Group purchases by supplier for pie chart
    const supplierData = new Map<string, number>();
    purchases.forEach((purchase) => {
      const supplierName = purchase.supplier?.name || "Tidak ada supplier";
      const currentTotal = supplierData.get(supplierName) || 0;
      supplierData.set(
        supplierName,
        currentTotal + purchase.totalAmount.toNumber()
      );
    });

    const supplierChartData = Array.from(supplierData.entries()).map(
      ([name, value]) => ({
        name,
        value,
      })
    );

    return {
      success: true,
      data: {
        trendData,
        supplierData: supplierChartData,
      },
    };
  } catch (error) {
    console.error("Error fetching purchase chart data:", error);
    return {
      error: "Gagal mengambil data grafik pembelian.",
    };
  }
};

// Enhanced function to get purchase report data with advanced filters
export const getPurchaseReportDataWithFilters = async (
  filters: AdvancedFilters
) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Build where clause with advanced filters
    const whereClause: any = {
      userId: effectiveUserId,
      purchaseDate: {
        gte: startDate,
        lte: endDate,
      },
    };

    console.log("Advanced Filters:", filters);

    // Add supplier filter if provided
    if (filters.supplier) {
      whereClause.supplier = {
        name: {
          contains: filters.supplier,
          mode: "insensitive",
        },
      };
    }

    // Add status filter if provided (assuming you have a status field)
    if (filters.status) {
      whereClause.status = filters.status;
    }

    console.log("Where Clause before DB query:", whereClause);

    const purchases = await db.purchase.findMany({
      where: whereClause,
      include: {
        supplier: {
          select: {
            name: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
                category: {
                  select: {
                    name: true,
                  },
                },
              },
            },
          },
        },
      },
      orderBy: {
        purchaseDate: "desc",
      },
    });

    // Filter by category if provided (filter at application level since it's nested)
    let filteredPurchases = purchases;
    if (filters.category) {
      filteredPurchases = purchases.filter((purchase) =>
        purchase.items.some((item) =>
          item.product.category?.name
            ?.toLowerCase()
            .includes(filters.category!.toLowerCase())
        )
      );
      console.log(
        "Filtered Purchases (after category filter):",
        filteredPurchases.length
      );
    } else {
      console.log(
        "No category filter applied. Total purchases:",
        filteredPurchases.length
      );
    }

    const purchaseData = filteredPurchases.map((purchase) => {
      const totalItems = purchase.items.reduce(
        (sum: number, item: { quantity: number }) => sum + item.quantity,
        0
      );

      return {
        id: purchase.transactionNumber || purchase.id,
        purchaseDate: purchase.purchaseDate.toISOString(),
        invoiceRef: purchase.invoiceRef || "-",
        paymentDueDate: purchase.paymentDueDate
          ? purchase.paymentDueDate.toISOString()
          : "-",
        supplier: {
          name: purchase.supplier?.name || "Tidak ada supplier",
        },
        totalAmount: purchase.totalAmount.toNumber(),
        items: {
          length: totalItems,
        },
        tags: [], // Add empty tags array for consistency
        // Keep legacy fields for backward compatibility
        date: purchase.purchaseDate.toISOString(),
        total: purchase.totalAmount.toNumber(),
        transactionNumber: purchase.transactionNumber || null,
      };
    });

    return {
      success: true,
      data: purchaseData,
    };
  } catch (error) {
    console.error("Error fetching purchase report data with filters:", error);
    return {
      error: "Gagal mengambil data laporan pembelian.",
    };
  }
};
