import { startOfDay, endOfDay, subDays } from "date-fns";

// Interface for advanced filters
export interface AdvancedFilters {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

// Helper function to get date range based on selection
export const getDateRange = (dateRange: string) => {
  const now = new Date();
  let startDate: Date;
  let endDate: Date = endOfDay(now);

  switch (dateRange) {
    case "today":
      startDate = startOfDay(now);
      break;
    case "7d":
      startDate = startOfDay(subDays(now, 7));
      break;
    case "30d":
      startDate = startOfDay(subDays(now, 30));
      break;
    case "month":
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      break;
    case "year":
      startDate = new Date(now.getFullYear(), 0, 1);
      break;
    default:
      // Default to 30 days
      startDate = startOfDay(subDays(now, 30));
  }

  return { startDate, endDate };
};
