"use server";

import { db } from "@/lib/prisma";
import { getEffectiveUserId } from "@/lib/get-effective-user-id";
import { AdvancedFilters, getDateRange } from "./_utils";
import { startOfDay, endOfDay } from "date-fns";

// Function to get income statement data with date filtering
export const getIncomeStatementData = async (filters: AdvancedFilters) => {
  try {
    const effectiveUserId = await getEffectiveUserId();
    if (!effectiveUserId) {
      return { error: "Tidak terautentikasi!" };
    }

    // Use custom date range if provided, otherwise use dateRange
    let startDate: Date, endDate: Date;
    if (filters.startDate && filters.endDate) {
      startDate = startOfDay(filters.startDate);
      endDate = endOfDay(filters.endDate);
    } else {
      const dateRange = getDateRange(filters.dateRange);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    // Get sales data (Revenue)
    const sales = await db.sale.findMany({
      where: {
        userId: effectiveUserId,
        saleDate: {
          gte: startDate,
          lte: endDate,
        },
        isDraft: false,
      },
      include: {
        items: {
          include: {
            product: {
              select: {
                name: true,
                cost: true, // Include cost for COGS calculation
              },
            },
          },
        },
      },
    });

    // Get purchase data (Cost of Goods Sold)
    const purchases = await db.purchase.findMany({
      where: {
        userId: effectiveUserId,
        purchaseDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get service data (Service Revenue)
    const services = await db.service.findMany({
      where: {
        userId: effectiveUserId,
        receivedDate: {
          gte: startDate,
          lte: endDate,
        },
        isDraft: false,
      },
    });

    // Calculate Revenue
    const salesRevenue = sales.reduce(
      (sum, sale) => sum + sale.totalAmount.toNumber(),
      0
    );
    const serviceRevenue = services.reduce((sum, service) => {
      return (
        sum +
        (service.finalCost
          ? service.finalCost.toNumber()
          : service.estimatedCost?.toNumber() || 0)
      );
    }, 0);
    const totalRevenue = salesRevenue + serviceRevenue;

    // Calculate Cost of Goods Sold (COGS)
    const directCosts = purchases.reduce(
      (sum, purchase) => sum + purchase.totalAmount.toNumber(),
      0
    );

    // Calculate cost of goods sold from sales items
    const salesCOGS = sales.reduce((sum, sale) => {
      return (
        sum +
        sale.items.reduce((itemSum, item) => {
          const costPrice = item.product?.cost?.toNumber() || 0;
          return itemSum + costPrice * item.quantity;
        }, 0)
      );
    }, 0);

    const totalCOGS = directCosts + salesCOGS;

    // Calculate Gross Profit
    const grossProfit = totalRevenue - totalCOGS;

    // Operating Expenses (simplified - using a percentage of purchases for demo)
    const operatingExpenses = directCosts * 0.15; // 15% of purchases as operating expenses
    const marketingExpenses = directCosts * 0.05; // 5% as marketing
    const administrativeExpenses = directCosts * 0.08; // 8% as administrative
    const totalOperatingExpenses =
      operatingExpenses + marketingExpenses + administrativeExpenses;

    // Net Income
    const netIncome = grossProfit - totalOperatingExpenses;

    // Prepare income statement data
    const incomeStatementData = [
      // Revenue Section
      {
        category: "PENDAPATAN",
        item: "Penjualan Produk",
        amount: salesRevenue,
      },
      {
        category: "PENDAPATAN",
        item: "Pendapatan Jasa",
        amount: serviceRevenue,
      },
      {
        category: "PENDAPATAN",
        item: "Total Pendapatan",
        amount: totalRevenue,
        isSubTotal: true,
      },

      // Cost of Goods Sold Section
      {
        category: "HARGA POKOK PENJUALAN",
        item: "Pembelian Langsung",
        amount: directCosts,
      },
      {
        category: "HARGA POKOK PENJUALAN",
        item: "Harga Pokok Produk Terjual",
        amount: salesCOGS,
      },
      {
        category: "HARGA POKOK PENJUALAN",
        item: "Total Harga Pokok Penjualan",
        amount: totalCOGS,
        isSubTotal: true,
      },

      // Gross Profit
      {
        category: "LABA KOTOR",
        item: "Laba Kotor",
        amount: grossProfit,
        isTotal: true,
      },

      // Operating Expenses Section
      {
        category: "BEBAN OPERASIONAL",
        item: "Beban Operasional",
        amount: operatingExpenses,
      },
      {
        category: "BEBAN OPERASIONAL",
        item: "Beban Pemasaran",
        amount: marketingExpenses,
      },
      {
        category: "BEBAN OPERASIONAL",
        item: "Beban Administrasi",
        amount: administrativeExpenses,
      },
      {
        category: "BEBAN OPERASIONAL",
        item: "Total Beban Operasional",
        amount: totalOperatingExpenses,
        isSubTotal: true,
      },

      // Net Income
      {
        category: "LABA BERSIH",
        item: "Laba Bersih",
        amount: netIncome,
        isTotal: true,
      },
    ];

    return {
      success: true,
      data: incomeStatementData,
      summary: {
        totalRevenue,
        totalCOGS,
        grossProfit,
        totalOperatingExpenses,
        netIncome,
        period: `${startDate.toLocaleDateString("id-ID")} - ${endDate.toLocaleDateString("id-ID")}`,
      },
    };
  } catch (error) {
    console.error("Error fetching income statement data:", error);
    return {
      error: "Gagal mengambil data laporan laba rugi.",
    };
  }
};
