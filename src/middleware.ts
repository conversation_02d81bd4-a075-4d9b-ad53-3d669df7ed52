import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export default function middleware(req: NextRequest) {
  const hostname = req.headers.get("host") || "";
  const pathname = req.nextUrl.pathname;

  // Skip static files, API routes, and other excluded paths
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") ||
    pathname.startsWith("/favicon")
  ) {
    return NextResponse.next();
  }

  // Skip auth paths
  if (
    pathname.startsWith("/login") ||
    pathname.startsWith("/register") ||
    pathname.startsWith("/auth")
  ) {
    return NextResponse.next();
  }

  const isDashboardSubdomain =
    hostname === "dashboard.kivapos.com" ||
    hostname === "dashboard.localhost:3000" ||
    hostname === "dashboard.localhost";

  const isMainDomain =
    hostname === "kivapos.com" ||
    hostname === "localhost:3000" ||
    hostname === "localhost";

  // Handle dashboard subdomain requests
  if (isDashboardSubdomain) {
    // Redirect dashboard.kivapos.com/dashboard/* to dashboard.kivapos.com/*
    if (pathname.startsWith("/dashboard")) {
      const cleanPath = pathname.replace("/dashboard", "") || "/summaries";
      const protocol = hostname.includes("localhost") ? "http" : "https";
      const redirectUrl = `${protocol}://${hostname}${cleanPath}`;
      console.log(
        `[Middleware] Dashboard redirect: ${pathname} -> ${redirectUrl}`
      );
      return NextResponse.redirect(redirectUrl, 301);
    }

    // Rewrite paths to internal /dashboard/* structure
    let dashboardPath: string;
    if (pathname === "/") {
      dashboardPath = "/dashboard/summaries";
    } else {
      dashboardPath = `/dashboard${pathname}`;
    }

    console.log(
      `[Middleware] Dashboard rewrite: ${pathname} -> ${dashboardPath}`
    );

    const url = req.nextUrl.clone();
    url.pathname = dashboardPath;
    return NextResponse.rewrite(url);
  }

  // Handle main domain dashboard redirects to subdomain
  if (isMainDomain && pathname.startsWith("/dashboard")) {
    // Extract the route after /dashboard
    const dashboardRoute =
      pathname === "/dashboard"
        ? "/summaries"
        : pathname.replace("/dashboard", "") || "/summaries";

    // Determine protocol and dashboard host
    const protocol = hostname.includes("localhost") ? "http" : "https";
    const dashboardHost = hostname.includes("localhost")
      ? "dashboard.localhost:3000"
      : "dashboard.kivapos.com";

    const redirectUrl = `${protocol}://${dashboardHost}${dashboardRoute}`;

    console.log(
      `[Middleware] Main domain redirect: ${pathname} -> ${redirectUrl}`
    );
    return NextResponse.redirect(redirectUrl, 301);
  }

  // All other requests continue normally
  return NextResponse.next();
}

export const config = {
  matcher: ["/((?!_next/static|_next/image|favicon.ico).*)"],
};
