# Application URL --- Used for email verification and password reset links
NEXT_PUBLIC_APP_URL=

# URL For Redirect Middleware
NEXT_PUBLIC_MAIN_DOMAIN=
NEXT_PUBLIC_DASHBOARD_SUBDOMAIN=

# For: After Google login will go website instead to localhost
NEXTAUTH_URL=

# Auth Secret
AUTH_SECRET=
AUTH_TRUST_HOST=

# Database URL
DATABASE_URL=

# Authentication (Required) secure by random string for JWT signing
# To generate go to wsl -> openssl rand -hex 64
JWT_SECRET=

# Email Service (Required)
RESEND_API_KEY=

# OAuth Providers
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=

# Vercel Blob Storage
BLOB_READ_WRITE_TOKEN=

# Midtrans Payment Gateway
MIDTRANS_SERVER_KEY=
MIDTRANS_CLIENT_KEY=
NEXT_PUBLIC_MIDTRANS_CLIENT_KEY=
MIDTRANS_IS_PRODUCTION=
MIDTRANS_WEBHOOK_URL=