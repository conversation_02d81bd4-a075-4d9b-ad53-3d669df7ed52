#!/usr/bin/env bun

/**
 * Midtrans Integration Test Script
 *
 * This script tests the Midtrans integration by creating a test transaction
 * and checking its status. Run this script to verify your Midtrans configuration.
 *
 * Usage: bun run scripts/test-midtrans.js
 */

require("dotenv").config();

const midtransClient = require("midtrans-client");

// Configuration
const config = {
  isProduction: process.env.MIDTRANS_IS_PRODUCTION === "true",
  serverKey: process.env.MIDTRANS_SERVER_KEY,
  clientKey: process.env.MIDTRANS_CLIENT_KEY,
};

console.log("🧪 [TEST] Starting Midtrans integration test...");
console.log("📋 [TEST] Configuration:", {
  isProduction: config.isProduction,
  hasServerKey: !!config.serverKey,
  hasClientKey: !!config.clientKey,
  environment: config.isProduction ? "PRODUCTION" : "SANDBOX",
});

if (!config.serverKey || !config.clientKey) {
  console.error("❌ [TEST] Missing required environment variables:");
  console.error("   - MIDTRANS_SERVER_KEY:", !!config.serverKey);
  console.error("   - MIDTRANS_CLIENT_KEY:", !!config.clientKey);
  console.error(
    "   - MIDTRANS_IS_PRODUCTION:",
    process.env.MIDTRANS_IS_PRODUCTION
  );
  process.exit(1);
}

// Initialize Midtrans clients
const snap = new midtransClient.Snap(config);
const coreApi = new midtransClient.CoreApi(config);

async function testCreateTransaction() {
  console.log("\n🚀 [TEST] Testing transaction creation...");

  const orderId = `test-${Date.now()}`;
  const parameter = {
    transaction_details: {
      order_id: orderId,
      gross_amount: 100000, // IDR 100,000
    },
    item_details: [
      {
        id: "test-subscription",
        name: "Test Subscription Plan",
        price: 100000,
        quantity: 1,
      },
    ],
    customer_details: {
      first_name: "Test",
      last_name: "User",
      email: "<EMAIL>",
      phone: "+628123456789",
    },
    credit_card: {
      secure: true,
    },
  };

  try {
    const transaction = await snap.createTransaction(parameter);

    console.log("✅ [TEST] Transaction created successfully:");
    console.log("   - Order ID:", orderId);
    console.log("   - Token:", transaction.token ? "Generated" : "Missing");
    console.log(
      "   - Redirect URL:",
      transaction.redirect_url ? "Generated" : "Missing"
    );

    return { orderId, transaction };
  } catch (error) {
    console.error("❌ [TEST] Failed to create transaction:");
    console.error("   - Error:", error.message);
    console.error("   - Details:", error.response?.data || error);
    throw error;
  }
}

async function testGetTransactionStatus(orderId) {
  console.log("\n🔍 [TEST] Testing transaction status check...");

  try {
    const statusResponse = await coreApi.transaction.status(orderId);

    console.log("✅ [TEST] Transaction status retrieved:");
    console.log("   - Order ID:", statusResponse.order_id);
    console.log("   - Transaction Status:", statusResponse.transaction_status);
    console.log("   - Payment Type:", statusResponse.payment_type);
    console.log("   - Status Code:", statusResponse.status_code);

    return statusResponse;
  } catch (error) {
    console.error("❌ [TEST] Failed to get transaction status:");
    console.error("   - Error:", error.message);
    console.error("   - Details:", error.response?.data || error);
    throw error;
  }
}

function testSignatureVerification() {
  console.log("\n🔐 [TEST] Testing signature verification...");

  const crypto = require("crypto");
  const testData = {
    order_id: "test-12345",
    status_code: "200",
    gross_amount: "100000.00",
  };

  try {
    // Create signature string: order_id + status_code + gross_amount + server_key
    const signatureString =
      testData.order_id +
      testData.status_code +
      testData.gross_amount +
      config.serverKey;

    // Generate SHA512 hash
    const calculatedSignature = crypto
      .createHash("sha512")
      .update(signatureString)
      .digest("hex");

    console.log("✅ [TEST] Signature verification test:");
    console.log("   - Signature String Components:", {
      order_id: testData.order_id,
      status_code: testData.status_code,
      gross_amount: testData.gross_amount,
      server_key: "***masked***",
    });
    console.log(
      "   - Calculated Signature:",
      calculatedSignature.substring(0, 20) + "..."
    );

    return calculatedSignature;
  } catch (error) {
    console.error("❌ [TEST] Signature verification failed:");
    console.error("   - Error:", error.message);
    throw error;
  }
}

async function runTests() {
  try {
    console.log("🧪 [TEST] Running Midtrans integration tests...\n");

    // Test 1: Signature verification
    testSignatureVerification();

    // Test 2: Create transaction
    const { orderId, transaction } = await testCreateTransaction();

    // Test 3: Get transaction status (will be pending for new transaction)
    await testGetTransactionStatus(orderId);

    console.log("\n✅ [TEST] All tests completed successfully!");
    console.log("\n📋 [TEST] Next steps:");
    console.log("   1. Test the payment flow in your application");
    console.log("   2. Configure webhook URL in Midtrans Dashboard");
    console.log("   3. Test webhook delivery using the simulator");
    console.log("   4. Monitor console logs during payment testing");

    if (!config.isProduction) {
      console.log("\n🔗 [TEST] Useful sandbox links:");
      console.log("   - Dashboard: https://dashboard.sandbox.midtrans.com");
      console.log("   - Simulator: https://simulator.sandbox.midtrans.com");
      console.log("   - Test Payment URL:", transaction.redirect_url);
    }
  } catch (error) {
    console.error("\n❌ [TEST] Test suite failed:");
    console.error("   - Error:", error.message);
    console.error("\n🔧 [TEST] Troubleshooting:");
    console.error("   1. Verify your Midtrans credentials in .env file");
    console.error(
      "   2. Check if you're using the correct environment (sandbox/production)"
    );
    console.error("   3. Ensure your server key and client key match");
    console.error("   4. Check Midtrans dashboard for any account issues");

    process.exit(1);
  }
}

// Run the tests
runTests();
