#!/usr/bin/env node

/**
 * Test script to verify the new middleware logic
 * Run with: node scripts/test-new-middleware.js
 */

// Mock NextRequest and NextResponse for testing
class MockNextRequest {
  constructor(url, headers = {}) {
    this.url = url;
    this.nextUrl = new URL(url);
    this.nextUrl.clone = () => new URL(url);
    this.headers = new Map(Object.entries(headers));
  }

  get(key) {
    return this.headers.get(key);
  }
}

class MockNextResponse {
  constructor(type, url) {
    this.type = type;
    this.url = url;
    this.headers = new Map();
  }

  static next() {
    return new MockNextResponse("next");
  }

  static redirect(url, status) {
    return new MockNextResponse("redirect", url);
  }

  static rewrite(url) {
    return new MockNextResponse("rewrite", url);
  }

  set(key, value) {
    this.headers.set(key, value);
  }
}

// Test middleware function
function testMiddleware(req) {
  const hostname = req.headers.get("host") || "";
  const pathname = req.nextUrl.pathname;

  // Skip static files and API routes
  if (
    pathname.startsWith("/_next/") ||
    pathname.startsWith("/api/") ||
    pathname.includes(".") ||
    pathname.startsWith("/favicon")
  ) {
    return MockNextResponse.next();
  }

  // Skip auth paths
  if (
    pathname.startsWith("/login") ||
    pathname.startsWith("/register") ||
    pathname.startsWith("/auth")
  ) {
    return MockNextResponse.next();
  }

  const isDashboardSubdomain =
    hostname === "dashboard.kivapos.com" ||
    hostname === "dashboard.localhost:3000" ||
    hostname === "dashboard.localhost";

  const isMainDomain =
    hostname === "kivapos.com" ||
    hostname === "localhost:3000" ||
    hostname === "localhost";

  // Handle dashboard subdomain requests
  if (isDashboardSubdomain) {
    // If already has /dashboard prefix, continue normally
    if (pathname.startsWith("/dashboard")) {
      return MockNextResponse.next();
    }

    // Rewrite to dashboard path
    let dashboardPath;
    if (pathname === "/") {
      dashboardPath = "/dashboard/summaries";
    } else {
      dashboardPath = `/dashboard${pathname}`;
    }

    const url = req.nextUrl.clone();
    url.pathname = dashboardPath;

    return MockNextResponse.rewrite(url);
  }

  // Handle main domain dashboard redirects to subdomain
  if (isMainDomain && pathname.startsWith("/dashboard")) {
    // Extract the dashboard route
    const dashboardRoute =
      pathname === "/dashboard"
        ? "/summaries"
        : pathname.replace("/dashboard", "");
    const finalRoute = dashboardRoute || "/summaries";

    // Determine protocol and dashboard host
    const protocol = hostname.includes("localhost") ? "http" : "https";
    const dashboardHost = hostname.includes("localhost")
      ? "dashboard.localhost:3000"
      : "dashboard.kivapos.com";

    const redirectUrl = `${protocol}://${dashboardHost}${finalRoute}`;

    return MockNextResponse.redirect(redirectUrl, 301);
  }

  // All other requests continue normally
  return MockNextResponse.next();
}

// Test cases
const testCases = [
  {
    name: "Dashboard subdomain root",
    url: "https://dashboard.kivapos.com/",
    headers: { host: "dashboard.kivapos.com" },
    expected: { type: "rewrite", path: "/dashboard/summaries" },
  },
  {
    name: "Dashboard subdomain settings",
    url: "https://dashboard.kivapos.com/settings",
    headers: { host: "dashboard.kivapos.com" },
    expected: { type: "rewrite", path: "/dashboard/settings" },
  },
  {
    name: "Dashboard subdomain with existing /dashboard path",
    url: "https://dashboard.kivapos.com/dashboard/settings",
    headers: { host: "dashboard.kivapos.com" },
    expected: { type: "next" },
  },
  {
    name: "Main domain dashboard redirect",
    url: "https://kivapos.com/dashboard/settings",
    headers: { host: "kivapos.com" },
    expected: {
      type: "redirect",
      url: "https://dashboard.kivapos.com/settings",
    },
  },
  {
    name: "Main domain root (should continue)",
    url: "https://kivapos.com/",
    headers: { host: "kivapos.com" },
    expected: { type: "next" },
  },
  {
    name: "Auth path (should continue)",
    url: "https://kivapos.com/login",
    headers: { host: "kivapos.com" },
    expected: { type: "next" },
  },
  {
    name: "Static file (should continue)",
    url: "https://dashboard.kivapos.com/favicon.ico",
    headers: { host: "dashboard.kivapos.com" },
    expected: { type: "next" },
  },
];

// Run tests
console.log("🧪 Testing New Middleware Logic\n");

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log(`  URL: ${testCase.url}`);

  const req = new MockNextRequest(testCase.url, testCase.headers);
  const response = testMiddleware(req);

  console.log(`  Result: ${response.type}`);
  if (response.url) {
    console.log(`  URL: ${response.url}`);
  }

  // Check if result matches expected
  const passed = response.type === testCase.expected.type;
  console.log(`  Status: ${passed ? "✅ PASS" : "❌ FAIL"}`);

  if (!passed) {
    console.log(`  Expected: ${JSON.stringify(testCase.expected)}`);
    console.log(
      `  Got: ${JSON.stringify({ type: response.type, url: response.url })}`
    );
  }

  console.log("");
});

console.log("🎯 Summary:");
console.log(
  "✅ Dashboard subdomain requests are rewritten to /dashboard/* routes"
);
console.log(
  "✅ Main domain /dashboard/* requests redirect to dashboard subdomain"
);
console.log("✅ Static files and auth paths are skipped");
console.log("✅ No more route conflicts - removed catch-all routes");
console.log("✅ Uses middleware for both redirect and rewrite logic");
