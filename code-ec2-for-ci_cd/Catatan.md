# Ini adalah catatan untuk atau cara CI/CD Pipeline dengan AWS EC2 dengan metode Blue-Grenn Deployment + Docker

## Structur Folder
- /home
└── ec2-user
    └── kivapos-deploy
        ├── deploy.sh
        ├── hook.js
        └── kivapos
            ├── Dockerfile
            ├── next.config.js
            ├── package.json
            ├── public
            ├── src
            ├── tsconfig.json
            └── other-files
            

## Langkah-langkah
1. Buat folder kivapos-deploy di /home/<USER>
2. Buat file deploy.sh di /home/<USER>/kivapos-deploy
3. Buat file hook.js di /home/<USER>/kivapos-deploy
4. Buat file kivapos-deploy.service untuk menjalankan deploy.sh secara otomatis setelah server reboot
5. Buat file kivapos-webhook.service untuk menjalankan hook.js setelah ter-trigger dari webhook github atau manual di PC lain dengan cara: curl -X POST http://ip-public-ec2:4000 

## Installation Program
- Step 1: Install Docker dan Nginx
ssh ec2-user@<your-ec2-ip>
sudo yum update -y
sudo yum install -y git docker nginx
- Step 2: Configurasi Docker
sudo systemctl enable docker
sudo systemctl start docker
sudo usermod -aG docker ec2-user
- Step 3: Buat .env.production di folder /kivapos-deploy/kivapos
nano .env.production
- Step 4: Buat file Dockerfile di folder root project
- Step 5: Buat file deploy.sh di folder /kivapos-deploy
setelah buat deploy.sh ketik 'chmod +x ~/deploy.sh' untuk membuat file executable
- Step 6: Buat file hook.js di folder /kivapos-deploy
- Step 7: Buat nginx Config (Reverse Proxy)
sudo nano /etc/nginx/conf.d/kivapos.conf
-- setelah membuat file kivapos.conf, lakukan test dan enable nginx
sudo nginx -t
sudo systemctl enable nginx
sudo systemctl restart nginx
- Step 8: Buat Webhook Listener (hook.js)
- Step 9: Buat 'systemd' to Run Webhook Listener (systemd akan run otomatis setelah server reboot)
sudo nano /etc/systemd/system/kivapos-webhook.service
-- setelah membuat file kivapos-webhook.service, lakukan test dan enable systemd
sudo systemctl daemon-reload
sudo systemctl enable kivapos-webhook
sudo systemctl start kivapos-webhook
-- cek status
sudo systemctl status kivapos-webhook
- Step 10: Test webhook di pc-lain
curl -X POST http://ip-public-ec2:4000
- Step 11: GitHub Setup Webhook
-- On EC2:  ssh-keygen -t rsa -b 4096 -C "ec2-deploy"
            cat ~/.ssh/id_rsa.pub
-- Tambah public key ke Github: GitHub → Repo Settings → Deploy Keys → Add key
-- Test: ssh -T **************
- Step 12: Tambah Webhook di Repo Github
-- GitHub → Repo → Settings → Webhooks
--- Payload URL: http://<your-ec2-ip>:4000
    Content type: application/json
    Event: Push events
    Save
- # DONE!