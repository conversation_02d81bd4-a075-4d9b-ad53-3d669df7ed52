#!/bin/bash

set -e

# === CONFIG ===
APP_NAME="kivapos"
BLUE_PORT=3000
GREEN_PORT=3001
NGINX_CONF_PATH="/etc/nginx/conf.d/kivapos.conf"
LOCK_FILE="/tmp/deploy.lock"
REPO_PATH="/home/<USER>/kivapos-deploy/kivapos"
LOG_FILE="/home/<USER>/kivapos-deploy/deploy.log"
DISCORD_WEBHOOK_URL="https://discord.com/api/webhooks/1401988706923057233/ct9EI4EBboRaYxdwAPSUIhGR0Xfn6aSzO1ISs2i1ogE8ptwNSgIOGGkTVSLTLqAyyiKM"
GITHUB_REPO_URL="https://github.com/YanuarArif/kivapos"

# === LOGGING ===
exec > >(tee -a "$LOG_FILE") 2>&1
echo "===== DEPLOY STARTED: $(date) ====="

# === DISCORD NOTIFIER ===
send_discord_message() {
  local MESSAGE="$1"
  curl -H "Content-Type: application/json" \
    -X POST \
    -d "{\"content\": \"$MESSAGE\"}" \
    "$DISCORD_WEBHOOK_URL"
}

# === DEPLOY LOCK ===
if [ -f "$LOCK_FILE" ]; then
  echo "❌ Deploy already in progress. Exiting."
  send_discord_message "❌ Deploy aborted: another deployment is already in progress on \`$(hostname)\`."
  exit 1
fi

touch "$LOCK_FILE"
trap "rm -f $LOCK_FILE" EXIT

# === FETCH LATEST CODE ===
cd "$REPO_PATH"
echo "📥 Fetching latest code from GitHub..."
git fetch origin

LOCAL_COMMIT=$(git rev-parse HEAD)
REMOTE_COMMIT=$(git rev-parse origin/main)

if [ "$LOCAL_COMMIT" == "$REMOTE_COMMIT" ]; then
  echo "⚠️ No new commits found. Rebuilding Docker image anyway."

  LATEST_COMMIT_MSG=$(git log -1 origin/main --pretty=format:"%s by %an")
  LATEST_COMMIT_HASH=$(git rev-parse --short=12 "$REMOTE_COMMIT")
  COMMIT_URL="${GITHUB_REPO_URL}/commit/${REMOTE_COMMIT}"

  send_discord_message "⚠️ No new commits. Rebuilding \`$APP_NAME\` anyway on \`$(hostname)\`."
  send_discord_message "🟢 Deploying \`$APP_NAME\` with commit [\`$LATEST_COMMIT_HASH\`]($COMMIT_URL):\n\`\`\`\n$LATEST_COMMIT_MSG\n\`\`\`"
else
  echo "🟢 New commits found:"
  NEW_COMMITS=$(git log --oneline "$LOCAL_COMMIT..origin/main")
  echo "$NEW_COMMITS"

  echo "➡️ Pulling changes..."
  git pull origin main

  NEW_HEAD=$(git rev-parse HEAD)
  LATEST_COMMIT_MSG=$(git log -1 --pretty=format:"%s by %an")
  LATEST_COMMIT_HASH=$(git rev-parse --short=12 "$NEW_HEAD")
  COMMIT_URL="${GITHUB_REPO_URL}/commit/${NEW_HEAD}"

  send_discord_message "🟢 Deploying \`$APP_NAME\` with new commit [\`$LATEST_COMMIT_HASH\`]($COMMIT_URL):\n\`\`\`\n$LATEST_COMMIT_MSG\n\`\`\`"
fi

# === DETERMINE ACTIVE COLOR ===
CURRENT_COLOR=$(cat /tmp/current_color 2>/dev/null || echo "green")
if [ "$CURRENT_COLOR" == "blue" ]; then
  NEW_COLOR="green"
  NEW_PORT=$GREEN_PORT
  OLD_COLOR="blue"
  OLD_PORT=$BLUE_PORT
else
  NEW_COLOR="blue"
  NEW_PORT=$BLUE_PORT
  OLD_COLOR="green"
  OLD_PORT=$GREEN_PORT
fi

# === BUILD AND RUN ===
echo "🛠️ Building new Docker image: ${APP_NAME}:${NEW_COLOR}"
docker build -t ${APP_NAME}:${NEW_COLOR} "$REPO_PATH"

echo "🧹 Cleaning up any stale container named ${APP_NAME}-${NEW_COLOR}"
docker rm -f ${APP_NAME}-${NEW_COLOR} 2>/dev/null || true

echo "🚀 Running new container on port ${NEW_PORT}"
docker run -d --rm \
  --name ${APP_NAME}-${NEW_COLOR} \
  -p ${NEW_PORT}:3000 \
  --env-file "${REPO_PATH}/.env" \
  ${APP_NAME}:${NEW_COLOR}

# === HEALTH CHECK ===
echo "🩺 Waiting for health check on port ${NEW_PORT}..."
for i in {1..30}; do
  if curl -fs http://localhost:${NEW_PORT}/api/healthz > /dev/null; then
    echo "✅ New version is healthy."
    break
  fi
  echo "⏳ Waiting... ($i)"
  sleep 2
done

if ! curl -fs http://localhost:${NEW_PORT}/api/healthz > /dev/null; then
  echo "❌ New version failed health check. Aborting deployment."
  send_discord_message "❌ Deployment failed: \`$APP_NAME\` failed health check on port \`${NEW_PORT}\`."
  docker stop ${APP_NAME}-${NEW_COLOR}
  exit 1
fi

# === SWITCH TRAFFIC ===
echo "🔁 Switching Nginx to port ${NEW_PORT}..."
sudo sed -i "s/server 127.0.0.1:[0-9]\+;/server 127.0.0.1:${NEW_PORT};/" $NGINX_CONF_PATH
sudo /usr/sbin/nginx -s reload

echo "$NEW_COLOR" > /tmp/current_color
echo "🟢 Switched traffic to ${NEW_COLOR} version."

# === CLEANUP OLD ===
echo "🧹 Stopping and cleaning up old container/image..."
docker stop ${APP_NAME}-${OLD_COLOR} || true
docker image rm ${APP_NAME}:${OLD_COLOR} || true
docker container prune -f
docker image prune -f

echo "✅ Deployment complete."
send_discord_message "✅ Deployment complete for \`$APP_NAME\`. Now serving on port \`${NEW_PORT}\` (\`${NEW_COLOR}\` version)."
