# ---------------------------
# Shared upstream for Next.js (served by <PERSON>un)
# ---------------------------
upstream kivapos_upstream {
    server 127.0.0.1:3001;
    keepalive 64;
}

# ---------------------------
# HTTP â†’ HTTPS Redirects
# ---------------------------

# Redirect root domain & www from HTTP to HTTPS
server {
    listen 80;
    server_name kivapos.com www.kivapos.com;
    return 301 https://kivapos.com$request_uri;
}

# Redirect dashboard subdomain from HTTP to HTTPS
server {
    listen 80;
    server_name dashboard.kivapos.com;
    return 301 https://dashboard.kivapos.com$request_uri;
}

# ---------------------------
# HTTPS: Root domain (kivapos.com)
# ---------------------------
server {
    listen 443 ssl;
    http2 on;
    server_name kivapos.com;

    location / {
        proxy_pass http://kivapos_upstream;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;

        # WebSocket support
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    ssl_certificate /etc/letsencrypt/live/kivapos.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/kivapos.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}

# ---------------------------
# HTTPS: Redirect www.kivapos.com â†’ kivapos.com
# ---------------------------
server {
    listen 443 ssl;
    http2 on;
    server_name www.kivapos.com;
    return 301 https://kivapos.com$request_uri;

    ssl_certificate /etc/letsencrypt/live/kivapos.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/kivapos.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}

# ---------------------------
# HTTPS: Subdomain (dashboard.kivapos.com)
# ---------------------------
server {
    listen 443 ssl;
    http2 on;
    server_name dashboard.kivapos.com;

    location / {
        proxy_pass http://kivapos_upstream;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Connection '';
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300;

        # WebSocket support
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # Security headers
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy strict-origin-when-cross-origin;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

    ssl_certificate /etc/letsencrypt/live/kivapos.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/kivapos.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}
