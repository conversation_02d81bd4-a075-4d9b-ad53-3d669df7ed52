# Subscription Plan Management System

This document describes the comprehensive subscription plan management system implemented for the Kasir Online application.

## Overview

The subscription system enforces limits based on three subscription plans with a 30-day trial system:

- **Paket Dasar (Basic Plan)**: Small business features with 30-day free trial for new users
- **Paket Pro (Pro Plan)**: Advanced features for growing businesses
- **Paket Enterprise (Enterprise Plan)**: Unlimited features for large enterprises

**Note**: The FREE plan has been removed. All new users start with a 30-day trial of the BASIC plan.

## Plan Details

### Trial System

- **Duration**: 30 days from registration
- **Features**: Full access to BASIC plan features during trial
- **Automatic**: All new users automatically get trial access
- **Expiration**: After trial ends, users must subscribe to continue

### Paket Dasar (Basic Plan)

- **Products**: Maximum 200
- **Transactions**: Maximum 1,000 per month
- **Users**: 10 (owner + 9 employees)
- **Contacts**: 100 (suppliers + customers combined)
- **Notifications**: Website notifications only
- **Backup**: Manual monthly backup

### Paket Pro (Pro Plan)

- **Products**: Maximum 500
- **Transactions**: Maximum 10,000 per month
- **Users**: 100 (owner + 99 employees)
- **Contacts**: 1,000 (suppliers + customers combined)
- **Notifications**: Website + email notifications
- **Backup**: Manual backup (daily, monthly, yearly)

### Paket Enterprise (Enterprise Plan)

- **Products**: Unlimited
- **Transactions**: Unlimited
- **Users**: Unlimited
- **Contacts**: Unlimited
- **Notifications**: Website + email notifications
- **Backup**: Automatic backup (daily, monthly, yearly)
- **Support**: Priority developer support

## Implementation

### Core Files

1. **`src/lib/subscription.ts`**: Centralized plan configuration
2. **`src/lib/subscription-limits.ts`**: Utility functions for checking limits
3. **`src/lib/api-subscription-middleware.ts`**: API middleware for backend validation
4. **`src/components/subscription/usage-summary.tsx`**: React component for usage display

### Limit Enforcement

#### Frontend (UI Prevention)

- Product creation forms check limits before allowing submission
- Transaction forms validate monthly limits
- Employee creation forms check user limits
- Contact forms validate contact limits
- Notifications page shows access denied for free users

#### Backend (API Validation)

- All creation actions check limits before database operations
- API routes use middleware to enforce subscription limits
- Server-side validation prevents bypassing frontend restrictions

### Modified Files

#### Actions (Server-side validation added)

- `src/actions/entities/products.ts`: Product creation limits
- `src/actions/entities/purchases.ts`: Purchase transaction limits
- `src/actions/entities/sales.ts`: Sales transaction limits
- `src/actions/entities/services.ts`: Service transaction limits
- `src/actions/entities/employee.ts`: Employee creation limits
- `src/actions/entities/suppliers.ts`: Supplier contact limits
- `src/actions/entities/customers.ts`: Customer contact limits

#### Pages

- `src/app/(protected)/dashboard/notifications/page.tsx`: Access restriction for free users
- `src/components/pages/dashboard/settings/plans/plans-settings.tsx`: Updated plan display

#### API Routes

- `src/app/api/notifications/route.ts`: Notifications with subscription check
- `src/app/api/subscription/usage/route.ts`: Usage summary endpoint

## Usage

### Checking Limits in Code

```typescript
import { canCreateProduct, canCreateTransaction } from "@/lib/subscription-limits";
import { getUserSubscription } from "@/lib/subscription";

// Check if user can create a product
const userSubscription = await getUserSubscription(userId);
const productCheck = await canCreateProduct(userId, userSubscription.plan);

if (!productCheck.allowed) {
  return { error: productCheck.message };
}
```

### Using API Middleware

```typescript
import { withSubscriptionLimit } from "@/lib/api-subscription-middleware";

// Wrap API handler with subscription limit check
export const POST = withSubscriptionLimit('product')(async (request) => {
  // Handler code here
  // userId is available as (request as any).userId
});
```

### Displaying Usage Summary

```tsx
import UsageSummary from "@/components/subscription/usage-summary";

// In your component
<UsageSummary className="w-full" showUpgradeButton={true} />
```

## Testing

### Test Page

Visit `/dashboard/test-subscription` to run comprehensive tests of the subscription system.

### Manual Testing

1. Create a user with FREE plan
2. Try to create more than 50 products - should be blocked
3. Try to access notifications - should show access denied
4. Upgrade to BASIC plan
5. Verify increased limits and notification access

### Test Functions

```typescript
import { testSubscriptionLimits, testPlanLimits } from "@/lib/test-subscription-limits";

// Test user-specific limits
const userTests = await testSubscriptionLimits(userId);

// Test plan configuration
const planTests = testPlanLimits();
```

## Error Handling

The system provides user-friendly error messages when limits are exceeded:

- Product limit: "Anda telah mencapai batas maksimal X produk untuk [Plan Name]"
- Transaction limit: "Batas transaksi bulanan tercapai untuk paket Anda"
- User limit: "Batas pengguna tercapai untuk paket Anda"
- Contact limit: "Batas kontak tercapai untuk paket Anda"

## Security

- All limits are enforced at both frontend and backend levels
- API middleware prevents bypassing frontend restrictions
- Server-side validation in all creation actions
- Subscription checks are performed on every relevant operation

## Maintenance

### Adding New Limits

1. Update plan configuration in `src/lib/subscription.ts`
2. Add limit check function in `src/lib/subscription-limits.ts`
3. Implement frontend validation in relevant forms
4. Add backend validation in relevant actions
5. Update API middleware if needed

### Modifying Existing Limits

1. Update values in `SUBSCRIPTION_PLANS` object in `src/lib/subscription.ts`
2. Test changes using the test page
3. Verify both frontend and backend enforcement

## Future Enhancements

- Real-time usage monitoring dashboard
- Email notifications when approaching limits
- Automatic plan upgrade suggestions
- Usage analytics and reporting
- Custom plan creation for enterprise customers
