# Subdomain Development Setup Guide

This guide explains how to set up and test the dashboard subdomain routing locally during development.

## Overview

The middleware handles these URL patterns:
- `localhost:3000/dashboard/*` → `dashboard.localhost:3000/*` (redirect)
- `dashboard.localhost:3000/*` → serves dashboard pages (rewrite to `/dashboard/*`)

## Local Development Setup

### Step 1: Update Hosts File

Add the subdomain to your local hosts file to resolve `dashboard.localhost` to your local machine.

#### Windows
1. Open Command Prompt as Administrator
2. Edit the hosts file:
   ```cmd
   notepad C:\Windows\System32\drivers\etc\hosts
   ```
3. Add this line:
   ```
   127.0.0.1 dashboard.localhost
   ```

#### macOS/Linux
1. Open terminal
2. Edit the hosts file:
   ```bash
   sudo nano /etc/hosts
   ```
3. Add this line:
   ```
   127.0.0.1 dashboard.localhost
   ```

### Step 2: Start Development Server

Start your Next.js development server as usual:

```bash
bun dev
# or
npm run dev
# or
yarn dev
```

The server will run on `http://localhost:3000`

### Step 3: Test Subdomain Routing

#### Test Main Domain
- `http://localhost:3000/` - Main site (should work normally)
- `http://localhost:3000/about` - Main site pages (should work normally)

#### Test Dashboard Redirects
- `http://localhost:3000/dashboard` → `http://dashboard.localhost:3000/`
- `http://localhost:3000/dashboard/products` → `http://dashboard.localhost:3000/products`
- `http://localhost:3000/dashboard/sales` → `http://dashboard.localhost:3000/sales`

#### Test Dashboard Subdomain
- `http://dashboard.localhost:3000/` - Dashboard home
- `http://dashboard.localhost:3000/products` - Dashboard products page
- `http://dashboard.localhost:3000/sales` - Dashboard sales page

## Testing Checklist

### ✅ Basic Functionality
- [ ] Main domain loads correctly (`localhost:3000`)
- [ ] Dashboard subdomain resolves (`dashboard.localhost:3000`)
- [ ] Dashboard pages load on subdomain
- [ ] Redirects work from main domain to subdomain

### ✅ URL Patterns
- [ ] `/dashboard` → `dashboard.localhost:3000/`
- [ ] `/dashboard/products` → `dashboard.localhost:3000/products`
- [ ] `/dashboard/sales` → `dashboard.localhost:3000/sales`
- [ ] `/dashboard/settings` → `dashboard.localhost:3000/settings`

### ✅ Edge Cases
- [ ] `/dashboard/dashboard/products` → `dashboard.localhost:3000/products` (removes duplicate)
- [ ] Query parameters preserved in redirects
- [ ] Hash fragments preserved in redirects
- [ ] Static files load correctly on both domains

### ✅ Security Headers
- [ ] Security headers present on main domain
- [ ] Security headers present on subdomain
- [ ] CSP allows subdomain resources

## Debugging

### Check Middleware Logs
The middleware logs all routing decisions to the console:

```bash
# Look for these log messages in your terminal
[Middleware] Redirecting main domain dashboard path: http://dashboard.localhost:3000/products
[Middleware] Rewriting dashboard subdomain: /products -> /dashboard/products
```

### Browser Developer Tools
1. Open Network tab in browser dev tools
2. Navigate to test URLs
3. Check for:
   - 301 redirects (permanent redirects)
   - Correct final URLs
   - No redirect loops

### Common Issues

#### 1. Subdomain Not Resolving
**Problem**: `dashboard.localhost:3000` shows "This site can't be reached"

**Solutions**:
- Check hosts file entry: `127.0.0.1 dashboard.localhost`
- Clear DNS cache:
  - Windows: `ipconfig /flushdns`
  - macOS: `sudo dscacheutil -flushcache`
  - Linux: `sudo systemctl restart systemd-resolved`
- Try different browser or incognito mode

#### 2. Redirect Loops
**Problem**: Browser shows "Too many redirects" error

**Solutions**:
- Check middleware logic for infinite redirects
- Clear browser cache and cookies
- Check for conflicting redirect rules

#### 3. 404 Errors on Subdomain
**Problem**: Dashboard subdomain shows 404 for dashboard pages

**Solutions**:
- Verify middleware rewrite logic
- Check that dashboard pages exist at `/dashboard/*` paths
- Ensure Next.js routing is set up correctly

#### 4. Static Files Not Loading
**Problem**: CSS, JS, or images don't load on subdomain

**Solutions**:
- Check middleware matcher excludes static files
- Verify CSP headers allow subdomain resources
- Use absolute URLs for critical assets

## Advanced Testing

### Test with Different Browsers
- Chrome/Chromium
- Firefox
- Safari (macOS)
- Edge (Windows)

### Test HTTPS Locally (Optional)
For more realistic testing, set up local HTTPS:

1. **Install mkcert**:
   ```bash
   # macOS
   brew install mkcert
   
   # Windows (with Chocolatey)
   choco install mkcert
   
   # Linux
   sudo apt install libnss3-tools
   wget -O mkcert https://github.com/FiloSottile/mkcert/releases/download/v1.4.4/mkcert-v1.4.4-linux-amd64
   chmod +x mkcert
   sudo mv mkcert /usr/local/bin/
   ```

2. **Create certificates**:
   ```bash
   mkcert -install
   mkcert localhost dashboard.localhost
   ```

3. **Update Next.js config**:
   ```javascript
   // next.config.js
   const fs = require('fs');
   
   module.exports = {
     // ... other config
     server: {
       https: {
         key: fs.readFileSync('./localhost+1-key.pem'),
         cert: fs.readFileSync('./localhost+1.pem'),
       },
     },
   };
   ```

### Performance Testing
Test redirect performance:

```bash
# Test redirect speed
curl -w "@curl-format.txt" -o /dev/null -s "http://localhost:3000/dashboard/products"

# Create curl-format.txt:
echo "     time_namelookup:  %{time_namelookup}\n      time_connect:  %{time_connect}\n   time_appconnect:  %{time_appconnect}\n  time_pretransfer:  %{time_pretransfer}\n     time_redirect:  %{time_redirect}\n  time_starttransfer:  %{time_starttransfer}\n                     ----------\n      time_total:  %{time_total}\n" > curl-format.txt
```

## Production Deployment Testing

Before deploying to production, test with production-like setup:

### 1. Build and Start Production Server
```bash
bun run build
bun start
```

### 2. Test with Production Domain
Update hosts file temporarily:
```
127.0.0.1 kivapos.com
127.0.0.1 dashboard.kivapos.com
```

### 3. Test Production URLs
- `http://kivapos.com:3000/dashboard` → `http://dashboard.kivapos.com:3000/`
- `http://dashboard.kivapos.com:3000/products`

## Cleanup

### Remove Hosts File Entries
After testing, remove the test entries from your hosts file:

#### Windows
```cmd
notepad C:\Windows\System32\drivers\etc\hosts
```
Remove the line: `127.0.0.1 dashboard.localhost`

#### macOS/Linux
```bash
sudo nano /etc/hosts
```
Remove the line: `127.0.0.1 dashboard.localhost`

### Clear DNS Cache
```bash
# Windows
ipconfig /flushdns

# macOS
sudo dscacheutil -flushcache

# Linux
sudo systemctl restart systemd-resolved
```

## Troubleshooting Commands

```bash
# Check if subdomain resolves
nslookup dashboard.localhost

# Test HTTP response
curl -I http://dashboard.localhost:3000/

# Test redirect
curl -I http://localhost:3000/dashboard/

# Check hosts file
cat /etc/hosts | grep localhost  # macOS/Linux
type C:\Windows\System32\drivers\etc\hosts | findstr localhost  # Windows
```

This setup allows you to fully test the subdomain routing functionality before deploying to production.
