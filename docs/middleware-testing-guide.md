# Middleware Testing Guide

This guide helps you test the fixed subdomain redirect middleware to ensure it works correctly with authentication flows.

## Fixed Issues

### ✅ **Issue 1: Login Redirect Loop**
**Problem**: After login, users were redirected to `http://dashboard.localhost:3000/login` causing 404 errors.

**Solution**: Updated auth callbacks to redirect to dashboard subdomain with `/summaries` as default route.

### ✅ **Issue 2: Duplicate Dashboard Prefixes**
**Problem**: URLs were showing `dashboard.domain.com/dashboard/settings` instead of `dashboard.domain.com/settings`.

**Solution**: Added logic to detect and remove duplicate `/dashboard` prefixes with 301 redirects.

### ✅ **Issue 3: Environment Support**
**Problem**: Middleware wasn't handling both development and production environments correctly.

**Solution**: Enhanced hostname detection to support both `localhost:3000` and production domains.

## Testing Checklist

### 🧪 **Authentication Flow Testing**

#### Test 1: Login Redirect
1. **Setup**: Ensure you're logged out
2. **Action**: Navigate to `http://localhost:3000/login`
3. **Login**: Enter valid credentials
4. **Expected**: Redirect to `http://dashboard.localhost:3000/summaries`
5. **Status**: ✅ Should work

#### Test 2: Protected Route Access
1. **Setup**: Ensure you're logged out
2. **Action**: Navigate to `http://localhost:3000/dashboard/products`
3. **Expected**: Redirect to `http://localhost:3000/login`
4. **Status**: ✅ Should work

#### Test 3: Auth Route Redirect (Logged In)
1. **Setup**: Ensure you're logged in
2. **Action**: Navigate to `http://localhost:3000/login`
3. **Expected**: Redirect to `http://dashboard.localhost:3000/summaries`
4. **Status**: ✅ Should work

### 🌐 **Subdomain Routing Testing**

#### Test 4: Main Domain Dashboard Redirect
1. **Action**: Navigate to `http://localhost:3000/dashboard/settings`
2. **Expected**: 301 redirect to `http://dashboard.localhost:3000/settings`
3. **Status**: ✅ Should work

#### Test 5: Subdomain Clean URLs
1. **Action**: Navigate to `http://dashboard.localhost:3000/products`
2. **Expected**: Shows dashboard products page (internal rewrite to `/dashboard/products`)
3. **Browser URL**: Should remain `http://dashboard.localhost:3000/products`
4. **Status**: ✅ Should work

#### Test 6: Duplicate Prefix Removal
1. **Action**: Navigate to `http://dashboard.localhost:3000/dashboard/settings`
2. **Expected**: 301 redirect to `http://dashboard.localhost:3000/settings`
3. **Status**: ✅ Should work

#### Test 7: Root Dashboard Route
1. **Action**: Navigate to `http://dashboard.localhost:3000/`
2. **Expected**: Shows dashboard summaries page (internal rewrite to `/dashboard/summaries`)
3. **Status**: ✅ Should work

### 🔒 **Security & Headers Testing**

#### Test 8: Security Headers
1. **Action**: Check response headers for any dashboard request
2. **Expected**: All security headers present (CSP, HSTS, etc.)
3. **Command**: `curl -I http://dashboard.localhost:3000/`
4. **Status**: ✅ Should work

#### Test 9: Static Files
1. **Action**: Load CSS/JS files on dashboard pages
2. **Expected**: All assets load correctly
3. **Status**: ✅ Should work

### 🚫 **Excluded Paths Testing**

#### Test 10: Auth Paths Excluded
1. **Action**: Navigate to `http://localhost:3000/login`
2. **Expected**: No subdomain redirect, normal login page
3. **Status**: ✅ Should work

#### Test 11: API Routes Excluded
1. **Action**: Make request to `http://localhost:3000/api/auth/session`
2. **Expected**: No subdomain redirect, normal API response
3. **Status**: ✅ Should work

## Manual Testing Commands

### Check DNS Resolution
```bash
# Should resolve to 127.0.0.1
nslookup dashboard.localhost
```

### Test HTTP Responses
```bash
# Test main domain redirect
curl -I "http://localhost:3000/dashboard/products"

# Test subdomain rewrite
curl -I "http://dashboard.localhost:3000/products"

# Test duplicate prefix removal
curl -I "http://dashboard.localhost:3000/dashboard/settings"
```

### Check Security Headers
```bash
# Verify security headers are present
curl -I "http://dashboard.localhost:3000/" | grep -E "(X-Frame-Options|Content-Security-Policy|Strict-Transport-Security)"
```

## Browser Testing

### Development Setup
1. **Add to hosts file**:
   ```
   127.0.0.1 dashboard.localhost
   ```

2. **Start development server**:
   ```bash
   bun dev
   ```

3. **Test URLs**:
   - Main site: `http://localhost:3000`
   - Dashboard: `http://dashboard.localhost:3000`

### Expected Behavior

| Input URL | Expected Result | Status |
|-----------|----------------|---------|
| `localhost:3000/login` | Login page (no redirect) | ✅ |
| `localhost:3000/dashboard/products` | → `dashboard.localhost:3000/products` | ✅ |
| `dashboard.localhost:3000/products` | Dashboard products page | ✅ |
| `dashboard.localhost:3000/dashboard/settings` | → `dashboard.localhost:3000/settings` | ✅ |
| `dashboard.localhost:3000/` | Dashboard summaries page | ✅ |

## Production Testing

### DNS Setup Required
```
# Add DNS records:
A dashboard.kivapos.com -> [Your Server IP]
```

### Test URLs
- Main site: `https://kivapos.com`
- Dashboard: `https://dashboard.kivapos.com`

### Expected Production Behavior

| Input URL | Expected Result |
|-----------|----------------|
| `kivapos.com/dashboard/products` | → `dashboard.kivapos.com/products` |
| `dashboard.kivapos.com/products` | Dashboard products page |
| `dashboard.kivapos.com/` | Dashboard summaries page |

## Troubleshooting

### Common Issues

#### 1. "This site can't be reached" for dashboard.localhost
**Solution**: 
- Check hosts file entry: `127.0.0.1 dashboard.localhost`
- Clear DNS cache: `ipconfig /flushdns` (Windows) or `sudo dscacheutil -flushcache` (Mac)

#### 2. Redirect loops
**Solution**:
- Clear browser cache and cookies
- Check middleware logs in terminal
- Verify no conflicting redirects

#### 3. 404 on dashboard pages
**Solution**:
- Ensure dashboard pages exist at `/dashboard/*` paths
- Check middleware rewrite logic
- Verify Next.js routing setup

#### 4. Login redirects to wrong URL
**Solution**:
- Check auth callback configuration
- Verify `getDashboardUrl` function logic
- Test with different browsers

### Debug Commands

```bash
# Check middleware logs
# Look for these patterns in your terminal:
# [Middleware] Redirecting main domain dashboard path: ...
# [Middleware] Rewriting dashboard subdomain: ...

# Test specific scenarios
curl -v "http://localhost:3000/dashboard/products"
curl -v "http://dashboard.localhost:3000/products"
curl -v "http://dashboard.localhost:3000/dashboard/settings"
```

## Success Criteria

✅ **All tests pass**
✅ **No redirect loops**
✅ **Clean URLs on subdomain**
✅ **Proper authentication flow**
✅ **Security headers present**
✅ **Static files load correctly**

The middleware should now handle all authentication flows and URL patterns correctly without the previous issues.
