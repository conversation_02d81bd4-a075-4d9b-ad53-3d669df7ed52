# Cloudflare DNS Configuration for Dashboard Subdomain

This guide explains how to set up the `dashboard.kivapos.com` subdomain using Cloudflare DNS and configure it to work with the Next.js middleware.

## Overview

The setup redirects:
- `kivapos.com/dashboard/*` → `dashboard.kivapos.com/*`
- Removes the `/dashboard` prefix from URLs
- Provides better SEO and user experience
- Maintains proper routing for the Next.js application

## Prerequisites

- Domain registered and using Cloudflare nameservers
- Cloudflare account with DNS management access
- Next.js application deployed (Vercel, custom server, etc.)

## Step 1: Cloudflare DNS Configuration

### Option A: Same Server (Recommended)
If your dashboard runs on the same server as your main site:

1. **Log in to Cloudflare Dashboard**
   - Go to [dash.cloudflare.com](https://dash.cloudflare.com)
   - Select your domain (`kivapos.com`)

2. **Add DNS Record**
   - Go to **DNS** → **Records**
   - Click **Add record**
   - Configure:
     ```
     Type: A
     Name: dashboard
     IPv4 address: [Your server IP]
     Proxy status: Proxied (orange cloud)
     TTL: Auto
     ```

3. **Alternative: CNAME Record**
   If using a hosting service like Vercel:
   ```
   Type: CNAME
   Name: dashboard
   Target: your-app.vercel.app
   Proxy status: Proxied (orange cloud)
   TTL: Auto
   ```

### Option B: Different Server
If your dashboard runs on a different server:

```
Type: A
Name: dashboard
IPv4 address: [Dashboard server IP]
Proxy status: Proxied (orange cloud)
TTL: Auto
```

## Step 2: SSL Certificate Configuration

### Automatic SSL (Recommended)
Cloudflare automatically provisions SSL certificates for subdomains when proxied.

1. **Check SSL Status**
   - Go to **SSL/TLS** → **Overview**
   - Ensure SSL/TLS encryption mode is set to **Full** or **Full (strict)**

2. **Verify Certificate**
   - Go to **SSL/TLS** → **Edge Certificates**
   - Confirm wildcard certificate covers `*.kivapos.com`

### Manual SSL (If needed)
If automatic SSL doesn't work:

1. **Upload Custom Certificate**
   - Go to **SSL/TLS** → **Custom Certificates**
   - Upload wildcard certificate for `*.kivapos.com`

## Step 3: Page Rules (Optional but Recommended)

Create page rules for better control and SEO:

1. **Go to Page Rules**
   - Navigate to **Rules** → **Page Rules**

2. **Create Redirect Rule**
   ```
   URL Pattern: kivapos.com/dashboard*
   Setting: Forwarding URL
   Status Code: 301 - Permanent Redirect
   Destination URL: https://dashboard.kivapos.com$1
   ```

3. **Create HTTPS Redirect**
   ```
   URL Pattern: http://dashboard.kivapos.com/*
   Setting: Always Use HTTPS
   ```

## Step 4: Cloudflare Workers (Advanced)

For more complex routing logic, use Cloudflare Workers:

```javascript
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request))
})

async function handleRequest(request) {
  const url = new URL(request.url)
  const hostname = url.hostname
  
  // Redirect main domain dashboard paths to subdomain
  if (hostname === 'kivapos.com' && url.pathname.startsWith('/dashboard')) {
    const newPath = url.pathname.replace('/dashboard', '') || '/'
    const newUrl = `https://dashboard.kivapos.com${newPath}${url.search}`
    return Response.redirect(newUrl, 301)
  }
  
  // Continue with normal request
  return fetch(request)
}
```

## Step 5: Verification

### DNS Propagation Check
```bash
# Check DNS resolution
nslookup dashboard.kivapos.com

# Check with different DNS servers
nslookup dashboard.kivapos.com *******
nslookup dashboard.kivapos.com *******
```

### SSL Certificate Check
```bash
# Check SSL certificate
openssl s_client -connect dashboard.kivapos.com:443 -servername dashboard.kivapos.com

# Or use online tools
# https://www.ssllabs.com/ssltest/
```

### Functionality Testing
1. **Test Redirects**
   - `https://kivapos.com/dashboard` → `https://dashboard.kivapos.com/`
   - `https://kivapos.com/dashboard/products` → `https://dashboard.kivapos.com/products`

2. **Test Direct Access**
   - `https://dashboard.kivapos.com/` (should work)
   - `https://dashboard.kivapos.com/products` (should work)

3. **Test HTTPS**
   - Ensure all requests redirect to HTTPS
   - Check for mixed content warnings

## Step 6: Deployment Configuration

### Vercel Deployment
If using Vercel, add to `vercel.json`:

```json
{
  "domains": [
    "kivapos.com",
    "dashboard.kivapos.com"
  ],
  "redirects": [
    {
      "source": "/dashboard/:path*",
      "destination": "https://dashboard.kivapos.com/:path*",
      "permanent": true
    }
  ]
}
```

### Custom Server
Ensure your server handles both domains:

```javascript
// Express.js example
app.use((req, res, next) => {
  const hostname = req.get('host');
  
  if (hostname === 'dashboard.kivapos.com') {
    // Handle dashboard subdomain
    req.url = '/dashboard' + req.url;
  }
  
  next();
});
```

## Step 7: SEO Considerations

### Canonical URLs
Add canonical URLs to prevent duplicate content:

```html
<!-- On dashboard pages -->
<link rel="canonical" href="https://dashboard.kivapos.com/current-path" />
```

### Sitemap Updates
Update your sitemap to use subdomain URLs:

```xml
<url>
  <loc>https://dashboard.kivapos.com/</loc>
  <lastmod>2024-01-01</lastmod>
  <priority>0.8</priority>
</url>
```

### Search Console
Add both domains to Google Search Console:
- `https://kivapos.com`
- `https://dashboard.kivapos.com`

## Troubleshooting

### Common Issues

1. **DNS Not Resolving**
   - Check nameservers are pointing to Cloudflare
   - Wait for DNS propagation (up to 48 hours)
   - Clear local DNS cache: `ipconfig /flushdns` (Windows) or `sudo dscacheutil -flushcache` (Mac)

2. **SSL Certificate Errors**
   - Ensure proxy status is enabled (orange cloud)
   - Check SSL/TLS encryption mode
   - Wait for certificate provisioning (up to 24 hours)

3. **Redirect Loops**
   - Check middleware logic for infinite redirects
   - Verify page rules don't conflict
   - Test with different browsers/incognito mode

4. **404 Errors on Subdomain**
   - Verify DNS record points to correct server
   - Check server configuration handles subdomain
   - Ensure middleware is properly deployed

### Debug Commands

```bash
# Check DNS resolution
dig dashboard.kivapos.com

# Check HTTP headers
curl -I https://dashboard.kivapos.com/

# Test redirects
curl -I https://kivapos.com/dashboard/

# Check SSL
curl -vI https://dashboard.kivapos.com/
```

## Performance Optimization

### Cloudflare Settings
1. **Enable Caching**
   - Go to **Caching** → **Configuration**
   - Set appropriate cache levels

2. **Enable Compression**
   - Go to **Speed** → **Optimization**
   - Enable Brotli compression

3. **Enable HTTP/2**
   - Automatically enabled with Cloudflare proxy

### Monitoring
Set up monitoring for:
- DNS resolution time
- SSL certificate expiration
- Redirect response times
- Subdomain availability

## Security Considerations

1. **HSTS Headers**
   ```
   Strict-Transport-Security: max-age=31536000; includeSubDomains
   ```

2. **Content Security Policy**
   Update CSP to include subdomain:
   ```
   Content-Security-Policy: default-src 'self' *.kivapos.com
   ```

3. **CORS Configuration**
   If needed, configure CORS for cross-subdomain requests:
   ```javascript
   app.use(cors({
     origin: ['https://kivapos.com', 'https://dashboard.kivapos.com']
   }));
   ```

## Maintenance

### Regular Checks
- Monitor DNS resolution
- Check SSL certificate expiration
- Verify redirect functionality
- Monitor performance metrics

### Updates
- Update middleware when adding new dashboard routes
- Adjust page rules as needed
- Keep security headers current

This setup provides a professional subdomain structure with proper SEO handling and user experience optimization.
